# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.vscode
.idea
.trae
COMPONENTS_DOCUMENTATION.md

# Added by Task Master AI
# Logs
logs
*.log
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.taskmaster
.roo
.cursor
.roomodes
.windsurfrules
.env.example

.windsurf
.clines
.clines
.clinerules
.vscode
AGENTS.md
COMPONENTS_DOCUMENTATION.md
CLAUDE.md
.github
.promptx

# Task files
tasks.json
tasks/ 