/**
 * TypeScript interfaces for User Usage Data
 * Tracks user activity and daily usage summaries
 */

// ============================================================================
// CORE INTERFACES
// ============================================================================

/**
 * Represents detailed information about a specific usage instance
 */
export interface UsageDetail {
  id: string;
  userId: string;
  activityType: 'worksheet' | 'question' | 'practice';
  resourceId: string;  // ID of the worksheet, question, etc.
  startTime: string;   // ISO timestamp
  endTime?: string;    // ISO timestamp
  duration?: number;   // Duration in seconds
  progress?: number;   // Progress percentage (0-100)
  score?: number;      // Score if applicable
  metadata?: {
    deviceType?: string;
    browser?: string;
    platform?: string;
    lastInteractionTime?: string;
    completionStatus?: 'completed' | 'in_progress' | 'abandoned';
  };
  createdAt: string;
  updatedAt: string;
}

/**
 * Represents aggregated usage data for a specific day
 */
export interface DailySummary {
  id: string;
  userId: string;
  date: string;           // YYYY-MM-DD format
  totalTime: number;      // Total time spent in seconds
  activities: {
    worksheet: number;    // Number of worksheets accessed
    question: number;     // Number of questions attempted
    practice: number;     // Number of practice sessions
  };
  scores: {
    total: number;       // Total score for the day
    average: number;     // Average score per activity
    highest: number;     // Highest score achieved
  };
  progress: {
    completed: number;   // Number of completed activities
    inProgress: number;  // Number of activities in progress
    abandoned: number;   // Number of abandoned activities
  };
  streakCount: number;   // Consecutive days of activity
  createdAt: string;
  updatedAt: string;
}

// ============================================================================
// RESPONSE DTOs
// ============================================================================

export interface IUsageResponse {
  data: UsageDetail;
  message?: string;
}

export interface IDailySummaryResponse {
  data: DailySummary;
  message?: string;
}

export interface IPaginatedUsageResponse {
  data: UsageDetail[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// ============================================================================
// REQUEST DTOs
// ============================================================================

export interface ICreateUsageDto {
  activityType: UsageDetail['activityType'];
  resourceId: string;
  startTime: string;
  metadata?: UsageDetail['metadata'];
}

export interface IUpdateUsageDto {
  endTime?: string;
  duration?: number;
  progress?: number;
  score?: number;
  metadata?: Partial<UsageDetail['metadata']>;
}

export interface IGetUsageQuery {
  userId?: string;
  activityType?: UsageDetail['activityType'];
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
  sortBy?: 'startTime' | 'endTime' | 'duration' | 'score';
  sortOrder?: 'asc' | 'desc';
}

export interface DailyUsage {
  date: string;
  count: number;
}

export interface UsageLimit {
  type: 'worksheet' | 'question_per_worksheet';
  limit: number;
  dailyUsage: DailyUsage[];
}

export interface GetUsageLimitsResponse {
  data: UsageLimit[];
} 