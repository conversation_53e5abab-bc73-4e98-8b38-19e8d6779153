'use server';

import { z } from 'zod';
import { forgotPassword, resetPassword, signUp, signIn } from '@/apis/authApi';
import { EUserRole } from '@/config/enums/user';
import { 
  forgotPasswordSchema, 
  resetPasswordSchema,
  studentSignUpSchema,
  studentSignInSchema,
  AuthActionResult,
  parseFormDataToObject,
  formatZodErrors
} from './auth.schemas';

// ============================================================================
// SERVER ACTIONS
// ============================================================================

/**
 * Server Action for handling forgot password form submissions
 * Validates email input and initiates password reset process
 * 
 * Security Note: Always returns success message to prevent user enumeration
 * 
 * @param prevState - Previous action state (for useFormState)
 * @param formData - Form data containing email
 * @returns Action result with success status and message
 */
export async function forgotPasswordAction(
  prevState: any,
  formData: FormData
): Promise<AuthActionResult> {
  try {
    // Parse FormData to object
    const rawData = parseFormDataToObject(formData);
    
    // Validate input using Zod schema
    const validationResult = forgotPasswordSchema.safeParse(rawData);
    
    if (!validationResult.success) {
      return {
        success: false,
        message: 'Please check your input and try again.',
        errors: formatZodErrors(validationResult.error),
      };
    }
    
    const { email } = validationResult.data;
    await forgotPassword({ email });
    return {
      success: true,
      message: 'Email sent successfully',
    };
    
  } catch (error: any) {
    console.error('Error in forgotPasswordAction:', error);
    return {
      success: true,
      message: 'Email sent successfully',
    };
  }
}

/**
 * Server Action for handling reset password form submissions
 * Validates token and new password, then resets the user's password
 * 
 * @param prevState - Previous action state (for useFormState)
 * @param formData - Form data containing token, password, and confirmPassword
 * @returns Action result with success status and message
 */
export async function resetPasswordAction(
  prevState: any,
  formData: FormData
): Promise<AuthActionResult> {
  try {
    // Parse FormData to object
    const rawData = parseFormDataToObject(formData);
    
    // Validate input using Zod schema
    const validationResult = resetPasswordSchema.safeParse(rawData);
    
    if (!validationResult.success) {
      return {
        success: false,
        message: 'Please check your input and try again.',
        errors: formatZodErrors(validationResult.error),
      };
    }
    
    const { token, password, confirmPassword } = validationResult.data;
    
    // Additional server-side validation: ensure passwords match
    if (password !== confirmPassword) {
      return {
        success: false,
        message: 'Please check your input and try again.',
        errors: { confirmPassword: ["Passwords don't match"] },
      };
    }
    
    // Call the API service function
    const response = await resetPassword({ token,newPassword: password });
    
    if (response.status === 'success') {
      return {
        success: true,
        message: 'Password has been reset successfully. You can now sign in with your new password.',
      };
    } else {
      // Handle error response - message can be string or TValidationError[]
      let errorMessage = 'Failed to reset password. Please try again.';
      
      if (response.message) {
        if (typeof response.message === 'string') {
          errorMessage = response.message;
        } else if (Array.isArray(response.message)) {
          // Extract constraints from TValidationError[]
          errorMessage = response.message.map(err => err.constraints).join(', ') || errorMessage;
        }
      }
      
      return {
        success: false,
        message: errorMessage,
      };
    }
    
  } catch (error: any) {
    console.error('Error in resetPasswordAction:', error);
    
    // Handle different types of errors
    if (error.response?.data?.message) {
      let errorMessage = 'An error occurred while resetting your password.';
      
      const apiMessage = error.response.data.message;
      if (typeof apiMessage === 'string') {
        errorMessage = apiMessage;
      } else if (Array.isArray(apiMessage)) {
        errorMessage = apiMessage.map((err: any) => err.constraints).join(', ') || errorMessage;
      }
      
      return {
        success: false,
        message: errorMessage,
      };
    }
    
    // Default error for token-related issues
    return {
      success: false,
      message: 'Invalid or expired reset token. Please request a new password reset link.',
    };
  }
}

/**
 * Server Action for handling student sign-up form submissions
 * Validates student registration data and creates a new student account
 * 
 * @param prevState - Previous action state (for useFormState)
 * @param formData - Form data containing name, email, password, and confirmPassword
 * @returns Action result with success status and message
 */
export async function studentSignUpAction(
  prevState: any,
  formData: FormData
): Promise<AuthActionResult> {
  try {
    // Parse FormData to object
    const rawData = parseFormDataToObject(formData);
    
    // Validate input using Zod schema
    const validationResult = studentSignUpSchema.safeParse(rawData);
    
    if (!validationResult.success) {
      return {
        success: false,
        message: 'Please check your input and try again.',
        errors: formatZodErrors(validationResult.error),
      };
    }
    
    const { name, email, password } = validationResult.data;
    
    // Call the API service function with STUDENT role
    const response = await signUp({
      name,
      email,
      password,
      role: EUserRole.STUDENT,
    });
    
    if (response.status === 'success') {
      return {
        success: true,
        message: 'Student account created successfully. You can now sign in.',
      };
    } else {
      // Handle error response - message can be string or TValidationError[]
      let errorMessage = 'Failed to create student account. Please try again.';
      
      // Type narrow to ensure we have an error response
      if (response.status === 'error' && response.message) {
        if (typeof response.message === 'string') {
          errorMessage = response.message;
        } else if (Array.isArray(response.message)) {
          // Extract constraints from TValidationError[]
          errorMessage = response.message.map((err: any) => err.constraints).join(', ') || errorMessage;
        }
      }
      
      return {
        success: false,
        message: errorMessage,
      };
    }
    
  } catch (error: any) {
    console.error('Error in studentSignUpAction:', error);
    
    // Handle different types of errors
    if (error.response?.data?.message) {
      let errorMessage = 'An error occurred while creating your student account.';
      
      const apiMessage = error.response.data.message;
      if (typeof apiMessage === 'string') {
        errorMessage = apiMessage;
      } else if (Array.isArray(apiMessage)) {
        errorMessage = apiMessage.map((err: any) => err.constraints).join(', ') || errorMessage;
      }
      
      return {
        success: false,
        message: errorMessage,
      };
    }
    
    return {
      success: false,
      message: 'An unexpected error occurred while creating your student account. Please try again.',
    };
  }
}

/**
 * Server Action for handling student sign-in form submissions
 * Validates credentials and authenticates the student
 * 
 * @param prevState - Previous action state (for useFormState)
 * @param formData - Form data containing email and password
 * @returns Action result with success status, message, and optional access token
 */
export async function studentSignInAction(
  prevState: any,
  formData: FormData
): Promise<AuthActionResult & { accessToken?: string; user?: any }> {
  try {
    // Parse FormData to object
    const rawData = parseFormDataToObject(formData);
    
    // Validate input using Zod schema
    const validationResult = studentSignInSchema.safeParse(rawData);
    
    if (!validationResult.success) {
      return {
        success: false,
        message: 'Please check your input and try again.',
        errors: formatZodErrors(validationResult.error),
      };
    }
    
    const { email, password } = validationResult.data;
    
    // Call the API service function
    const response = await signIn({ email, password });
    
    if (response.status === 'success' && response.data) {
      return {
        success: true,
        message: 'Signed in successfully.',
        accessToken: response.data.accessToken,
        user: response.data.user,
      };
    } else {
      // Handle error response - message can be string or TValidationError[]
      let errorMessage = 'Invalid email or password. Please try again.';
      
      // Type narrow to ensure we have an error response
      if (response.status === 'error' && response.message) {
        if (typeof response.message === 'string') {
          errorMessage = response.message;
        } else if (Array.isArray(response.message)) {
          // Extract constraints from TValidationError[]
          errorMessage = response.message.map((err: any) => err.constraints).join(', ') || errorMessage;
        }
      }
      
      return {
        success: false,
        message: errorMessage,
      };
    }
    
  } catch (error: any) {
    console.error('Error in studentSignInAction:', error);
    
    // Handle different types of errors
    if (error.response?.data?.message) {
      let errorMessage = 'An error occurred during sign in.';
      
      const apiMessage = error.response.data.message;
      if (typeof apiMessage === 'string') {
        errorMessage = apiMessage;
      } else if (Array.isArray(apiMessage)) {
        errorMessage = apiMessage.map((err: any) => err.constraints).join(', ') || errorMessage;
      }
      
      return {
        success: false,
        message: errorMessage,
      };
    }
    
    return {
      success: false,
      message: 'An unexpected error occurred during sign in. Please try again.',
    };
  }
} 