'use server';

import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/config/auth';
import { revalidatePath } from 'next/cache';
import { request } from '@/apis/request';
import { TTransformResponse } from '@/apis/transformResponse';
import { createExam, getAllExamsForUser, IGetAllExamsResponse, assignExam, IAssignExamPayload, IExamAssignmentResponse, getAssignedExams, getUnassignedStudents } from '@/apis/exam';
import { sendInvitation, ISendInvitationPayload, IInvitationResponse } from '@/apis/invitation';
import {
  createExamFromWorksheetSchema,
  CreateExamFromWorksheetInput,
  ICreateExamResponse,
  IExamQuestion,
  IExamSubmissionPayload,
  IExamSubmissionResponse,
  IExamSubmissionWithCompletionResponse,
  IDetailedStudentResult,
  IStudentExamResultForTeacher,
  IExamDetailResponse,
  ITeacherExamsQueryParams,
  IExamResultsQueryParams,
  IExamCompletionData,
  IExamResultsResponse,
  IExamSubmissionAnswer,
  IAssignedExamsResponse
} from '@/types/exam.types';
import { z } from 'zod';
import { getWorksheetDetail } from '@/apis/worksheet';
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import { EUserRole } from '@/config/enums/user';
import { UnassignedStudent } from '@/types/student';

// Temporary in-memory cache for exam results
// In a production environment, this should be replaced with a proper database or cache solution
const examResultsCache = new Map<string, { data: IExamSubmissionResponse; timestamp: number }>();

// Temporary in-memory cache for exam completion data
const examCompletionCache = new Map<string, { data: IExamCompletionData; timestamp: number }>();

// Cache cleanup - remove results older than 1 hour
const CACHE_EXPIRY_MS = 60 * 60 * 1000; // 1 hour
const cleanupCache = () => {
  const now = Date.now();
  for (const [key, value] of examResultsCache.entries()) {
    if (now - value.timestamp > CACHE_EXPIRY_MS) {
      examResultsCache.delete(key);
    }
  }
};

const cleanupCompletionCache = () => {
  const now = Date.now();
  for (const [key, value] of examCompletionCache.entries()) {
    if (now - value.timestamp > CACHE_EXPIRY_MS) {
      examCompletionCache.delete(key);
    }
  }
};

// ============================================================================
// EXAM CREATION
// ============================================================================

/**
 * Converts Question objects from worksheet to IExamQuestion format
 * @param questions - Array of Question objects from worksheet
 * @returns Array of IExamQuestion objects
 */
function convertQuestionsToExamFormat(questions: Question[]): IExamQuestion[] {
  return questions.map(question => ({
    type: question.type,
    content: question.content,
    options: question.options || [],
    answer: question.answer || [],
    image: question.image || undefined,
    explain: question.explain || undefined,
  }));
}

/**
 * Creates an exam from an existing worksheet
 * @param input - Object containing worksheetId, title, and selectedOptions with timeLimit and passingScore
 * @returns The newly created exam data or error response
 */
export async function createExamFromWorksheet(
  input: CreateExamFromWorksheetInput
): Promise<TTransformResponse<ICreateExamResponse>> {
  try {
    // Validate input using Zod schema (without questions first)
    const initialValidation = z.object({
      worksheetId: z.string().min(1),
      title: z.string().min(1),
      selectedOptions: z.array(z.object({
        key: z.string(),
        value: z.string(),
      })).min(2),
    }).safeParse(input);
    
    if (!initialValidation.success) {
      const errors = initialValidation.error.errors.map(err => err.message).join(', ');
      return {
        status: 'error',
        message: `Initial validation failed: ${errors}`
      };
    }

    // Fetch worksheet details to get questions
    const worksheetResponse = await getWorksheetDetail(input.worksheetId);
    
    if (worksheetResponse.status === 'error') {
      return {
        status: 'error',
        message: `Failed to fetch worksheet: ${worksheetResponse.message}`
      };
    }

    // Extract questions from worksheet response
    const worksheetQuestions = worksheetResponse.data?.promptResult?.result || [];
    
    if (worksheetQuestions.length === 0) {
      return {
        status: 'error',
        message: 'Worksheet has no questions. Cannot create exam from empty worksheet.'
      };
    }

    // Convert questions to exam format
    const examQuestions = convertQuestionsToExamFormat(worksheetQuestions);

    // Create the complete input with questions
    const completeInput: CreateExamFromWorksheetInput = {
      ...input,
      questions: examQuestions
    };

    // Validate the complete input with questions
    const validationResult = createExamFromWorksheetSchema.safeParse(completeInput);
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => err.message).join(', ');
      return {
        status: 'error',
        message: `Validation failed: ${errors}`
      };
    }

    const { worksheetId, title, selectedOptions, questions } = validationResult.data;

    // Extract timeLimit and passingScore from selectedOptions
    const timeLimitOption = selectedOptions.find(opt => opt.key === 'timeLimit');
    const passingScoreOption = selectedOptions.find(opt => opt.key === 'passingScore');

    if (!timeLimitOption || !passingScoreOption) {
      return {
        status: 'error',
        message: 'Time limit and passing score are required in selectedOptions'
      };
    }

    // Validate and convert values
    const timeLimit = parseInt(timeLimitOption.value, 10);
    const passingScore = parseInt(passingScoreOption.value, 10);

    if (isNaN(timeLimit) || timeLimit < 1 || timeLimit > 600) {
      return {
        status: 'error',
        message: 'Time limit must be between 1 and 600 minutes'
      };
    }

    if (isNaN(passingScore) || passingScore < 0 || passingScore > 100) {
      return {
        status: 'error',
        message: 'Passing score must be between 0 and 100'
      };
    }

    // Create exam using the API
    const examPayload: CreateExamFromWorksheetInput = {
      worksheetId,
      title,
      description: `Exam created from worksheet: ${title}`,
      selectedOptions,
      questions
    };

    const apiResponse = await createExam(examPayload);

    if (apiResponse.status === 'error') {
      return {
        status: 'error',
        message: apiResponse.message || 'Failed to create exam'
      };
    }

    return {
      status: 'success',
      data: apiResponse.data
    };

  } catch (error) {
    console.error('Error creating exam from worksheet:', error);
    return {
      status: 'error',
      message: error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
}

/**
 * Fetches exam data by ID with role-based response structure
 * @param examId - The ID of the exam to fetch
 * @returns The exam data with role-specific structure:
 *   - For teachers (exam owners): exam details + all student results
 *   - For other users (students): exam details + their specific result (or null)
 */
export async function getExamByIdAction(
  examId: string
): Promise<TTransformResponse<IExamDetailResponse>> {
  try {
    // Validate examId
    if (!examId || typeof examId !== 'string') {
      return {
        status: 'error',
        message: 'Invalid exam ID provided.'
      };
    }

    // Get the session to ensure user is authenticated
    const session = await getServerSession(authOptions);
    if (!session?.user?.accessToken) {
      return {
        status: 'error',
        message: 'Authentication required. Please log in to access the exam.'
      };
    }

    // The backend API automatically returns the appropriate structure based on user role:
    // - Teachers (exam owners) get exam details + results array (all student results)
    // - Other users get exam details + userResult (their specific result or null)
    const response = await request<IExamDetailResponse>({
      url: `/exams/${examId}`,
      options: {
        method: 'GET',
      },
    });

    return response;
  } catch (error: any) {
    console.error('Error fetching exam:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while fetching the exam.'
    };
  }
}

/**
 * Submits exam answers for grading and redirects to results page on success
 * @param examId - The ID of the exam being submitted
 * @param answers - Array of user answers
 * @param timeSpent - Time spent on exam in seconds
 * @returns The exam results with score and detailed breakdown, or redirects on success
 */
export async function submitExamAction(
  examId: string,
  answers: IExamSubmissionAnswer[],
  timeSpent: number
): Promise<TTransformResponse<IExamSubmissionWithCompletionResponse>> {
  try {
    // Get session for user authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return {
        status: 'error',
        message: 'Authentication required.'
      };
    }

    // Validate inputs
    if (!examId || typeof examId !== 'string') {
      return {
        status: 'error',
        message: 'Invalid exam ID provided.'
      };
    }

    if (!Array.isArray(answers)) {
      return {
        status: 'error',
        message: 'Invalid answers format provided.'
      };
    }

    // Validate timeSpent
    if (typeof timeSpent !== 'number' || timeSpent < 0) {
      return {
        status: 'error',
        message: 'Invalid time spent value. Must be a positive number.'
      };
    }

    const payload: IExamSubmissionPayload = { answers, timeSpent };

    const response = await request<IExamSubmissionResponse>({
      url: `/exams/${examId}/submit`,
      options: {
        method: 'POST',
        body: JSON.stringify(payload),
      },
    });

    console.log('submitExamAction response', response);

    // On successful submission, store results and completion data
    if (response.status === 'success' && response.data) {
      // Clean up old cache entries
      cleanupCache();
      cleanupCompletionCache();

      // Get exam details to create completion data
      const examResponse = await getExamByIdAction(examId);

      if (examResponse.status === 'success' && examResponse.data) {
        const examData = examResponse.data;
        const timestamp = new Date();

        // Create completion data
        const completionData: IExamCompletionData = {
          examId,
          examTitle: examData.title,
          submissionId: `${session.user.id}-${examId}-${Date.now()}`,
          submissionTimestamp: timestamp,
          timeSpent,
          timeLimit: examData.selectedOptions?.find((opt: any) => opt.key === 'timeLimit')?.value ?
            parseInt(examData.selectedOptions.find((opt: any) => opt.key === 'timeLimit').value) * 60 : undefined,
          totalQuestions: Array.isArray(examData.questions) ? examData.questions.length : 0,
          answeredQuestions: answers.filter(answer => answer.userAnswer.length > 0).length,
          gradingType: 'auto', // Assuming auto-grading for now
          resultsAvailable: true, // Results are available immediately for auto-graded exams
          studentName: session.user.name,
          score: response.data.totalQuestions > 0 ? Math.round((response.data.score / response.data.totalQuestions) * 100) : 0, // Calculate percentage from response
          passed: response.data.passed,
          completionPercentage: Math.round((answers.filter(answer => answer.userAnswer.length > 0).length / (Array.isArray(examData.questions) ? examData.questions.length : 1)) * 100)
        };

        // Create cache keys
        const resultsCacheKey = `${session.user.id}-${examId}-${Date.now()}`;
        const completionCacheKey = `completion-${session.user.id}-${examId}-${Date.now()}`;

        // Store the results in cache with timestamp
        examResultsCache.set(resultsCacheKey, {
          data: response.data,
          timestamp: Date.now()
        });

        // Store the completion data in cache
        examCompletionCache.set(completionCacheKey, {
          data: completionData,
          timestamp: Date.now()
        });

        // Return response with completion key for redirect
        return {
          status: 'success',
          data: {
            ...response.data,
            completionKey: completionCacheKey
          }
        };
      }

      // Fallback if exam data fetch fails - still store results
      const cacheKey = `${session.user.id}-${examId}-${Date.now()}`;
      examResultsCache.set(cacheKey, {
        data: response.data,
        timestamp: Date.now()
      });

      return response;
    }

    // Return error response if submission failed
    return response;
  } catch (error: any) {
    console.error('Error submitting exam:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while submitting the exam.'
    };
  }
}

/**
 * Retrieves cached exam completion data by completion key
 * @param completionKey - The cache key for the exam completion data
 * @returns The cached exam completion data or error if not found/expired
 */
export async function getExamCompletionDataAction(
  completionKey: string
): Promise<TTransformResponse<IExamCompletionData>> {
  try {
    // Validate completionKey
    if (!completionKey || typeof completionKey !== 'string') {
      return {
        status: 'error',
        message: 'Invalid completion key provided.'
      };
    }

    // Get session for user authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return {
        status: 'error',
        message: 'Authentication required. Please log in to view completion data.'
      };
    }

    // Clean up old cache entries
    cleanupCompletionCache();

    // Check if the completion key exists and belongs to the current user
    const cachedCompletion = examCompletionCache.get(completionKey);
    if (!cachedCompletion) {
      return {
        status: 'error',
        message: 'Completion data not found or has expired. Please try submitting the exam again.'
      };
    }

    // Verify the cache key belongs to the current user (basic security check)
    if (!completionKey.includes(session.user.id)) {
      return {
        status: 'error',
        message: 'Access denied. You can only view your own completion data.'
      };
    }

    // Return the cached completion data
    return {
      status: 'success',
      data: cachedCompletion.data
    };
  } catch (error: any) {
    console.error('Error retrieving exam completion data:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while retrieving completion data.'
    };
  }
}

/**
 * Retrieves exam completion data by exam ID for the current user
 * This replaces the need for completion keys by fetching data directly from the exam detail
 * @param examId - The ID of the exam
 * @returns The exam completion data if the user has completed the exam
 */
export async function getExamCompletionDataByExamIdAction(
  examId: string
): Promise<TTransformResponse<IExamCompletionData>> {
  try {
    // Validate examId
    if (!examId || typeof examId !== 'string') {
      return {
        status: 'error',
        message: 'Invalid exam ID provided.'
      };
    }

    // Get session for user authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return {
        status: 'error',
        message: 'Authentication required. Please log in to view completion data.'
      };
    }

    // Get exam details which includes user result if they've completed it
    const examResponse = await getExamByIdAction(examId);

    if (examResponse.status !== 'success' || !examResponse.data) {
      return {
        status: 'error',
        message: 'Exam not found or access denied.'
      };
    }

    const examData = examResponse.data;

    // Check if this is a student response and if user has completed the exam
    if (!('userResult' in examData) || !examData.userResult) {
      return {
        status: 'error',
        message: 'You have not completed this exam yet. Please submit the exam first.'
      };
    }

    const userResult = examData.userResult;

    // Create completion data from the exam result
    const completionData: IExamCompletionData = {
      examId,
      examTitle: examData.title,
      submissionId: userResult.id || `${session.user.id}-${examId}`,
      submissionTimestamp: new Date(userResult.submittedAt),
      timeSpent: userResult.timeSpent || 0, // Use actual timeSpent from userResult
      timeLimit: examData.selectedOptions?.find((opt: any) => opt.key === 'timeLimit')?.value ?
        parseInt(examData.selectedOptions.find((opt: any) => opt.key === 'timeLimit').value) * 60 : undefined,
      totalQuestions: Array.isArray(examData.questions) ? examData.questions.length : 0,
      answeredQuestions: userResult.total || 0, // Using total as answered questions count
      gradingType: 'auto', // This should come from exam settings
      resultsAvailable: true, // Results are available if userResult exists
      studentName: session.user.name,
      score: userResult.total > 0 ? Math.round((userResult.score / userResult.total) * 100) : 0, // Calculate percentage from score and total
      passed: userResult.total > 0 ? Math.round((userResult.score / userResult.total) * 100) >= (examData.selectedOptions?.find((opt: any) => opt.key === 'passingScore')?.value ?
        parseInt(examData.selectedOptions.find((opt: any) => opt.key === 'passingScore').value) : 70) : false,
      completionPercentage: Math.round((userResult.total / (Array.isArray(examData.questions) ? examData.questions.length : 1)) * 100)
    };

    return {
      status: 'success',
      data: completionData
    };
  } catch (error: any) {
    console.error('Error retrieving exam completion data by exam ID:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while retrieving completion data.'
    };
  }
}

/**
 * Retrieves cached exam results by result key
 * @param resultKey - The cache key for the exam results
 * @returns The cached exam results or error if not found/expired
 */
export async function getCachedExamResultsAction(
  resultKey: string
): Promise<TTransformResponse<IExamSubmissionResponse>> {
  try {
    // Validate resultKey
    if (!resultKey || typeof resultKey !== 'string') {
      return {
        status: 'error',
        message: 'Invalid result key provided.'
      };
    }

    // Get the session to ensure user is authenticated
    const session = await getServerSession(authOptions);
    if (!session?.user?.accessToken) {
      return {
        status: 'error',
        message: 'Authentication required. Please log in to view exam results.'
      };
    }

    // Clean up old cache entries
    cleanupCache();

    // Check if the result key exists and belongs to the current user
    const cachedResult = examResultsCache.get(resultKey);
    if (!cachedResult) {
      return {
        status: 'error',
        message: 'Exam results not found or have expired. Please take the exam again.'
      };
    }

    // Verify the cache key belongs to the current user (basic security check)
    if (!resultKey.startsWith(session.user.id)) {
      return {
        status: 'error',
        message: 'Access denied. You can only view your own exam results.'
      };
    }

    // Return the cached results
    return {
      status: 'success',
      data: cachedResult.data
    };
  } catch (error: any) {
    console.error('Error retrieving cached exam results:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while retrieving exam results.'
    };
  }
}

/**
 * Fetches all exams for the current user with pagination
 * Uses the documented GET /exams endpoint
 * @param params - Query parameters for pagination
 * @returns List of user's exams with pagination info
 */
export async function getTeacherExamsAction(
  params: ITeacherExamsQueryParams
): Promise<TTransformResponse<IGetAllExamsResponse>> {
  try {
    const {
      page = 1,
      limit = 10
    } = params;

    // Validate parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return {
        status: 'error',
        message: 'Invalid pagination parameters. Page must be >= 1 and limit must be between 1 and 100.'
      };
    }

    // Use the documented API endpoint for getting all exams for current user
    const response = await getAllExamsForUser({
      page,
      limit
    });

    return response;
  } catch (error: any) {
    console.error('Error fetching teacher exams:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while fetching your exams.'
    };
  }
}

/**
 * Fetches assigned exams for the current student with pagination
 * Uses the documented GET /exams endpoint which returns assigned exams for students
 * @param params - Query parameters for pagination (page, limit)
 * @returns List of assigned exams for the student with pagination info
 */
export async function getAssignedExamsAction(
  params: { page?: number; limit?: number } = {}
): Promise<TTransformResponse<IAssignedExamsResponse>> {
  try {
    const {
      page = 1,
      limit = 10
    } = params;

    // Validate parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return {
        status: 'error',
        message: 'Invalid pagination parameters. Page must be >= 1 and limit must be between 1 and 100.'
      };
    }

    // Use the documented API endpoint for getting assigned exams for students
    const response = await getAssignedExams({
      page,
      limit
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching assigned exams:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while fetching your assigned exams.'
    };
  }
}

/**
 * Fetches student results for a specific exam
 * Enhanced version with pagination, filtering, and sorting for large data sets
 * @param examId - The ID of the exam to get results for
 * @param params - Query parameters for pagination, filtering, and sorting
 * @returns Student results and statistics for the exam
 */
export async function getExamResultsAction(
  examId: string,
  params: IExamResultsQueryParams
): Promise<TTransformResponse<IExamResultsResponse>> {
  try {
    // Validate examId
    if (!examId || typeof examId !== 'string') {
      return {
        status: 'error',
        message: 'Invalid exam ID provided.'
      };
    }


    // Validate and set default parameters
    const {
      page = 1,
      limit = 20,
      sortBy = 'submittedAt',
      sortOrder = 'desc',
      minScore,
      maxScore,
      passed,
      search = ''
    } = params;

    // Validate parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return {
        status: 'error',
        message: 'Invalid pagination parameters. Page must be >= 1 and limit must be between 1 and 100.'
      };
    }

    if (!['studentName', 'score', 'submittedAt', 'timeSpent'].includes(sortBy)) {
      return {
        status: 'error',
        message: 'Invalid sortBy parameter. Must be one of: studentName, score, submittedAt, timeSpent.'
      };
    }

    if (!['asc', 'desc'].includes(sortOrder)) {
      return {
        status: 'error',
        message: 'Invalid sortOrder parameter. Must be either "asc" or "desc".'
      };
    }

    if (minScore !== undefined && (minScore < 0 || minScore > 100)) {
      return {
        status: 'error',
        message: 'Invalid minScore parameter. Must be between 0 and 100.'
      };
    }

    if (maxScore !== undefined && (maxScore < 0 || maxScore > 100)) {
      return {
        status: 'error',
        message: 'Invalid maxScore parameter. Must be between 0 and 100.'
      };
    }

    if (minScore !== undefined && maxScore !== undefined && minScore > maxScore) {
      return {
        status: 'error',
        message: 'Invalid score range. minScore cannot be greater than maxScore.'
      };
    }

    // Build query parameters
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      sortBy,
      sortOrder,
      ...(minScore !== undefined && { minScore: minScore.toString() }),
      ...(maxScore !== undefined && { maxScore: maxScore.toString() }),
      ...(passed !== undefined && { passed: passed.toString() }),
      ...(search.trim() && { search: search.trim() })
    });
    const response = await request<IExamResultsResponse>({
      url: `/exams/${examId}?${queryParams.toString()}`,
      options: {
        method: 'GET',
      },
    });


    return response;
  } catch (error: any) {
    console.error('Error fetching exam results:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while fetching exam results.'
    };
  }
}

/**
 * Utility function to get exam results summary statistics
 * @param examId - The ID of the exam to get statistics for
 * @returns Summary statistics for the exam
 */
export async function getExamStatisticsAction(
  examId: string
): Promise<TTransformResponse<any>> { // Keeping as 'any' for now as stats structure is not defined
  try {
    // Validate examId
    if (!examId || typeof examId !== 'string') {
      return {
        status: 'error',
        message: 'Invalid exam ID provided.'
      };
    }

    // Get the session to ensure user is authenticated
    const session = await getServerSession(authOptions);
    if (!session?.user?.accessToken) {
      return {
        status: 'error',
        message: 'Authentication required. Please log in to view exam statistics.'
      };
    }

    const response = await request<any>({ // Keeping as 'any' for now
      url: `/admin/exams/${examId}/statistics`,
      options: {
        method: 'GET',
      },
    });

    return response;
  } catch (error: any) {
    console.error('Error fetching exam statistics:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while fetching exam statistics.'
    };
  }
}

/**
 * Utility function to export exam results as CSV
 * @param examId - The ID of the exam to export results for
 * @param params - Optional filtering parameters
 * @returns CSV data or download URL
 */
export async function exportExamResultsAction(
  examId: string,
  params: Omit<IExamResultsQueryParams, 'page' | 'limit'>
): Promise<TTransformResponse<{ downloadUrl: string; filename: string }>> {
  try {
    // Validate examId
    if (!examId || typeof examId !== 'string') {
      return {
        status: 'error',
        message: 'Invalid exam ID provided.'
      };
    }

    // Get the session to ensure user is authenticated
    const session = await getServerSession(authOptions);
    if (!session?.user?.accessToken) {
      return {
        status: 'error',
        message: 'Authentication required. Please log in to export exam results.'
      };
    }

    // Build query parameters (excluding pagination)
    const queryParams = new URLSearchParams({
      ...(params.sortBy && { sortBy: params.sortBy }),
      ...(params.sortOrder && { sortOrder: params.sortOrder }),
      ...(params.minScore !== undefined && { minScore: params.minScore.toString() }),
      ...(params.maxScore !== undefined && { maxScore: params.maxScore.toString() }),
      ...(params.passed !== undefined && { passed: params.passed.toString() }),
      ...(params.search?.trim() && { search: params.search.trim() })
    });

    const response = await request<{ downloadUrl: string; filename: string }>({
      url: `/admin/exams/${examId}/export?${queryParams.toString()}`,
      options: {
        method: 'GET',
      },
    });

    return response;
  } catch (error: any) {
    console.error('Error exporting exam results:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while exporting exam results.'
    };
  }
}

// ============================================================================
// DETAILED STUDENT RESULT ACTIONS
// ============================================================================

/**
 * Fetches detailed results for a specific student's exam submission
 * @param examId - The ID of the exam
 * @param studentId - The ID of the student
 * @returns Detailed student result with question-by-question breakdown
 */
export async function getDetailedStudentResultAction(
  examId: string,
  studentId: string
): Promise<TTransformResponse<IStudentExamResultForTeacher>> {
  try {
    // Validate inputs
    if (!examId || typeof examId !== 'string') {
      return {
        status: 'error',
        message: 'Invalid exam ID provided.'
      };
    }

    if (!studentId || typeof studentId !== 'string') {
      return {
        status: 'error',
        message: 'Invalid student ID provided.'
      };
    }

    // Get the session to ensure user is authenticated
    const session = await getServerSession(authOptions);
    if (!session?.user?.accessToken) {
      return {
        status: 'error',
        message: 'Authentication required. Please log in to view detailed student results.'
      };
    }

    const response = await request<IStudentExamResultForTeacher>({
      url: `/exams/${examId}/results/${studentId}`,
      options: {
        method: 'GET',
      },
    });

    return response;
  } catch (error: any) {
    console.error('Error fetching detailed student result:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while fetching detailed student results.'
    };
  }
}

// ============================================================================
// EXAM ASSIGNMENT ACTIONS
// ============================================================================

/**
 * Server action to assign an exam to students
 * @param examId - The ID of the exam to assign
 * @param studentIds - Array of student IDs to assign the exam to
 * @returns Promise with array of created exam assignments
 */
export async function assignExamAction(
  examId: string,
  studentIds: string[]
): Promise<TTransformResponse<IExamAssignmentResponse[]>> {
  try {
    // Get the current user's session for authentication
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return {
        status: 'error',
        message: 'Authentication required. Please sign in to assign exams.'
      };
    }

    // Validate input
    if (!examId || !studentIds || studentIds.length === 0) {
      return {
        status: 'error',
        message: 'Exam ID and at least one student ID are required.'
      };
    }

    // Call the API function
    const response = await assignExam(examId, { studentIds });

    if (response.status === 'success') {
      // Revalidate relevant paths
      revalidatePath('/admin/exam-results');
      revalidatePath('/manage-worksheet');
    }

    return response;
  } catch (error: any) {
    console.error('Error in assignExamAction:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while assigning the exam.'
    };
  }
}

/**
 * Server action to send an invitation to a new student
 * @param email - Email address of the student to invite
 * @param schoolId - ID of the school to invite the student to
 * @returns Promise with the created invitation response
 */
export async function sendStudentInvitationAction(
  email: string,
  schoolId: string
): Promise<TTransformResponse<IInvitationResponse>> {
  try {
    // Get the current user's session for authentication
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return {
        status: 'error',
        message: 'Authentication required. Please sign in to send invitations.'
      };
    }

    // Validate input
    if (!email || !schoolId) {
      return {
        status: 'error',
        message: 'Email and school ID are required.'
      };
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return {
        status: 'error',
        message: 'Please enter a valid email address.'
      };
    }

    // Call the API function
    const response = await sendInvitation({
      email,
      role: EUserRole.STUDENT,
      schoolId
    });

    return response;
  } catch (error: any) {
    console.error('Error in sendStudentInvitationAction:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while sending the invitation.'
    };
  }
}

/**
 * Server action to get unassigned students for a specific exam
 * @param examId - The ID of the exam
 * @param schoolId - Optional school ID filter
 * @returns Promise with array of unassigned students
 */
export async function getUnassignedStudentsAction(
  examId: string,
  schoolId?: string
): Promise<TTransformResponse<UnassignedStudent[]>> {
  try {
    // Get the current user's session for authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return {
        status: 'error',
        message: 'Authentication required. Please sign in to view unassigned students.'
      };
    }

    // For non-admin users, use their school ID if no schoolId is provided
    let effectiveSchoolId = schoolId;
    if (!effectiveSchoolId && session.user.role !== EUserRole.ADMIN) {
      effectiveSchoolId = session.user.schoolId || undefined;
    }

    // Call the API function
    const response = await getUnassignedStudents(examId, effectiveSchoolId);
    console.log('getUnassignedStudentsAction response', response);
    return response;
  } catch (error: any) {
    console.error('Error in getUnassignedStudentsAction:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while fetching unassigned students.'
    };
  }
}
