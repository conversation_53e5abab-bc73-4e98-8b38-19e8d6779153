'use server';

import { validateInvitation, acceptInvitation, sendInvitationWithExam, IValidateInvitationResponse, IAcceptInvitationResponse } from '@/apis/invitation';
import { IInvitationWithExamResponse, InvitationUserRole } from '@/types/invitation.types';
import { TTransformResponse } from '@/apis/transformResponse';

// ============================================================================
// SERVER ACTIONS FOR INVITATION FLOW
// ============================================================================

/**
 * Server action to validate an invitation token
 * @param token - The invitation token to validate
 * @returns Promise with the invitation validation response
 */
export async function validateInvitationAction(
  token: string
): Promise<TTransformResponse<IValidateInvitationResponse>> {
  try {
    if (!token) {
      return {
        status: 'error',
        message: 'Invitation token is required.',
      };
    }

    const response = await validateInvitation(token);
    return response;
  } catch (error: any) {
    console.error('Error in validateInvitationAction:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while validating the invitation.',
    };
  }
}

/**
 * Server action to accept an invitation and complete user registration
 * @param token - The invitation token
 * @param name - User's full name
 * @param password - User's password
 * @param confirmPassword - Password confirmation
 * @returns Promise with the invitation acceptance response
 */
export async function acceptInvitationAction(
  token: string,
  name: string,
  password: string,
  confirmPassword: string
): Promise<TTransformResponse<IAcceptInvitationResponse>> {
  try {
    // Validate input
    if (!token || !name || !password || !confirmPassword) {
      return {
        status: 'error',
        message: 'Token, name, password, and password confirmation are required.',
      };
    }

    // Validate name length
    if (name.trim().length === 0 || name.length > 100) {
      return {
        status: 'error',
        message: 'Name must be between 1 and 100 characters.',
      };
    }

    // Validate password length
    if (password.length < 6) {
      return {
        status: 'error',
        message: 'Password must be at least 6 characters long.',
      };
    }

    // Validate password confirmation
    if (password !== confirmPassword) {
      return {
        status: 'error',
        message: 'Passwords do not match.',
      };
    }

    const response = await acceptInvitation({
      token,
      name: name.trim(),
      password,
    });

    return response;
  } catch (error: any) {
    console.error('Error in acceptInvitationAction:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while accepting the invitation.',
    };
  }
}

/**
 * Server action to send an invitation with exam assignment
 * @param email - Student's email address
 * @param schoolId - School UUID
 * @param examIds - Array of exam UUIDs to assign
 * @returns Promise with the invitation and assignment response
 */
export async function sendInvitationWithExamAction(
  email: string,
  schoolId: string,
  examIds: string[]
): Promise<TTransformResponse<IInvitationWithExamResponse>> {
  try {
    // Validate input
    if (!email || !schoolId || !examIds || examIds.length === 0) {
      return {
        status: 'error',
        message: 'Email, school ID, and at least one exam ID are required.'
      };
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return {
        status: 'error',
        message: 'Please enter a valid email address.'
      };
    }

    // Call the API function - backend handles all permission checks
    const response = await sendInvitationWithExam({
      email,
      schoolId,
      role: 'student', // Fixed: use lowercase 'student' to match API specification
      examIds
    });

    return response;
  } catch (error: any) {
    console.error('Error in sendInvitationWithExamAction:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while sending the invitation with exam assignment.'
    };
  }
}
