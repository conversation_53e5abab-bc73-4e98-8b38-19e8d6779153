'use server';

import { getServerSession } from 'next-auth';
import { authOptions } from '@/config/auth';
import { revalidatePath } from 'next/cache';
import { usageApi } from '@/apis/usageApi';
import { TTransformResponse } from '@/apis/transformResponse';
import { IDailySummary, IUsageLimit } from '@/types/usage';

/**
 * Server action to fetch user's usage limits for worksheets and questions.
 *
 * @returns Promise<TTransformResponse<IUsageLimit[]>> Transformed response containing array of usage limits.
 */
export async function handleGetUsageLimitsAction(): Promise<TTransformResponse<IUsageLimit[]>> {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return {
        status: 'error',
        message: 'Authentication required to fetch usage limits.',
      };
    }
    // Fetch usage summary from API
    const dailySummary: IDailySummary = await usageApi.getDailySummary(
      session.user.accessToken || '',
    );

    const usageLimits: IUsageLimit[] = [
      dailySummary.maxWorksheets,
      dailySummary.maxQuestionsPerWorksheet,
    ];

    // Revalidate the usage summary page to ensure fresh data
    revalidatePath('/subscription/me');

    return {
      status: 'success',
      data: usageLimits,
    };
  } catch (error: any) {
    console.error('Server action handleGetUsageLimitsAction error:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while fetching usage limits.',
    };
  }
} 