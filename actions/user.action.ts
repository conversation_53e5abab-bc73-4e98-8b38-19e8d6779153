'use server';

import { revalidatePath } from 'next/cache';
import {
  createUser as apiCreateUser,
  updateUser as apiUpdateUser,
  deleteUser as apiDeleteUser,
  getAllUsers as apiGetAllUsers,
  getStudentList as apiGetStudentList,
  getUserById,
  getStudentDetailById as apiGetStudentDetailById,
  ICreateUserPayload,
  IUpdateUserPayload,
  IUserResponse,
  IStudentDetailResponse,
} from '@/apis/userApi';
import { signUp as apiSignUp, ISignUpPayload } from '@/apis/authApi';
import { TTransformResponse, TSuccess, TApiError } from '@/apis/transformResponse';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/config/auth';
import { User } from 'next-auth';
import { EUserRole } from '@/config/enums/user';
import { autoCreateSchoolForUser } from '@/actions/school.action';
import { Session } from 'next-auth';

// Define specific success and error types for profile update action
type UpdateProfileSuccess = TSuccess<IUserResponse> & {
  updatedUser: IUserResponse;
  message?: string; // Allow an optional success message
};
type UpdateProfileError = TApiError;
type UpdateProfileResult = UpdateProfileSuccess | UpdateProfileError;

export async function updateUserProfileAction(
  userId: string,
  payload: IUpdateUserPayload
): Promise<UpdateProfileResult> {
  // Ensure this action is called by an authenticated user if necessary
  // For profile updates, the userId should match the logged-in user's ID.
  const session = await getServerSession(authOptions);
  if (!session?.user?.id || session.user.id !== userId) {
    return { status: 'error', message: 'Unauthorized or user ID mismatch.' } as UpdateProfileError;
  }

  try {
    const response = await apiUpdateUser(userId, payload);

    if (response.status === 'success' && response.data) {
      revalidatePath('/profile'); // Revalidate the profile page to show updated data
      return {
        status: 'success',
        data: response.data,
        updatedUser: response.data, // Pass back the updated user for client-side session handling
        message: (response as TSuccess<IUserResponse> & { message?: string }).message || 'Profile updated successfully.', // Access message if available
      } as UpdateProfileSuccess;
    } else if (response.status === 'error') {
      return {
        status: 'error',
        message: response.message || 'Failed to update profile via action.',
      } as UpdateProfileError;
    } else {
      // Should not happen if apiUpdateUser always returns TTransformResponse
      return {
        status: 'error',
        message: 'Unknown error structure from API.',
      } as UpdateProfileError;
    }
  } catch (error: any) {
    console.error('Server action updateUserProfileAction error:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred in the server action.',
    };
  }
}

export async function getUpdatedSessionUser(): Promise<Session['user'] | null> {
    const session = await getServerSession(authOptions);
    return session?.user || null;
}

/**
 * Refreshes user data for JWT callback - fetches latest user information by ID
 * This function is designed to be called from NextAuth JWT callback
 * @param userId - The user ID to fetch data for
 * @returns User data or null if not found
 */
export async function refreshUserDataForJWT(userId: string): Promise<IUserResponse | null> {
  try {
    const response = await getUserById(userId);

    if (response.status === 'success' && response.data) {
      return response.data;
    } else {
      return null;
    }
  } catch (error: any) {
    return null;
  }
}

// Action to get all users
export async function handleGetAllUsersAction(
  schoolId?: string,
  role?: EUserRole
): Promise<TTransformResponse<IUserResponse[]>> {

  try {
    const response = await apiGetAllUsers(schoolId, role);
    return response;
  } catch (error: any) {
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while fetching users.',
    };
  }
}

type DeleteUserResult = TSuccess<{ id: string }> | TApiError; // Or TSuccess<void> if no data is returned

export async function handleGetUserByIdAction(userId: string): Promise<TTransformResponse<IUserResponse>> {
  try {
    const response = await getUserById(userId);
    return response;
  } catch (error: any) {
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while fetching user by ID.',
    };
  }
}

export async function handleUpdateUserAction(
  userId: string,
  payload: IUpdateUserPayload
): Promise<TTransformResponse<IUserResponse>> {
  try {
    const response = await apiUpdateUser(userId, payload);
    
    if (response.status === 'success') {
      revalidatePath('/users-management');
      revalidatePath('/teacher-management');
    }
    
    return response;
  } catch (error: any) {
    console.error('Server action handleUpdateUserAction error:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while updating user.',
    };
  }
}

export async function handleCreateUserAction(
  payload: ICreateUserPayload
): Promise<TTransformResponse<IUserResponse>> {
  try {
    const response = await apiCreateUser(payload);

    if (response.status === 'success') {
      revalidatePath('/users-management');
      revalidatePath('/teacher-management');
      return {
        status: 'success',
        data: response.data,
        message: 'User created successfully.',
      } as TTransformResponse<IUserResponse>;
    } else {
      return {
        status: 'error',
        message: response.message || 'Failed to create user.',
      } as TTransformResponse<IUserResponse>;
    }
  } catch (error: any) {
    console.error('Server action handleCreateUserAction error:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while creating user.',
    };
  }
}

// Public signup action for independent teachers
export async function signUpAction(
  formData: FormData
): Promise<TTransformResponse<IUserResponse>> {
  try {
    const name = formData.get('name') as string;
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;

    // Validate required fields
    if (!name || !email || !password) {
      return {
        status: 'error',
        message: 'All fields are required.',
      };
    }

    // Create payload for independent teacher signup
    const payload: ISignUpPayload = {
      name,
      email,
      password,
      role: EUserRole.INDEPENDENT_TEACHER,
    };
    const userResponse = await apiSignUp(payload);

    if (userResponse.status !== 'success' || !userResponse.data) {
      return {
        status: 'error',
        message: (userResponse as TApiError).message || 'Failed to create account.',
      } as TTransformResponse<IUserResponse>;
    }
    // Step 2: Create school for INDEPENDENT_TEACHER users
    try {
      console.log(`[signUpAction] Creating school for INDEPENDENT_TEACHER user: ${email}`);

      const schoolCreationResult = await autoCreateSchoolForUser({
        id: userResponse.data.id,
        name: userResponse.data.name,
        email: userResponse.data.email,
      });

      if (schoolCreationResult.status === 'success') {
        // Return user data - the school association will be available when they sign in
        return {
          status: 'success',
          data: userResponse.data as any,
          message: 'Account and school created successfully.',
        } as TTransformResponse<IUserResponse>;
      } else {
       
        return {
          status: 'success',
          data: userResponse.data as any,
          message: 'Account created successfully.',
        } as TTransformResponse<IUserResponse>;
      }
    } catch (schoolError: any) {
      return {
        status: 'success',
        data: userResponse.data as any,
        message: 'Account created successfully.',
      } as TTransformResponse<IUserResponse>;
    }

  } catch (error: any) {
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while creating account.',
    };
  }
}

export async function handleDeleteUserAction(userId: string): Promise<DeleteUserResult> {
  try {
    const response = await apiDeleteUser(userId);
    if (response.status === 'success') {
      revalidatePath('/users-management');
      revalidatePath('/teacher-management');
      return { status: 'success', data: { id: userId } };
    }
    return { status: 'error', message: response.message || 'Failed to delete user' };
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected error occurred' };
  }
}

export async function handleDeleteStudentAction(userId: string): Promise<DeleteUserResult> {
  try {
    const response = await apiDeleteUser(userId);
    if (response.status === 'success') {
      revalidatePath('/student-management');
      return { status: 'success', data: { id: userId } };
    }
    return { status: 'error', message: response.message || 'Failed to delete user' };
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected error occurred' };
  }
}

export async function handleGetStudentListAction(schoolId?: string): Promise<TTransformResponse<IUserResponse[]>> {
  try {
    const response = await apiGetStudentList(schoolId);
    return response;
  } catch (error: any) {
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while fetching students.',
    };
  }
}

/**
 * Server action to get detailed information about a specific student
 * @param studentId - The ID of the student to fetch details for
 * @returns Student details with exam results
 */
export async function handleGetStudentDetailByIdAction(studentId: string): Promise<TTransformResponse<IStudentDetailResponse>> {
  try {
    // Validate studentId
    if (!studentId) {
      return { status: 'error', message: 'Student ID is required.' };
    }

    // Call the API function
    const response = await apiGetStudentDetailById(studentId);
    
    // Handle redirect errors for Next.js (memory from rules)
    if (response.status === 'error') {
      return response;
    }

    return response;
  } catch (error: any) {
    // Check for NEXT_REDIRECT error and re-throw
    if (error.digest?.startsWith('NEXT_REDIRECT')) {
      throw error;
    }

    console.error('Error in handleGetStudentDetailByIdAction:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while fetching student details.',
    };
  }
}
