import { TTransformResponse } from './transformResponse';

export interface IDocumentUploadPayload {
  files: File[];
  title?: string;
  description?: string;
  category?: string;
}

export interface IDocumentUploadResult {
  documentId: string;
  title: string;
  description: string;
  fileName: string;
  mimeType: string;
  uploadedAt: string;
  category: string;
}

export interface IDocumentUploadResponse {
  count: number;
  results: IDocumentUploadResult[];
}

/**
 * Upload documents for processing
 * Corresponds to: POST /documents/upload
 * @param payload - The document upload data including files and metadata
 * @param accessToken - The access token for authentication
 * @returns The upload response with document details
 */
export async function uploadDocuments(
  payload: IDocumentUploadPayload,
  accessToken?: string
): Promise<TTransformResponse<IDocumentUploadResponse>> {
  try {
    const baseUrl = process.env.API_URL || process.env.NEXT_PUBLIC_API_URL || '';
    const endpoint = `${baseUrl}/documents/upload`;

    // Create FormData for multipart/form-data
    const formData = new FormData();
    
    // Add files
    payload.files.forEach((file) => {
      formData.append('files', file);
    });

    // Add optional metadata
    if (payload.title) {
      formData.append('title', payload.title);
    }
    if (payload.description) {
      formData.append('description', payload.description);
    }
    if (payload.category) {
      formData.append('category', payload.category);
    }

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        ...(accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}),
        // Don't set Content-Type for FormData, let the browser set it with boundary
      },
      body: formData,
    });

    const result = await response.json();

    if (response.ok) {
      return {
        status: 'success',
        data: result.data,
      };
    } else {
      return {
        status: 'error',
        message: result.message || 'Failed to upload documents',
        statusCode: response.status,
      };
    }
  } catch (error: any) {
    console.error('Error uploading documents:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while uploading documents.',
    };
  }
} 