import { EAPIEndpoint } from "@/@types/enums/api";
import { request } from "./request";
import { TTransformResponse } from "./transformResponse";
import {
  CreateExamFromWorksheetInput,
  IExamDetailResponse,
  IExamSubmissionPayload,
  IExamSubmissionResponse,
  IAssignedExamsResponse
} from "@/types/exam.types";
import { UnassignedStudent } from "@/types/student";

// ============================================================================
// EXAM API INTERFACES
// ============================================================================

/**
 * API response interface for exam creation - matches backend API structure
 */
export interface IExamApiResponse {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  userId: string;
  worksheetId: string;
  title: string;
  description: string | null;
  /**
   * JSONB field containing exam settings like timeLimit, passingScore, etc.
   * Structure: Array<{key: string, value: string}>
   */
  selectedOptions: any;
  status: 'in_progress' | 'completed' | 'cancelled';
  /**
   * JSONB field containing the exam questions array
   * Structure: Array<Question> where Question contains type, content, options, answer, etc.
   */
  questions: any;
  maxAttempts: number;
  schoolId: string | null;
  // Statistics fields (available for teachers/admins)
  totalSubmissions?: number;
  averageScore?: number;
  stats?: {
    totalSubmissions: number;
    passRate: number;
    averageScore: number;
    highestScore: number;
    lowestScore: number;
    averageTimeSpent: number;
  };
}

/**
 * Paginated response interface for getting all exams for current user
 */
export interface IGetAllExamsResponse {
  items: IExamApiResponse[];
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

/**
 * Query parameters for getting all exams for current user
 */
export interface IGetAllExamsParams {
  page?: number;
  limit?: number;
}

// ============================================================================
// EXAM API FUNCTIONS
// ============================================================================

/**
 * Creates a new exam from worksheet data
 * @param examData - The exam creation payload
 * @returns Promise with the created exam response
 */
export const createExam = async (
  examData: CreateExamFromWorksheetInput
): Promise<TTransformResponse<IExamApiResponse>> => {
  const url = EAPIEndpoint.EXAMS;
  return request<IExamApiResponse>({
    url,
    options: {
      method: 'POST',
      body: JSON.stringify(examData)
    }
  });
};

/**
 * Gets all exams for the current user with pagination
 * @param params - Query parameters for pagination
 * @returns Promise with paginated list of user's exams
 */
export const getAllExamsForUser = async (
  params: IGetAllExamsParams = {}
): Promise<TTransformResponse<IGetAllExamsResponse>> => {
  const { page = 1, limit = 10 } = params;

  const queryParams = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString()
  });

  const url = `${EAPIEndpoint.EXAMS}?${queryParams.toString()}`;
  return request<IGetAllExamsResponse>({
    url,
    options: {
      method: 'GET'
    }
  });
};

/**
 * Gets assigned exams for the current student with pagination
 * @param params - Query parameters for pagination (page, limit)
 * @returns Promise with paginated list of assigned exams for student
 */
export const getAssignedExams = async (
  params: { page?: number; limit?: number } = {}
): Promise<TTransformResponse<IAssignedExamsResponse>> => {
  const { page = 1, limit = 10 } = params;

  const queryParams = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString()
  });

  const url = `${EAPIEndpoint.EXAMS}?${queryParams.toString()}`;
  return request<IAssignedExamsResponse>({
    url,
    options: {
      method: 'GET'
    }
  });
};

/**
 * Fetches exam data by ID with role-based response structure
 * @param examId - The ID of the exam to fetch
 * @returns Promise with exam details (role-specific structure)
 */
export const getExamById = async (
  examId: string
): Promise<TTransformResponse<IExamDetailResponse>> => {
  const url = `${EAPIEndpoint.EXAMS}/${examId}`;
  return request<IExamDetailResponse>({
    url,
    options: {
      method: 'GET'
    }
  });
};

/**
 * Submits exam answers for grading
 * @param examId - The ID of the exam being submitted
 * @param payload - The submission payload containing answers
 * @returns Promise with exam results and score breakdown
 */
export const submitExam = async (
  examId: string,
  payload: IExamSubmissionPayload
): Promise<TTransformResponse<IExamSubmissionResponse>> => {
  const url = `${EAPIEndpoint.EXAMS}/${examId}/submit`;
  return request<IExamSubmissionResponse>({
    url,
    options: {
      method: 'POST',
      body: JSON.stringify(payload)
    }
  });
};

/**
 * Fetches exams by worksheet ID
 * @param worksheetId - The ID of the worksheet
 * @returns Promise with list of exams for the worksheet
 */
export const getExamsByWorksheet = async (
  worksheetId: string
): Promise<TTransformResponse<IExamApiResponse[]>> => {
  const url = `${EAPIEndpoint.EXAMS}/by-worksheet/${worksheetId}`;
  return request<IExamApiResponse[]>({
    url,
    options: {
      method: 'GET'
    }
  });
};

// ============================================================================
// EXAM ASSIGNMENT API INTERFACES
// ============================================================================

/**
 * Request payload for assigning an exam to students
 */
export interface IAssignExamPayload {
  studentIds: string[];
}

/**
 * Response interface for exam assignment - matches backend ExamAssignment entity
 */
export interface IExamAssignmentResponse {
  id: string;
  score: number;
  status: 'assigned' | 'in_progress' | 'completed';
  feedback: string | null;
  assignedAt: string;
  startedAt: string | null;
  completedAt: string | null;
  examId: string;
  studentId: string;
  createdAt: string;
  updatedAt: string;
}

// ============================================================================
// EXAM ASSIGNMENT API FUNCTIONS
// ============================================================================

/**
 * Assigns an exam to a list of students (Server-side only)
 * @param examId - The ID of the exam to assign
 * @param payload - The assignment payload containing student IDs
 * @returns Promise with array of created exam assignments
 */
export const assignExam = async (
  examId: string,
  payload: IAssignExamPayload
): Promise<TTransformResponse<IExamAssignmentResponse[]>> => {
  const url = `${EAPIEndpoint.EXAMS}/${examId}/assign`;
  return request<IExamAssignmentResponse[]>({
    url,
    options: {
      method: 'POST',
      body: JSON.stringify(payload)
    }
  });
};

/**
 * Gets unassigned students for a specific exam
 * @param examId - The ID of the exam
 * @param schoolId - Optional school ID filter
 * @returns Promise with list of unassigned students for the exam
 */
export const getUnassignedStudents = async (
  examId: string,
  schoolId?: string
): Promise<TTransformResponse<UnassignedStudent[]>> => {
  const params = new URLSearchParams();
  if (schoolId) {
    params.append('schoolId', schoolId);
  }

  let url = `${EAPIEndpoint.EXAMS}/${examId}/unassigned-students`;
  console.log('url', url);
  const queryString = params.toString();
  if (queryString) {
    url += `?${queryString}`;
  }

  return request<UnassignedStudent[]>({
    url,
    options: {
      method: 'GET'
    }
  });
};


