import { EAPIEndpoint } from "@/@types/enums/api";
import { request } from "./request";
import { TTransformResponse } from "./transformResponse";
import { 
  ISendInvitationWithExamPayload, 
  IInvitationWithExamResponse,
  InvitationUserRole
} from "@/types/invitation.types";

// ============================================================================
// INVITATION API INTERFACES
// ============================================================================

/**
 * Request payload for sending an invitation
 */
export interface ISendInvitationPayload {
  email: string;
  role: string;
  schoolId: string;
}

/**
 * Response interface for invitation - matches backend Invitation entity
 */
export interface IInvitationResponse {
  id: string;
  email: string;
  role: string;
  schoolId: string;
  token: string;
  expiresAt: string;
  isUsed: boolean;
  createdAt: string;
  updatedAt: string;
  userId?: string; // New field for linking to pre-registered user
}

/**
 * Request payload for accepting an invitation
 */
export interface IAcceptInvitationPayload {
  token: string;
  name: string;
  password: string;
}

/**
 * Response interface for accepting invitation - returns access token and user data
 */
export interface IAcceptInvitationResponse {
  accessToken: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
    schoolId: string;
    school: {
      id: string;
      name: string;
    };
  };
}

/**
 * Response interface for validating invitation token
 */
export interface IValidateInvitationResponse {
  id: string;
  email: string;
  role: string;
  schoolId: string;
  token: string;
  expiresAt: string;
  isUsed: boolean;
  createdAt: string;
  updatedAt: string;
  userId?: string;
  user?: {
    id: string;
    email: string;
    role: string;
    schoolId: string;
    status: 'invited' | 'active';
  };
  school: {
    id: string;
    name: string;
  };
}

// ============================================================================
// INVITATION API FUNCTIONS
// ============================================================================

/**
 * Sends an email invitation to a new user to join a school (Server-side only)
 * Now pre-registers users during invitation creation
 * @param payload - The invitation payload containing email, role, and schoolId
 * @returns Promise with the created invitation response
 */
export const sendInvitation = async (
  payload: ISendInvitationPayload
): Promise<TTransformResponse<IInvitationResponse>> => {
  const url = `${EAPIEndpoint.INVITATIONS}/send`;
  return request<IInvitationResponse>({
    url,
    options: {
      method: 'POST',
      body: JSON.stringify(payload)
    }
  });
};

/**
 * Validates an invitation token and returns invitation details with user info
 * @param token - The invitation token to validate
 * @returns Promise with the invitation validation response
 */
export const validateInvitation = async (
  token: string
): Promise<TTransformResponse<IValidateInvitationResponse>> => {
  const url = `${EAPIEndpoint.INVITATIONS}/validate?token=${encodeURIComponent(token)}`;
  return request<IValidateInvitationResponse>({
    url,
    options: {
      method: 'GET'
    }
  });
};

/**
 * Accepts an invitation and completes user registration (Public endpoint)
 * @param payload - The invitation acceptance payload containing token, name, and password
 * @returns Promise with the access token and user data
 */
export const acceptInvitation = async (
  payload: IAcceptInvitationPayload
): Promise<TTransformResponse<IAcceptInvitationResponse>> => {
  try {
    const baseUrl = process.env.API_URL || process.env.NEXT_PUBLIC_API_URL || '';
    const endpoint = `${baseUrl}/auth/accept-invitation`;

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    const result = await response.json();

    if (response.ok) {
      return {
        status: 'success',
        data: result,
      };
    } else {
      return {
        status: 'error',
        message: result.message || 'Failed to accept invitation',
        statusCode: response.status,
      };
    }
  } catch (error: any) {
    console.error('Error accepting invitation:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while accepting invitation.',
    };
  }
};

/**
 * Sends an email invitation to a new student and assigns exams simultaneously (Server-side only)
 * @param payload - The invitation payload containing email, schoolId, and examIds
 * @returns Promise with the created invitation and exam assignments response
 */
export const sendInvitationWithExam = async (
  payload: ISendInvitationWithExamPayload
): Promise<TTransformResponse<IInvitationWithExamResponse>> => {
  const url = `${EAPIEndpoint.INVITATIONS}/assign-exam`;
  return request<IInvitationWithExamResponse>({
    url,
    options: {
      method: 'POST',
      body: JSON.stringify(payload)
    }
  });
};
