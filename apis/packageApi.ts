import { request } from './request';
import { TTransformResponse } from './transformResponse';
import { transformResponse } from './transformResponse';

// Price interface based on API documentation
export interface IPrice {
  id: string;
  stripePriceId: string;
  currency: string;
  unitAmount: number;
  nickname?: string;
  active: boolean;
  type: 'one_time' | 'recurring';
  interval?: 'day' | 'week' | 'month' | 'year';
  intervalCount?: number;
  trialPeriodDays?: number;
  usageType?: 'licensed' | 'metered';
  createdAt: string;
  updatedAt: string;
}

// Create package payload - matches API CreatePackageDto
export interface ICreatePackagePayload {
  name: string;
  description: string;
  image?: string;
  stripeProductId?: string;
  prices: {
    currency: string;
    unitAmount: number;
    nickname?: string;
    active?: boolean;
    type: 'one_time' | 'recurring';
    interval?: 'day' | 'week' | 'month' | 'year';
    intervalCount?: number;
    trialPeriodDays?: number;
    usageType?: 'licensed' | 'metered';
  }[];
}

// Package response - matches actual API response structure
export interface IPackageResponse {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  name: string;
  description: string;
  image?: string;
  stripeProductId: string;
  prices: IPrice[];
}

// Update package payload - all fields optional
export interface IUpdatePackagePayload {
  name?: string;
  description?: string;
  image?: string;
  stripeProductId?: string;
  prices?: {
    currency: string;
    unitAmount: number;
    nickname?: string;
    active?: boolean;
    type: 'one_time' | 'recurring';
    interval?: 'day' | 'week' | 'month' | 'year';
    intervalCount?: number;
    trialPeriodDays?: number;
    usageType?: 'licensed' | 'metered';
  }[];
}

// Subscription session creation payload - for Stripe checkout
export interface ICreateSubscriptionSessionPayload {
  packageId: string;
  priceId?: string;
  successUrl?: string;
  cancelUrl?: string;
  metadata?: Record<string, string>;
}

// User subscription status response
export interface ISubscriptionStatusResponse {
  id: string;
  userId: string;
  packageId: string;
  package: IPackageResponse;
  stripeSubscriptionId?: string;
  stripeCustomerId?: string;
  status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid';
  currentPeriodStart?: string;
  currentPeriodEnd?: string;
  cancelAtPeriodEnd?: boolean;
  canceledAt?: string;
  trialStart?: string;
  trialEnd?: string;
  createdAt: string;
  updatedAt: string;
}

const PACKAGES_API_ENDPOINT = '/packages';
const SUBSCRIPTION_API_ENDPOINT = '/subscription';

/**
 * Creates a new package.
 * Corresponds to: POST /packages
 * @param payload - The package data.
 * @returns The created package details.
 */
export async function createPackage(payload: ICreatePackagePayload): Promise<TTransformResponse<IPackageResponse>> {
  try {
    const response = await request<IPackageResponse>({
      url: PACKAGES_API_ENDPOINT,
      options: {
        method: 'POST',
        body: JSON.stringify(payload),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error creating package:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while creating the package.' };
  }
}

/**
 * Fetches all packages.
 * Corresponds to: GET /packages
 * @returns A list of all packages.
 */
export async function getAllPackages(): Promise<TTransformResponse<IPackageResponse[]>> {
  try {
    const response = await request<IPackageResponse[]>({
      url: PACKAGES_API_ENDPOINT,
      options: {
        method: 'GET',
      }
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching packages:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while fetching packages.' };
  }
}

/**
 * Alias for getAllPackages() - matches Task 6 requirements.
 * For server-side usage with authentication.
 * Corresponds to: GET /packages
 * @returns A list of all packages.
 */
export const getPackages = getAllPackages;

/**
 * Fetches a package by ID.
 * Corresponds to: GET /packages/{id}
 * @param packageId - The ID of the package to fetch.
 * @returns The package details.
 */
export async function getPackageById(packageId: string): Promise<TTransformResponse<IPackageResponse>> {
  try {
    const response = await request<IPackageResponse>({
      url: `${PACKAGES_API_ENDPOINT}/${packageId}`,
      options: {
        method: 'GET',
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching package:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while fetching the package.' };
  }
}

/**
 * Updates an existing package.
 * Corresponds to: PUT /packages/{id}
 * @param packageId - The ID of the package to update.
 * @param payload - The package data to update.
 * @returns The updated package details.
 */
export async function updatePackage(packageId: string, payload: IUpdatePackagePayload): Promise<TTransformResponse<IPackageResponse>> {
  try {
    const response = await request<IPackageResponse>({
      url: `${PACKAGES_API_ENDPOINT}/${packageId}`,
      options: {
        method: 'PUT',
        body: JSON.stringify(payload),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error updating package:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while updating the package.' };
  }
}

/**
 * Deletes a package.
 * Corresponds to: DELETE /packages/{id}
 * @param packageId - The ID of the package to delete.
 * @returns Confirmation of deletion.
 */
export async function deletePackage(packageId: string): Promise<TTransformResponse<{ message: string }>> {
  try {
    const response = await request<{ message: string }>({
      url: `${PACKAGES_API_ENDPOINT}/${packageId}`,
      options: {
        method: 'DELETE',
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error deleting package:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while deleting the package.' };
  }
}

/**
 * Creates a subscription for a package (backward compatibility).
 * Corresponds to: POST /packages/subscription
 * @param packageId - The ID of the package to subscribe to.
 * @returns Stripe checkout session.
 */
export async function createSubscription(packageId: string): Promise<TTransformResponse<any>> {
  try {
    const response = await request<any>({
      url: `${PACKAGES_API_ENDPOINT}/subscription`,
      options: {
        method: 'POST',
        body: JSON.stringify({ id: packageId }),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error creating subscription:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while creating the subscription.' };
  }
}

/**
 * Creates a subscription session with detailed payload - matches Task 6 requirements.
 * Corresponds to: POST /packages/subscription
 * @param data - The subscription session data including packageId, priceId, URLs, etc.
 * @returns Stripe checkout session details.
 */
export async function createSubscriptionSession(data: ICreateSubscriptionSessionPayload): Promise<TTransformResponse<string>> {
  try {
    const response = await request<string>({
      url: `${PACKAGES_API_ENDPOINT}/subscription`,
      options: {
        method: 'POST',
        body: JSON.stringify(data),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error creating subscription session:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while creating the subscription session.' };
  }
}

/**
 * Retrieves the current user's subscription status - matches Task 6 requirements.
 * Corresponds to: GET /subscription/me
 * @returns The current user's subscription status and details.
 */
export async function getSubscriptionStatus(accessToken?: string): Promise<TTransformResponse<ISubscriptionStatusResponse>> {
  try {
    const response = await request<ISubscriptionStatusResponse>({
      url: `${SUBSCRIPTION_API_ENDPOINT}/me/`,
      options: {
        method: 'GET',
        headers: accessToken
          ? { Authorization: `Bearer ${accessToken}` }
          : undefined,
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching subscription status:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while fetching subscription status.' };
  }
} 