import { clientRequest } from './clientRequest';
import { GetDailySummaryResponse, IDailySummary } from '@/types/usage';

const USAGE_API = {
  DAILY_SUMMARY: '/usage/summary/daily',
} as const;

export const usageApi = {
  /**
   * Fetches daily usage summary data
   * @returns Promise containing an array of daily summaries
   */
  getDailySummary: async (accessToken: string): Promise<IDailySummary> => {
    try {
      const response = await clientRequest<GetDailySummaryResponse>({
        url: USAGE_API.DAILY_SUMMARY,
        options: {
          method: 'GET',
        },
        accessToken,
      });

      if (response.status === 'error') {
        const errorMessage = Array.isArray(response.message)
          ? response.message.map(err => err.constraints).join(', ')
          : response.message;
        throw new Error(errorMessage);
      }

      return response.data.data.dailySummary;
    } catch (error) {
      console.error('Error fetching daily usage summary:', error);
      throw error;
    }
  },
}; 