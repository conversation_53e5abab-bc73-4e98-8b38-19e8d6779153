'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { getExamResultsAction, getExamByIdAction } from '@/actions/exam.action';
import { IExamResultsResponse, IExamDetailResponse, IExamAssignmentDetail } from '@/types/exam.types';
import { Button } from '@/components/atoms/Button/Button';
import { LoadingSpinner } from '@/components/molecules/LoadingSpinner';
import { ErrorDisplay } from '@/components/molecules/ErrorDisplay/ErrorDisplay';
import { SubmissionListTable } from '@/components/molecules/ExamManagement/SubmissionListTable/SubmissionListTable';
import { AssignExamModal } from '@/components/molecules/AssignExamModal/AssignExamModal';
import { AssignmentTrackingSection } from '@/components/molecules/AssignmentTrackingSection/AssignmentTrackingSection';
import { ExamStatsOverview } from '@/components/molecules/ExamStatsOverview/ExamStatsOverview';
import { Tabs } from '@/components/atoms/Tabs/Tabs';
import BottomDock from '@/components/organisms/BottomDock/BottomDock';
import {
  Users,
  BarChart3,
  UserPlus
} from 'lucide-react';

export const dynamic = 'force-dynamic';

export default function ExamResultsDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session, status } = useSession();
  
  const examId = params.id as string;
  const [resultsData, setResultsData] = useState<IExamResultsResponse | null>(null);
  const [examDetails, setExamDetails] = useState<IExamDetailResponse | null>(null);
  const [assignments, setAssignments] = useState<IExamAssignmentDetail[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Modal state for assign exam
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);

  const formatErrorMessage = (message: any): string => {
    if (typeof message === 'string') {
      return message;
    }
    if (Array.isArray(message)) {
      return message.map(err => err.constraints || 'Unknown error').join(', ');
    }
    return 'An unexpected error occurred.';
  };

  // Fetch exam results data
  useEffect(() => {
    const fetchResults = async () => {
      if (status === 'loading') {
        return; // Wait for session to load
      }

      if (!examId) {
        setError('Invalid exam ID');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Fetch exam details which should include assignments according to API spec
        const detailsResponse = await getExamByIdAction(examId);
        
        if (detailsResponse.status === 'success' && detailsResponse.data) {
          setExamDetails(detailsResponse.data);
          
          // Extract assignments if available (from API spec, it should be in assignments field)
          const examData = detailsResponse.data as any;
          if (examData.assignments && Array.isArray(examData.assignments)) {
            setAssignments(examData.assignments);
          }
        }

        // Also fetch results data for the submissions table
        const resultsResponse = await getExamResultsAction(examId, {});
        
        if (resultsResponse.status === 'success' && resultsResponse.data) {
          setResultsData(resultsResponse.data);
          
          // If assignments weren't in exam details, check if they're in results response
          if (resultsResponse.data.assignments && Array.isArray(resultsResponse.data.assignments)) {
            setAssignments(resultsResponse.data.assignments);
          }
        }
        
        // Handle case where one or both requests failed
        if (detailsResponse.status === 'error' && resultsResponse.status === 'error') {
          const errorMsg = formatErrorMessage(detailsResponse.message) || formatErrorMessage(resultsResponse.message);
          setError(errorMsg || 'Failed to load exam data');
        } else if (detailsResponse.status === 'error') {
          // Can still show results even if details failed
          console.warn('Failed to load exam details:', detailsResponse.message);
        } else if (resultsResponse.status === 'error') {
          // Can still show assignments even if results failed
          console.warn('Failed to load exam results:', resultsResponse.message);
        }

      } catch (err: any) {
        console.error('Error fetching exam data:', err);
        setError(err.message || 'An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchResults();
  }, [examId, status, router]);

  const handleGoBack = () => {
    router.push('/admin/exam-results');
  };

  // Handler for opening assign exam modal
  const handleAssignExam = () => {
    setIsAssignModalOpen(true);
  };

  // Handler for closing assign exam modal
  const handleCloseAssignModal = () => {
    setIsAssignModalOpen(false);
  };

  // Handler for successful assignment
  const handleAssignSuccess = () => {
    // Refresh the data to show new assignments
    window.location.reload();
  };

  // Handler for assignment card click
  const handleAssignmentClick = (assignment: IExamAssignmentDetail) => {
    // Navigate to detailed student result if completed
    if (assignment.status === 'completed') {
      router.push(`/admin/exam-results/${examId}/student/${assignment.studentId}`);
    }
  };

  // Loading state
  if (isLoading || status === 'loading') {
    return <LoadingSpinner message="Loading exam results..." fullScreen />;
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-4 pb-24">
        <div className="max-w-4xl mx-auto">
          <section className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
            <ErrorDisplay error={error} />
          </section>
        </div>
        <BottomDock className="justify-start">
          <Button
            variant="ghost"
            onClick={handleGoBack}
            className="w-10 h-10 rounded-full p-0 text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-all duration-200"
            iconProps={{
              variant:'arrow-left',
              className:'w-5'
            }}
          />
        </BottomDock>
      </div>
    );
  }

  // No data state is now handled within individual tabs, so we don't need this section

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-4 pb-24">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Enhanced Header */}
        <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
          <div className="flex items-center justify-between max-sm:flex-col-reverse max-sm:items-start max-sm:gap-4">
            <div className="space-y-3">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  {resultsData?.examTitle || examDetails?.title || 'Exam Results'}
                </h1>
                <p className="text-gray-600 flex items-center gap-2">
                  <BarChart3 className="w-4" />
                  Exam Results & Analytics Dashboard
                </p>
              </div>
            </div>
            <div className="flex items-center max-sm:w-full max-sm:justify-end gap-3">
              <Button
                variant="primary"
                onClick={handleAssignExam}
                className="flex items-center gap-2"
                title="Assign this exam to more students"
                iconProps={{
                  variant:'plus',
                  className:'w-4'
                }}
              >
                Assign Exam
              </Button>
            </div>
          </div>
        </div>

        {/* Tab-based Content Layout */}
        <Tabs.Container defaultTab="overview" className="space-y-0">
          {/* Tab Navigation */}
          <Tabs.List className="bg-white rounded-t-2xl border border-gray-200 border-b-0 shadow-sm">
            <Tabs.Tab value="overview" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              Overview
            </Tabs.Tab>
            <Tabs.Tab value="assignments" className="flex items-center gap-2">
              <UserPlus className="w-4 h-4" />
              Assignments ({assignments.length})
            </Tabs.Tab>
            <Tabs.Tab value="submissions" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              Submissions ({resultsData?.results?.length || 0})
            </Tabs.Tab>
          </Tabs.List>

          {/* Tab Content */}
          <div className="bg-white rounded-b-2xl border border-gray-200 shadow-lg overflow-hidden">
            {/* Overview Tab */}
            <Tabs.Panel value="overview" className="p-6">
              <ExamStatsOverview 
                resultsData={resultsData}
                totalAssignments={assignments.length}
              />
            </Tabs.Panel>

            {/* Assignments Tab */}
            <Tabs.Panel value="assignments" className="p-6">
              {assignments.length > 0 ? (
                <AssignmentTrackingSection
                  assignments={assignments}
                  onAssignmentClick={handleAssignmentClick}
                />
              ) : (
                <div className="text-center py-12">
                  <UserPlus className="h-16 w-16 mx-auto mb-6 text-gray-400" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">No Assignments Yet</h3>
                  <p className="text-gray-600 mb-6">
                    Assign this exam to students to start tracking their progress.
                  </p>
                  <Button
                    variant="primary"
                    onClick={handleAssignExam}
                    className="flex items-center gap-2 mx-auto"
                  >
                    <UserPlus className="w-4" />
                    Assign Exam to Students
                  </Button>
                </div>
              )}
            </Tabs.Panel>

            {/* Submissions Tab */}
            <Tabs.Panel value="submissions" className="p-0">
              {resultsData?.results ? (
                <div>
                  <div className="p-6 border-b border-gray-100">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">Student Submissions</h3>
                        <p className="text-sm text-gray-600 mt-1">
                          Detailed results for all {resultsData.stats?.totalSubmissions || 0} submissions
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">
                          {resultsData.results.length} results
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="p-6">
                    <SubmissionListTable
                      examId={examId}
                      initialData={resultsData}
                      showExportButton={true}
                      onExportResults={() => {
                        // TODO: Implement export functionality
                        console.log('Export results clicked');
                      }}
                    />
                  </div>
                </div>
              ) : (
                <div className="p-12 text-center">
                  <Users className="h-16 w-16 mx-auto mb-6 text-gray-400" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">No Submissions Yet</h3>
                  <p className="text-gray-600">
                    Student submissions will appear here once they complete the exam.
                  </p>
                </div>
              )}
            </Tabs.Panel>
          </div>
        </Tabs.Container>
        
      </div>

      <BottomDock className="justify-between lg:left-[322px] lg:w-[calc(100vw-330px)]">
        <Button
          variant="ghost"
          onClick={handleGoBack}
          className="w-10 h-10 rounded-full p-0 text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-all duration-200"
          iconProps={{
            variant:'arrow-left',
            className:'w-5 m-0'
          }}
        />
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            className="flex items-center gap-2 hover:bg-blue-50 hover:border-blue-200 transition-all duration-200"
            iconProps={{
              variant:'mail',
              className:'w-4'
            }}
          >
            Email Results
          </Button>
          <Button
            variant="primary"
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 transition-all duration-200"
            iconProps={{
              variant:'download',
              className:'w-4'
            }}
          >
            Export Data
          </Button>
        </div>
      </BottomDock>

      {/* Assign Exam Modal */}
      <AssignExamModal
        isOpen={isAssignModalOpen}
        onClose={handleCloseAssignModal}
        examId={examId}
        examTitle={examDetails?.title || resultsData?.examTitle || 'Exam'}
        onSuccess={handleAssignSuccess}
      />
    </div>
  );
}
