'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { getDetailedStudentResultAction } from '@/actions/exam.action';
import { IStudentExamResultForTeacher } from '@/types/exam.types';
import { Button } from '@/components/atoms/Button/Button';
import { LoadingSpinner } from '@/components/molecules/LoadingSpinner';
import { ErrorDisplay } from '@/components/molecules/ErrorDisplay/ErrorDisplay';
import { DetailedStudentResultView } from '@/components/organisms/DetailedStudentResultView';
import BottomDock from '@/components/organisms/BottomDock/BottomDock';
import { 
  User, 
  Clock, 
  Calendar, 
  Target, 
  TrendingUp, 
  Award, 
  CheckCircle, 
  XCircle, 
  BookOpen,
  Sparkles
} from 'lucide-react';
import { cn } from '@/utils/cn';
import { formatExamDuration, formatDate } from '@/utils/date';
import { TApiError } from '@/apis/transformResponse';

export default function DetailedStudentResultPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const examId = params.id as string;
  const studentId = params.studentId as string;
  
  const [result, setResult] = useState<IStudentExamResultForTeacher | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const formatErrorMessage = (message: any): string => {
    if (typeof message === 'string') {
      return message;
    }
    if (Array.isArray(message)) {
      return message.map(err => err.constraints || 'Unknown error').join(', ');
    }
    return 'An unexpected error occurred.';
  };

  useEffect(() => {
    const fetchStudentResult = async () => {
      if (!examId || !studentId || status === 'loading') return;
      
      if (status === 'unauthenticated') {
        router.push('/auth/sign-in');
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await getDetailedStudentResultAction(examId, studentId);

        if (response.status === 'success') {
          if (response.data) {
            setResult(response.data);
          } else {
            setError('No detailed results found for this student.');
          }
        } else {
          setError(formatErrorMessage((response as TApiError).message) || 'Failed to load detailed student results');
        }
      } catch (err: any) {
        console.error('Error fetching detailed student result:', err);
        setError(err.message || 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchStudentResult();
  }, [examId, studentId, status, router]);

  const handleGoBack = () => {
    router.push(`/admin/exam-results/${examId}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background-subtle flex items-center justify-center p-4">
        <div className="bg-background-default rounded-2xl p-8 shadow-md w-full max-w-md">
          <div className="py-12 flex flex-col items-center">
            <LoadingSpinner />
            <p className="text-text-secondary mt-4">Loading student results...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background-subtle p-4">
        <div className="max-w-md mx-auto pt-20">
          <div className="bg-background-default rounded-2xl p-6 shadow-md">
            <ErrorDisplay error={error} />
            <Button onClick={() => window.location.reload()} className="mt-4 w-full" variant="primary">
              Try Again
            </Button>
          </div>
        </div>

        <BottomDock className="justify-start">
          <Button
            variant="ghost"
            onClick={handleGoBack}
            className="w-12 h-12 rounded-full p-0 text-text-secondary hover:text-text-primary hover:bg-background-subtle transition-all duration-200"
            iconProps={{
              variant:'arrow-left',
              className:'w-5'
            }}
          />
        </BottomDock>
      </div>
    );
  }

  if (!result) {
    return (
      <div className="min-h-screen bg-background-subtle p-4">
        <div className="max-w-md mx-auto pt-20">
          <div className="bg-background-default rounded-2xl p-6 shadow-md text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-section-bg-accent rounded-full flex items-center justify-center">
              <User className="w-8 text-primary-action" />
            </div>
            <h3 className="text-lg font-semibold text-text-primary mb-2">No Results Found</h3>
            <p className="text-text-secondary">No detailed results found for this student.</p>
          </div>
        </div>

        <BottomDock className="justify-start">
          <Button
            variant="ghost"
            onClick={handleGoBack}
            className="w-12 h-12 rounded-full p-0 text-text-secondary hover:text-text-primary hover:bg-background-subtle transition-all duration-200"
            iconProps={{
              variant:'arrow-left',
              className:'w-5'
            }}
          />
        </BottomDock>
      </div>
    );
  }

  // Status badge component for better reusability
  const StatusBadge = ({ status }: { status: 'passed' | 'failed' }) => {
    const isPassed = status === 'passed';
    return (
      <div className={cn(
        'inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-sm font-semibold',
        isPassed 
          ? 'bg-green-50 text-green-700 border border-green-200' 
          : 'bg-red-50 text-red-700 border border-red-200'
      )}>
        {isPassed ? (
          <CheckCircle className="w-4 h-4" />
        ) : (
          <XCircle className="w-4 h-4" />
        )}
        <span>{isPassed ? 'Passed' : 'Failed'}</span>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-background-subtle pb-20">
      {/* Mobile Layout: Single Column */}
      <div className="lg:hidden max-w-4xl mx-auto p-4 space-y-5">
        {/* Student Header Card */}
        <div className="bg-background-default rounded-2xl overflow-hidden border-none shadow-md">
          <div className="h-3 bg-gradient-to-r from-primary-action to-primary-action/70"></div>
          <div className="p-5">
            <div className="flex items-start justify-between gap-4 mb-5">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-14 h-14 bg-primary-action/10 rounded-full flex items-center justify-center">
                  <User className="w-7 h-7 text-primary-action" />
                </div>
                <div className="min-w-0 flex-1">
                  <h1 className="text-xl font-bold text-text-primary truncate">
                    {result.studentName}
                  </h1>
                  <p className="text-sm text-text-secondary truncate mt-1">
                    <BookOpen className="w-3.5 h-3.5 inline mr-1.5 opacity-70" />
                    {result.examTitle}
                  </p>
                </div>
              </div>
              {/* Status Badge - Now positioned at the top right */}
              <StatusBadge status={result.status} />
            </div>

            {/* Score Highlight Card */}
            <div className={cn(
              "rounded-xl p-5 mb-5 border",
              result.status === 'passed' 
                ? "bg-green-50 border-green-100" 
                : "bg-red-50 border-red-100"
            )}>
              <div className="flex items-center justify-between">
                <div className="text-center flex-1">
                  <div className="text-3xl font-bold text-text-primary">
                    {result.score}<span className="text-lg text-text-secondary">/{result.total}</span>
                  </div>
                  <p className="text-xs text-text-secondary font-medium mt-1">Score</p>
                </div>
                <div className="w-px h-12 bg-gray-200 mx-4"></div>
                <div className="text-center flex-1">
                  <div className={cn(
                    "text-3xl font-bold",
                    result.status === 'passed' ? 'text-green-600' : 'text-red-600'
                  )}>
                    {result.percentage.toFixed(1)}%
                  </div>
                  <p className="text-xs text-text-secondary font-medium mt-1">Percentage</p>
                </div>
              </div>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-background-default border border-gray-100 rounded-xl p-4 shadow-sm">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-50 rounded-lg flex items-center justify-center">
                    <Clock className="w-5 text-blue-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-xs font-medium text-text-secondary uppercase tracking-wide">Time Spent</p>
                    <p className="text-base font-bold text-text-primary truncate">
                      {result.timeSpent ? formatExamDuration(result.timeSpent) : 'N/A'}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-background-default border border-gray-100 rounded-xl p-4 shadow-sm">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-purple-50 rounded-lg flex items-center justify-center">
                    <Calendar className="w-5 text-purple-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-xs font-medium text-text-secondary uppercase tracking-wide">Submitted</p>
                    <p className="text-sm font-bold text-text-primary">
                      {formatDate(result.submittedAt)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Performance Insights Card */}
        <div className="bg-background-default rounded-2xl shadow-md border-none p-5">
          <div className="flex items-center gap-2 mb-4">
            <TrendingUp className="w-5 text-primary-action" />
            <h2 className="text-lg font-semibold text-text-primary">Performance Summary</h2>
          </div>
          
          <div className="space-y-5">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-text-secondary">Completion Rate</span>
                <span className="font-medium text-text-primary">{result.percentage.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={cn(
                    'h-2 rounded-full transition-all duration-300',
                    result.status === 'passed' ? 'bg-green-500' : 'bg-red-500'
                  )}
                  style={{ width: `${result.percentage}%` }}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 pt-2">
              <div className="text-center p-4 bg-section-bg-neutral-alt rounded-lg">
                <div className="text-lg font-bold text-text-primary">{result.score}</div>
                <div className="text-xs text-text-secondary">Correct Answers</div>
              </div>
              <div className="text-center p-4 bg-section-bg-neutral-alt rounded-lg">
                <div className="text-lg font-bold text-text-primary">{result.total - result.score}</div>
                <div className="text-xs text-text-secondary">Incorrect Answers</div>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Detailed Results */}
        <div className="bg-background-default rounded-2xl shadow-md border-none">
          <div className="p-5 pb-2">
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="w-5 h-5 text-primary-action" />
              <h2 className="text-lg font-semibold text-text-primary">Detailed Results</h2>
            </div>
          </div>
          <DetailedStudentResultView studentResult={result} />
        </div>
      </div>

      {/* Desktop Layout: Two Columns */}
      <div className="hidden lg:block max-w-7xl mx-auto p-6">
        <div className="grid lg:grid-cols-12 gap-6 h-full">
          {/* Left Column - Student Overview */}
          <div className="lg:col-span-4 space-y-6">
            {/* Student Header Card */}
            <div className="bg-background-default rounded-2xl shadow-md border-none overflow-hidden sticky top-6">
              <div className="h-3 bg-gradient-to-r from-primary-action to-primary-action/70"></div>
              <div className="p-6">
                <div className="flex flex-col gap-5">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="flex-shrink-0 w-16 h-16 bg-primary-action/10 rounded-full flex items-center justify-center">
                        <User className="w-8 h-8 text-primary-action" />
                      </div>
                      <div>
                        <h1 className="text-2xl font-bold text-text-primary">
                          {result.studentName}
                        </h1>
                        <p className="text-base text-text-secondary">
                          <BookOpen className="w-4 h-4 inline mr-2 opacity-70" />
                          {result.examTitle}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Score Highlight with Status Badge */}
                  <div className={cn(
                    "rounded-xl p-6 border relative",
                    result.status === 'passed' 
                      ? "bg-green-50 border-green-100" 
                      : "bg-red-50 border-red-100"
                  )}>
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <StatusBadge status={result.status} />
                    </div>
                    
                    <div className="text-center pt-2">
                      <div className="text-4xl font-bold text-text-primary mb-2">
                        {result.score}<span className="text-xl text-text-secondary">/{result.total}</span>
                      </div>
                      <div className={cn(
                        "text-2xl font-bold mb-1",
                        result.status === 'passed' ? 'text-green-600' : 'text-red-600'
                      )}>
                        {result.percentage.toFixed(1)}%
                      </div>
                      <p className="text-sm text-text-secondary font-medium">Final Score</p>
                    </div>
                  </div>

                  {/* Performance Insights */}
                  <div className="space-y-5">
                    <h3 className="text-lg font-semibold text-text-primary flex items-center gap-2">
                      <TrendingUp className="w-5 h-5 text-primary-action" />
                      Performance
                    </h3>
                    
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-text-secondary">Completion Rate</span>
                          <span className="font-medium text-text-primary">{result.percentage.toFixed(1)}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div 
                            className={cn(
                              'h-2.5 rounded-full transition-all duration-300',
                              result.status === 'passed' ? 'bg-green-500' : 'bg-red-500'
                            )}
                            style={{ width: `${result.percentage}%` }}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div className="text-center p-4 bg-section-bg-neutral-alt rounded-lg">
                          <div className="text-xl font-bold text-text-primary">{result.score}</div>
                          <div className="text-xs text-text-secondary">Correct</div>
                        </div>
                        <div className="text-center p-4 bg-section-bg-neutral-alt rounded-lg">
                          <div className="text-xl font-bold text-text-primary">{result.total - result.score}</div>
                          <div className="text-xs text-text-secondary">Incorrect</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Time & Date Info */}
                  <div className="pt-6 border-t border-gray-200">
                    <div className="space-y-4">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center">
                          <Clock className="w-6 h-6 text-blue-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="text-xs font-medium text-text-secondary uppercase tracking-wide">Time Spent</p>
                          <p className="text-base font-semibold text-text-primary">
                            {result.timeSpent ? formatExamDuration(result.timeSpent) : 'N/A'}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-purple-50 rounded-lg flex items-center justify-center">
                          <Calendar className="w-6 h-6 text-purple-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="text-xs font-medium text-text-secondary uppercase tracking-wide">Submitted</p>
                          <p className="text-base font-semibold text-text-primary">
                            {formatDate(result.submittedAt)}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Detailed Results */}
          <div className="lg:col-span-8">
            <div className="bg-background-default rounded-2xl shadow-md border-none">
              <div className="p-6 pb-2">
                <h2 className="text-xl font-semibold text-text-primary flex items-center gap-2">
                  <Sparkles className="w-5 h-5 text-primary-action" />
                  Detailed Results
                </h2>
              </div>
              <DetailedStudentResultView studentResult={result} />
            </div>
          </div>
        </div>
      </div>

      <BottomDock className="justify-start lg:left-[322px]">
        <Button
          variant="outline"
          onClick={handleGoBack}
          className="w-10 h-10 rounded-full p-0 text-text-secondary hover:text-white shadow-sm"
          iconProps={{
            variant:'arrow-left',
            className:'w-5 m-0'
          }}
        />
      </BottomDock>
    </div>
  );
}
