'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { ExamListTable } from '@/components/molecules/ExamManagement/ExamListTable/ExamListTable';
import { Button } from '@/components/atoms/Button/Button';
import { LoadingSpinner } from '@/components/molecules/LoadingSpinner';
import { Breadcrumb } from '@/components/atoms/Breadcrumb/Breadcrumb';
import { 
  BarChart3, 
  FileText, 
  Users, 
  TrendingUp, 
  Plus,
  Calendar,
  BookOpen,
  Award,
  Clock
} from 'lucide-react';
import { cn } from '@/utils/cn';

// Mock data for stats - in a real app, this would come from an API
const mockStats = {
  totalExams: 12,
  totalSubmissions: 156,
  averageScore: 82.5,
  completionRate: 94.2,
  pendingReviews: 8,
  recentActivity: 24
};

export default function ExamResultsPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [isLoading, setIsLoading] = useState(false);

  const handleCreateExam = () => {
    router.push('/manage-worksheet');
  };

  const breadcrumbItems = [
    { label: 'Dashboard', href: '/' },
    { label: 'Admin', href: '/admin' },
    { label: 'Exam Results', href: '/admin/exam-results', current: true }
  ];

  const statCards = [
    {
      title: 'Total Exams',
      value: mockStats.totalExams.toString(),
      subtitle: 'Published exams',
      icon: FileText,
      color: 'text-primary-action',
      bgColor: 'bg-section-bg-accent'
    },
    {
      title: 'Total Submissions',
      value: mockStats.totalSubmissions.toString(),
      subtitle: 'Student submissions',
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Average Score',
      value: `${mockStats.averageScore}%`,
      subtitle: 'Across all exams',
      icon: Award,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Completion Rate',
      value: `${mockStats.completionRate}%`,
      subtitle: 'Assignment completion',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    }
  ];

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-background-subtle flex items-center justify-center">
        <LoadingSpinner message="Loading exam results..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background-subtle">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 space-y-6">
        
        {/* Header Section */}
        <div className="bg-background-default rounded-2xl shadow-sm border border-gray-100 p-6">
          {/* Breadcrumb */}
          <div className="mb-4">
            <Breadcrumb items={breadcrumbItems} />
          </div>

          {/* Page Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-section-bg-accent rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-5 text-primary-action" />
                </div>
                <div>
                  <h1 className="text-2xl sm:text-3xl font-bold text-text-primary">
                    Exam Results Dashboard
                  </h1>
                  <p className="text-text-secondary mt-1">
                    Monitor and analyze exam performance across your institution
                  </p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                className="flex items-center gap-2 hover:bg-section-bg-accent hover:border-primary-action/20 transition-all duration-200"
                iconProps={{
                  variant:'download',
                  className:'w-4'
                }}
              >
                Export Report
              </Button>
              <Button
                variant="primary"
                onClick={handleCreateExam}
                className="flex items-center gap-2 bg-primary-action hover:opacity-90 transition-all duration-200"
                iconProps={{
                  variant:'plus',
                  className:'w-4'
                }}
              >
                Create New Exam
              </Button>
            </div>
          </div>
        </div>

        {/* Main Exam Results Table */}
        <div className="bg-background-default rounded-xl border border-gray-100 shadow-sm overflow-hidden">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-text-primary">Your Exams</h2>
                <p className="text-text-secondary mt-1">
                  Manage and review all your published exams and their results
                </p>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-text-secondary">
                  {mockStats.totalExams} total exams
                </span>
              </div>
            </div>
          </div>
          
          <div className="p-6">
            <ExamListTable 
              tableTitle=""
              showCreateButton={false}
              onCreateExam={handleCreateExam}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
