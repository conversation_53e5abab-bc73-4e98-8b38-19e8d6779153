import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/config/auth';

export async function POST(request: NextRequest) {
  try {
    // Get the session to check authentication
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.accessToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the form data from the request
    const formData = await request.formData();
    
    // Get the backend API URL
    const backendUrl = process.env.API_URL || process.env.NEXT_PUBLIC_API_URL;
    if (!backendUrl) {
      return NextResponse.json(
        { error: 'Backend API URL not configured' },
        { status: 500 }
      );
    }

    // Forward the request to the backend
    const backendResponse = await fetch(`${backendUrl}/documents/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.user.accessToken}`,
        // Don't set Content-Type for FormData, let the browser set it with boundary
      },
      body: formData,
    });

    const responseData = await backendResponse.json();

    if (backendResponse.ok) {
      return NextResponse.json(responseData, { status: 200 });
    } else {
      return NextResponse.json(
        responseData,
        { status: backendResponse.status }
      );
    }
  } catch (error: any) {
    console.error('Error in document upload API route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 