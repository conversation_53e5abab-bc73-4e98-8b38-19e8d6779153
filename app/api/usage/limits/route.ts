import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/config/auth';

// Mock data based on the new types.
// In a real implementation, this data would come from a database
// and be calculated based on the user's subscription plan.
const mockUsageLimits = [
  {
    type: 'worksheet',
    limit: 20,
    dailyUsage: [
      { date: new Date().toISOString().slice(0, 10), count: 5 }
    ],
  },
  {
    type: 'question_per_worksheet',
    limit: 100,
    dailyUsage: [], // This limit is per-worksheet, not a daily aggregate
  },
];

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Here you would typically fetch user's subscription to get limits
    // and query a database for their actual usage.
    // For now, we return mock data.

    return NextResponse.json({ data: mockUsageLimits });
  } catch (error) {
    console.error('Failed to get usage limits:', error);
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
} 