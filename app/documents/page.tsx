'use client';

import React from 'react';
import { DocumentUpload } from '@/components/molecules/DocumentUpload/DocumentUpload';
import { IDocumentUploadResponse } from '@/apis/documentUploadApi';
import Container from '@/components/atoms/Container/Container';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { EUserRole } from '@/config/enums/user';
import DashboardTemplate from '@/components/templates/Dashboard/Dashboard';

export default function DocumentUploadPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  // Default props for DashboardTemplate
  const defaultSidebarItems: any[] = [];

  const defaultUserMenuDropdownProps = {
    userName: session?.user?.name || undefined,
    userEmail: session?.user?.email || undefined,
  };

  const handleUploadSuccess = (response: IDocumentUploadResponse) => {
    console.log('Upload successful:', response);
    // You can add additional success handling here
    // For example, redirect to a documents list page
  };

  const handleUploadError = (error: string) => {
    console.error('Upload failed:', error);
    // You can add additional error handling here
  };

  // Loading state
  if (status === 'loading') {
    return (
      <DashboardTemplate
        sidebarItems={defaultSidebarItems}
        userMenuDropdownProps={defaultUserMenuDropdownProps}
        schoolInfo={null}
      >
        <div className="flex flex-col items-center justify-center min-h-[400px]">
          <span className="loading loading-spinner loading-lg mb-4"></span>
          <p className="text-lg font-medium text-gray-700">
            Loading session...
          </p>
        </div>
      </DashboardTemplate>
    );
  }

  // Redirect if not authenticated
  if (status === 'unauthenticated') {
    router.push('/auth/sign-in');
    return (
      <DashboardTemplate
        sidebarItems={defaultSidebarItems}
        userMenuDropdownProps={defaultUserMenuDropdownProps}
        schoolInfo={null}
      >
        <p className="text-center">Redirecting to sign-in...</p>
      </DashboardTemplate>
    );
  }

  // Check if user is ADMIN
  if (session?.user?.role !== EUserRole.ADMIN) {
    return (
      <DashboardTemplate
        sidebarItems={defaultSidebarItems}
        userMenuDropdownProps={defaultUserMenuDropdownProps}
        schoolInfo={null}
      >
        <div className="flex flex-col items-center justify-center min-h-[400px]">
          <div className="alert alert-error max-w-md">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="stroke-current shrink-0 h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>
              Access denied. This page is only available to Administrators.
            </span>
          </div>
        </div>
      </DashboardTemplate>
    );
  }

  return (
    <DashboardTemplate
      sidebarItems={defaultSidebarItems}
      userMenuDropdownProps={defaultUserMenuDropdownProps}
      schoolInfo={null}
    >
      <Container>
        <div className="max-w-4xl mx-auto py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Upload Documents
            </h1>
            <p className="text-gray-600">
              Upload documents for processing. Supported formats include PDF,
              DOC, DOCX, TXT, and RTF files.
            </p>
          </div>

          <DocumentUpload
            onSuccess={handleUploadSuccess}
            onError={handleUploadError}
          />
        </div>
      </Container>
    </DashboardTemplate>
  );
}
