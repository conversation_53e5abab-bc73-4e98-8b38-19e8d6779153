'use client';

import React from 'react';
import { ExamPrintPreview } from '@/components/molecules/PDFExport';
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import {
  ExamHeader,
  PrintOptions,
} from '@/components/molecules/PrintModal/types';

const ExamPreviewPage = () => {
  // Sample exam header data
  const examHeader: ExamHeader = {
    schoolName: 'EduSG Education Center',
    examTitle: 'Mathematics Assessment',
    subject: 'Mathematics',
    examDate: 'December 2024',
    duration: '90 minutes',
    gradeLevel: 'Grade 10',
    studentName: '',
    className: '',
    logoUrl: '', // Add your logo URL here if needed
  };

  // Sample print options
  const printOptions: PrintOptions = {
    paperSize: 'A4',
    orientation: 'portrait',
    includeInstructions: true,
    includeMarkAllocation: true,
  };

  // Sample questions with MathML content
  const questions: Question[] = [
    {
      id: '1',
      type: 'multiple_choice',
      content: `Solve for x in the equation: <math xmlns="http://www.w3.org/1998/Math/MathML">
        <mi>x</mi><mo>+</mo><mn>5</mn><mo>=</mo><mn>12</mn>
      </math>`,
      options: [
        '<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>x</mi><mo>=</mo><mn>7</mn></math>',
        '<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>x</mi><mo>=</mo><mn>17</mn></math>',
        '<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>x</mi><mo>=</mo><mn>5</mn></math>',
        '<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>x</mi><mo>=</mo><mn>-7</mn></math>',
      ],
      answer: [
        '<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>x</mi><mo>=</mo><mn>7</mn></math>',
      ],
      explain: 'To solve x + 5 = 12, subtract 5 from both sides to get x = 7.',
    },
    {
      id: '2',
      type: 'fill_blank',
      content: `The quadratic formula is: <math xmlns="http://www.w3.org/1998/Math/MathML">
        <mi>x</mi><mo>=</mo>
        <mfrac>
          <mrow>
            <mo>-</mo><mi>b</mi><mo>±</mo>
            <msqrt>
              <msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>4</mn><mi>a</mi><mi>c</mi>
            </msqrt>
          </mrow>
          <mrow><mn>2</mn><mi>a</mi></mrow>
        </mfrac>
      </math>. If a = 1, b = -3, and c = 2, then the discriminant equals ___.`,
      options: [],
      answer: ['1'],
      explain: 'The discriminant is b² - 4ac = (-3)² - 4(1)(2) = 9 - 8 = 1.',
    },
    {
      id: '3',
      type: 'single_choice',
      content: `What is the derivative of <math xmlns="http://www.w3.org/1998/Math/MathML">
        <mi>f</mi><mo>(</mo><mi>x</mi><mo>)</mo><mo>=</mo><msup><mi>x</mi><mn>3</mn></msup>
      </math>?`,
      options: [
        '<math xmlns="http://www.w3.org/1998/Math/MathML"><mn>3</mn><msup><mi>x</mi><mn>2</mn></msup></math>',
        '<math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>x</mi><mn>2</mn></msup></math>',
        '<math xmlns="http://www.w3.org/1998/Math/MathML"><mn>3</mn><mi>x</mi></math>',
        '<math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>x</mi><mn>4</mn></msup></math>',
      ],
      answer: [
        '<math xmlns="http://www.w3.org/1998/Math/MathML"><mn>3</mn><msup><mi>x</mi><mn>2</mn></msup></math>',
      ],
      explain: 'Using the power rule, the derivative of x³ is 3x².',
    },
    {
      id: '4',
      type: 'creative_writing',
      content: `Explain how the Pythagorean theorem <math xmlns="http://www.w3.org/1998/Math/MathML">
        <msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>=</mo><msup><mi>c</mi><mn>2</mn></msup>
      </math> can be used to find the distance between two points in a coordinate plane.`,
      options: [],
      answer: [
        'Use the theorem to calculate the hypotenuse of a right triangle formed by the horizontal and vertical distances between the points.',
      ],
      explain:
        'The distance formula is derived from the Pythagorean theorem by treating the horizontal and vertical distances as the legs of a right triangle.',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">

          <button
            onClick={() => window.print()}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
          >
            Print Preview
          </button>

          {/* The ExamPrintPreview component */}
          <ExamPrintPreview
            questions={questions}
            examHeader={examHeader}
            printOptions={printOptions}
            isHtmlContent={true} // Enable MathML rendering
          />
        </div>
      </div>
    </div>
  );
};

export default ExamPreviewPage;
