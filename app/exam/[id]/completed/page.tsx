'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { getExamCompletionDataByExamIdAction } from '@/actions/exam.action';
import { IExamCompletionData } from '@/types/exam.types';
import { ExamCompletionScreen } from '@/components/organisms/ExamCompletion';
import { LoadingSpinner } from '@/components/molecules/LoadingSpinner';
import { ErrorDisplay } from '@/components/molecules/ErrorDisplay/ErrorDisplay';

export default function ExamCompletedPage() {
  const params = useParams();
  const router = useRouter();
  
  const examId = params.id as string;

  const [completionData, setCompletionData] = useState<IExamCompletionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCompletionData = async () => {
      if (!examId) {
        setError('Invalid exam ID.');
        setLoading(false);
        return;
      }

      try {
        // Fetch completion data by examId - this checks if the current user has completed this exam
        // and returns their completion data directly from the database
        const response = await getExamCompletionDataByExamIdAction(examId);
        if (response.status === 'success' && response.data) {
          setCompletionData(response.data);
        } else {
          // Handle error response - response.message exists on TApiError
          const errorMessage = response.status === 'error' ? response.message : 'No completion data found. Please submit the exam first.';
          setError(typeof errorMessage === 'string' ? errorMessage : 'An error occurred while loading completion data.');
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchCompletionData();
  }, [examId]);

  if (loading) {
    return <LoadingSpinner message="Loading completion data..." fullScreen />;
  }

  if (error || !completionData) {
    return (
      <ErrorDisplay
        error={error || 'Unable to load exam completion information'}
        title="Completion Data Not Found"
        description="Please make sure you have completed the exam before accessing this page."
        onRetry={() => router.push('/exam')}
        retryText="Go to Exams"
      />
    );
  }

  return <ExamCompletionScreen completionData={completionData} />;
}
