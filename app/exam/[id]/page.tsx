'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { getExamByIdAction } from '@/actions/exam.action';
import { IExamDetailResponse, IExamResponse } from '@/types/exam.types';
import { ExamTaking } from '@/components/organisms/ExamTaking/ExamTaking';
import { LoadingSpinner } from '@/components/molecules/LoadingSpinner';
import { ErrorDisplay } from '@/components/molecules/ErrorDisplay/ErrorDisplay';
import { Button } from '@/components/atoms/Button/Button';
import { TApiError } from '@/apis/transformResponse';

export default function ExamPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const examId = params.id as string;

  const [examData, setExamData] = useState<IExamDetailResponse | null>(null);
  const [exam, setExam] = useState<IExamResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const formatErrorMessage = (message: any): string => {
    if (typeof message === 'string') {
      return message;
    }
    if (Array.isArray(message)) {
      return message.map(err => err.constraints || 'Unknown error').join(', ');
    }
    return 'An unexpected error occurred.';
  };

  useEffect(() => {
    const fetchExam = async () => {
      if (!examId || status === 'loading') return;
      if (status === 'unauthenticated') {
        router.push('/auth/sign-in');
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const response = await getExamByIdAction(examId);

        if (response.status === 'success') {
          if (response.data) {
            setExamData(response.data);

            // Check if this is a student response and if they have completed the exam
            // If userResult exists and is not null, it means the student has already completed this exam
            if ('userResult' in response.data && response.data.userResult) {
              // Student has completed the exam, redirect to completion page instead of showing exam taking interface
              console.log('Student has completed exam, redirecting to completion page');
              router.push(`/exam/${examId}/completed`);
              return;
            }

            // Convert to legacy format for ExamTaking component
            const legacyExam: IExamResponse = {
              id: response.data.id,
              title: response.data.title,
              questions: response.data.questions || [],
              timeLimit: response.data.selectedOptions?.find((opt: any) => opt.key === 'timeLimit')?.value ?
                parseInt(response.data.selectedOptions.find((opt: any) => opt.key === 'timeLimit').value) : undefined,
              passingScore: response.data.selectedOptions?.find((opt: any) => opt.key === 'passingScore')?.value ?
                parseInt(response.data.selectedOptions.find((opt: any) => opt.key === 'passingScore').value) : undefined,
            };
            setExam(legacyExam);
          } else {
            setError('Exam not found.');
          }
        } else {
          setError(formatErrorMessage((response as TApiError).message) || 'Failed to load exam');
        }
      } catch (err: any) {
        setError(err.message || 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchExam();
  }, [examId, status, router]);

  if (loading) {
    return <div className="flex justify-center items-center h-screen"><LoadingSpinner /></div>;
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center h-screen">
        <ErrorDisplay error={error} />
        <Button onClick={() => window.location.reload()} className="mt-4">Try Again</Button>
      </div>
    );
  }

  if (!exam) {
    return <div className="text-center p-8">Exam not found.</div>;
  }

  return <ExamTaking exam={exam} />;
}
