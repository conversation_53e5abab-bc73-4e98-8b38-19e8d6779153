'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { getExamByIdAction } from '@/actions/exam.action';
import { IExamDetailResponse, IExamDetailStudentResponse } from '@/types/exam.types';
import { Button } from '@/components/atoms/Button/Button';
import { LoadingSpinner } from '@/components/molecules/LoadingSpinner';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import {
  ArrowLeft,
  CheckCircle2,
  XCircle,
  Trophy,
  Target,
  Clock,
  FileText,
  Eye,
  EyeOff,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/utils/cn';
import { formatExamDuration } from '@/utils/date';
import { TApiError } from '@/apis/transformResponse';
import { MathContentRenderer } from '@/components/molecules/MathContentRenderer/MathContentRenderer';

interface ExamResultDetail {
  questionIndex: number;
  isCorrect: boolean;
  userAnswer: string[];
  answer: string[];
  explain: string;
  content: string;
  type: string;
}

export default function ExamResultPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session, status } = useSession();

  const examId = params.id as string;
  const [examData, setExamData] = useState<IExamDetailStudentResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCorrectAnswers, setShowCorrectAnswers] = useState(false);
  const [expandedQuestions, setExpandedQuestions] = useState<Set<number>>(new Set());

  const formatErrorMessage = (message: any): string => {
    if (typeof message === 'string') {
      return message;
    }
    if (Array.isArray(message)) {
      return message.map(err => err.constraints || 'Unknown error').join(', ');
    }
    return 'An unexpected error occurred.';
  };

  useEffect(() => {
    const fetchExamResult = async () => {
      if (!examId || status === 'loading') return;
      
      if (status === 'unauthenticated') {
        router.push('/auth/sign-in');
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await getExamByIdAction(examId);

        if (response.status === 'success' && response.data) {
          // Check if this is a student response with userResult
          const studentResponse = response.data as IExamDetailStudentResponse;
          if ('userResult' in studentResponse) {
            if (studentResponse.userResult) {
              setExamData(studentResponse);
            } else {
              setError('You have not completed this exam yet.');
            }
          } else {
            setError('Access denied. This page is for students only.');
          }
        } else {
          setError(formatErrorMessage((response as TApiError).message) || 'Failed to load exam result');
        }
      } catch (err: any) {
        console.error('Error fetching exam result:', err);
        setError(err.message || 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchExamResult();
  }, [examId, status, router]);

  const toggleQuestionExpansion = (questionIndex: number) => {
    const newExpanded = new Set(expandedQuestions);
    if (newExpanded.has(questionIndex)) {
      newExpanded.delete(questionIndex);
    } else {
      newExpanded.add(questionIndex);
    }
    setExpandedQuestions(newExpanded);
  };

  const expandAllQuestions = () => {
    if (!examData?.userResult?.detail) return;
    const allQuestions = new Set(examData.userResult.detail.map((_: any, index: number) => index));
    setExpandedQuestions(allQuestions);
  };

  const collapseAllQuestions = () => {
    setExpandedQuestions(new Set());
  };

  const getQuestionTypeLabel = (type: string): string => {
    switch (type) {
      case 'single_choice':
        return 'Single Choice';
      case 'multiple_choice':
        return 'Multiple Choice';
      case 'fill_blank':
        return 'Fill in the Blank';
      case 'creative_writing':
        return 'Creative Writing';
      default:
        return type;
    }
  };

  const handleGoBack = () => {
    router.push('/manage-worksheet');
  };

  // Loading state
  if (loading || status === 'loading') {
    return <LoadingSpinner message="Loading exam result" fullScreen />;
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <AlertMessage
            type="error"
            title="Error"
            message={error}
          />
          <div className="mt-4 text-center">
            <Button
              onClick={handleGoBack}
              variant="secondary"
              className="inline-flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // No exam data
  if (!examData || !examData.userResult) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full text-center">
          <AlertMessage
            type="warning"
            title="No Results Found"
            message="You have not completed this exam yet."
          />
          <div className="mt-4">
            <Button
              onClick={handleGoBack}
              variant="secondary"
              className="inline-flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const userResult = examData.userResult;
  const resultDetails: ExamResultDetail[] = userResult.detail || [];
  const scorePercentage = (userResult.score / userResult.total) * 100;

  // Get passing score from exam selectedOptions, default to 70% if not found
  const passingScoreOption = examData.selectedOptions?.find((opt: any) => opt.key === 'passingScore');
  const passingScore = passingScoreOption ? parseInt(passingScoreOption.value) : 70;
  const passed = scorePercentage >= passingScore;

  const correctAnswers = resultDetails.filter(detail => detail.isCorrect).length;
  const totalQuestions = resultDetails.length;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto p-4 sm:p-6">
        {/* Header */}
        <div className="mb-6">
          <Button
            onClick={handleGoBack}
            variant="ghost"
            iconProps={{
              variant: 'arrow-left',
              className: 'w-4',
            }}
            className="mb-4 inline-flex items-center gap-2 text-gray-600 hover:text-gray-900"
          >
            Back to Exams
          </Button>
          
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className={cn(
                'w-12 h-12 rounded-full flex items-center justify-center',
                passed ? 'bg-green-100' : 'bg-red-100'
              )}>
                {passed ? (
                  <Trophy className="w-6 h-6 text-green-600" />
                ) : (
                  <Target className="w-6 h-6 text-red-600" />
                )}
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{examData.title}</h1>
                <p className="text-gray-600">Exam Results</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {/* Score */}
              <div className="text-center">
                <div className={cn(
                  'w-16 h-16 rounded-full mx-auto mb-2 flex items-center justify-center text-xl font-bold',
                  passed ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                )}>
                  {scorePercentage.toFixed(0)}%
                </div>
                <p className="text-sm font-medium text-gray-600">Score</p>
                <p className={cn(
                  'text-sm font-medium',
                  passed ? 'text-green-600' : 'text-red-600'
                )}>
                  {passed ? 'Passed' : 'Failed'}
                </p>
                <p className="text-xs text-gray-500">
                  Passing: {passingScore}%
                </p>
              </div>

              {/* Correct Answers */}
              <div className="text-center">
                <div className="w-16 h-16 rounded-full mx-auto mb-2 bg-blue-100 flex items-center justify-center text-xl font-bold text-blue-600">
                  {correctAnswers}
                </div>
                <p className="text-sm font-medium text-gray-600">Correct</p>
                <p className="text-sm text-gray-500">out of {totalQuestions}</p>
              </div>

              {/* Time Spent */}
              <div className="text-center">
                <div className="w-16 h-16 rounded-full mx-auto mb-2 bg-purple-100 flex items-center justify-center">
                  <Clock className="w-6 h-6 text-purple-600" />
                </div>
                <p className="text-sm font-medium text-gray-600">Time Spent</p>
                <p className="text-sm text-gray-500">
                  {userResult.timeSpent ? formatExamDuration(userResult.timeSpent) : '0s'}
                </p>
              </div>

              {/* Submitted */}
              <div className="text-center">
                <div className="w-16 h-16 rounded-full mx-auto mb-2 bg-gray-100 flex items-center justify-center">
                  <FileText className="w-6 h-6 text-gray-600" />
                </div>
                <p className="text-sm font-medium text-gray-600">Submitted</p>
                <p className="text-sm text-gray-500">
                  {new Date(userResult.submittedAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Question Details */}
        {resultDetails.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">Question Details</h2>
                <div className="flex items-center gap-2">
                  <Button
                    onClick={() => setShowCorrectAnswers(!showCorrectAnswers)}
                    variant="ghost"
                    className="inline-flex items-center gap-2"
                  >
                    {showCorrectAnswers ? (
                      <>
                        Hide Answers
                      </>
                    ) : (
                      <>
                        Show Answers
                      </>
                    )}
                  </Button>
                  
                  <Button
                    onClick={expandedQuestions.size === resultDetails.length ? collapseAllQuestions : expandAllQuestions}
                    variant="ghost"
                  >
                    {expandedQuestions.size === resultDetails.length ? 'Collapse All' : 'Expand All'}
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="p-6 space-y-4">
              {resultDetails.map((detail, index) => {
                const isExpanded = expandedQuestions.has(index);
                
                return (
                  <div
                    key={index}
                    className={cn(
                      'border rounded-lg transition-all duration-200',
                      detail.isCorrect 
                        ? 'border-green-200 bg-green-50' 
                        : 'border-red-200 bg-red-50'
                    )}
                  >
                    {/* Question Header */}
                    <div 
                      className="p-4 cursor-pointer hover:bg-opacity-80"
                      onClick={() => toggleQuestionExpansion(index)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="flex-shrink-0">
                            {detail.isCorrect ? (
                              <CheckCircle2 className="w-5 h-5 text-green-600" />
                            ) : (
                              <XCircle className="w-5 h-5 text-red-600" />
                            )}
                          </div>
                          
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="font-medium text-gray-900">
                                Question {index + 1}
                              </h3>
                              <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded">
                                {getQuestionTypeLabel(detail.type)}
                              </span>
                            </div>
                            <div className="text-sm text-gray-600 mt-1 line-clamp-2">
                              <MathContentRenderer
                                content={detail.content}
                                className="prose prose-sm max-w-none"
                                fallbackToText={true}
                              />
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <span className={cn(
                            'text-sm font-medium',
                            detail.isCorrect ? 'text-green-600' : 'text-red-600'
                          )}>
                            {detail.isCorrect ? 'Correct' : 'Incorrect'}
                          </span>
                          {isExpanded ? (
                            <ChevronDown className="w-4 h-4 text-gray-400" />
                          ) : (
                            <ChevronRight className="w-4 h-4 text-gray-400" />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Question Details */}
                    {isExpanded && (
                      <div className="px-4 pb-4 border-t border-gray-200">
                        <div className="pt-4 space-y-3">
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Question:</h4>
                            <MathContentRenderer
                              content={detail.content}
                              className="text-gray-700 prose prose-sm max-w-none"
                              fallbackToText={true}
                            />
                          </div>
                          
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Your Answer:</h4>
                            <div className="text-gray-700">
                              {detail.userAnswer.length > 0 ? (
                                <MathContentRenderer
                                  content={detail.userAnswer.join(', ')}
                                  className="prose prose-sm max-w-none"
                                  fallbackToText={true}
                                />
                              ) : (
                                <span>No answer provided</span>
                              )}
                            </div>
                          </div>
                          
                          {showCorrectAnswers && (
                            <div>
                              <h4 className="font-medium text-gray-900 mb-2">Correct Answer:</h4>
                              <div className="text-green-700 font-medium">
                                <MathContentRenderer
                                  content={detail.answer.join(', ')}
                                  className="prose prose-sm max-w-none"
                                  fallbackToText={true}
                                />
                              </div>
                            </div>
                          )}
                          
                          {showCorrectAnswers && detail.explain && (
                            <div>
                              <h4 className="font-medium text-gray-900 mb-2">Explanation:</h4>
                              <MathContentRenderer
                                content={detail.explain}
                                className="text-gray-700 prose prose-sm max-w-none"
                                fallbackToText={true}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
