'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { IExamSubmissionResponse, getCachedExamResultsAction } from '@/actions/exam.action';
import { Button } from '@/components/atoms/Button/Button';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { LoadingSpinner } from '@/components/molecules/LoadingSpinner';
import {
  ArrowLeft,
  CheckCircle2,
  XCircle,
  Trophy,
  Target,
  FileText
} from 'lucide-react';
import { cn } from '@/utils/cn';

export default function ExamResultsPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session, status } = useSession();

  const examId = params.id as string;
  const resultKey = searchParams.get('resultKey');
  const [results, setResults] = useState<IExamSubmissionResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load results from server cache or sessionStorage (fallback)
  useEffect(() => {
    const loadResults = async () => {
      if (status === 'loading') {
        return; // Wait for session to load
      }


      try {
        setLoading(true);
        setError(null);

        // First, try to load from server cache using resultKey
        if (resultKey) {
          const response = await getCachedExamResultsAction(resultKey);
          if (response.status === 'success' && response.data) {
            setResults(response.data);
            return;
          } else {
            console.warn('Failed to load from server cache:', response.message);
          }
        }

        // Fallback: try to load from sessionStorage (for backward compatibility)
        const storedResults = sessionStorage.getItem('examResult');
        if (storedResults) {
          const parsedResults = JSON.parse(storedResults);
          setResults(parsedResults);
          // Clear the stored results after loading
          sessionStorage.removeItem('examResult');
        } else {
          setError('No exam results found. Please take the exam first.');
        }
      } catch (err: any) {
        console.error('Error loading exam results:', err);
        setError(err.message || 'Failed to load exam results.');
      } finally {
        setLoading(false);
      }
    };

    loadResults();
  }, [status, router, resultKey]);

  // Handle back navigation
  const handleGoBack = () => {
    router.push('/manage-worksheet'); // Or wherever the user should go back to
  };

  // Handle retake exam
  const handleRetakeExam = () => {
    router.push(`/exam/${examId}`);
  };

  // Loading state
  if (loading || status === 'loading') {
    return <LoadingSpinner message="Loading results" fullScreen />;
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <div className="max-w-md w-full">
          <AlertMessage
            type="error"
            title="Error Loading Results"
            message={error}
            className="mb-4"
          />
          <Button
            variant="outline"
            onClick={handleGoBack}
            className="w-full"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  // No results
  if (!results) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <div className="max-w-md w-full text-center">
          <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">No Results Found</h2>
          <p className="text-gray-600 mb-4">
            We couldn&apos;t find any results for this exam.
          </p>
          <div className="space-y-2">
            <Button
              onClick={handleRetakeExam}
              className="w-full"
            >
              Take Exam
            </Button>
            <Button
              variant="outline"
              onClick={handleGoBack}
              className="w-full"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Calculate percentage
  const percentage = Math.round((results.score / results.total) * 100);
  const isPassingGrade = percentage >= 60; // Assuming 60% is passing

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto p-4 sm:p-6 lg:p-8">
        {/* Header */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={handleGoBack}
            className="text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
        </div>

        {/* Results Summary */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6">
            <div className="text-center">
              <div className={cn(
                'inline-flex items-center justify-center w-16 h-16 rounded-full mb-4',
                isPassingGrade ? 'bg-green-100' : 'bg-red-100'
              )}>
                {isPassingGrade ? (
                  <Trophy className="w-8 h-8 text-green-600" />
                ) : (
                  <Target className="w-8 h-8 text-red-600" />
                )}
              </div>
              
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Exam Complete!
              </h1>
              
              <div className="text-6xl font-bold mb-4">
                <span className={cn(
                  isPassingGrade ? 'text-green-600' : 'text-red-600'
                )}>
                  {percentage}%
                </span>
              </div>
              
              <div className="text-lg text-gray-600 mb-6">
                You scored {results.correct} out of {results.total} questions correctly
              </div>
              
              <div className={cn(
                'inline-flex items-center px-4 py-2 rounded-full text-sm font-medium',
                isPassingGrade 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              )}>
                {isPassingGrade ? 'Passed' : 'Needs Improvement'}
              </div>
            </div>
          </div>
        </div>

        {/* Detailed Results */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Question Breakdown
            </h2>
            
            <div className="space-y-4">
              {results.detail.map((detail, index) => (
                <div
                  key={index}
                  className={cn(
                    'p-4 rounded-lg border',
                    detail.isCorrect 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-red-50 border-red-200'
                  )}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      {detail.isCorrect ? (
                        <CheckCircle2 className="w-5 h-5 text-green-600" />
                      ) : (
                        <XCircle className="w-5 h-5 text-red-600" />
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium text-gray-900">
                          Question {detail.questionIndex + 1}
                        </h3>
                        <span className={cn(
                          'text-sm font-medium',
                          detail.isCorrect ? 'text-green-600' : 'text-red-600'
                        )}>
                          {detail.isCorrect ? 'Correct' : 'Incorrect'}
                        </span>
                      </div>
                      
                      <div className="text-sm text-gray-600 space-y-1">
                        <div>
                          <span className="font-medium">Your answer:</span>{' '}
                          {detail.userAnswer.join(', ') || 'No answer provided'}
                        </div>
                        {!detail.isCorrect && (
                          <div>
                            <span className="font-medium">Correct answer:</span>{' '}
                            {detail.answer.join(', ')}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={handleRetakeExam}
                variant="outline"
                className="min-w-32"
              >
                Retake Exam
              </Button>
              <Button
                onClick={handleGoBack}
                className="min-w-32"
              >
                Continue
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
