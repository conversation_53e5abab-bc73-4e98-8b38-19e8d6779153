'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { ExamList } from '@/components/organisms/ExamList/ExamList';
import { LoadingSpinner } from '@/components/molecules/LoadingSpinner';
import { Button } from '@/components/atoms/Button/Button';
import { ErrorBoundary } from '@/components/molecules/ErrorBoundary';
import { setupGlobalErrorHandling } from '@/utils/errorHandling';
import { BookOpen, GraduationCap } from 'lucide-react';

export default function StudentExamListPage() {
  const router = useRouter();
  const { status } = useSession();

  // Initialize global error handling
  useEffect(() => {
    setupGlobalErrorHandling();
  }, []);

  // Handle authentication
  useEffect(() => {
    if (status === 'loading') return; // Wait for session to load

    if (status === 'unauthenticated') {
      router.push('/auth/sign-in');
      return;
    }
  }, [status, router]);

  // Loading state while session is loading
  if (status === 'loading') {
    return (
      <div className="flex justify-center items-center min-h-screen bg-background-subtle">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 mx-auto bg-primary-action/10 rounded-full flex items-center justify-center">
            <GraduationCap className="w-6 h-6 text-primary-action" />
          </div>
          <LoadingSpinner />
          <p className="text-text-secondary text-sm">Loading your exams...</p>
        </div>
      </div>
    );
  }

  // Redirect if not authenticated (handled in useEffect, but this is a fallback)
  if (status === 'unauthenticated') {
    return (
      <div className="flex justify-center items-center min-h-screen bg-background-subtle">
        <div className="text-center max-w-md mx-auto px-6">
          <div className="w-16 h-16 mx-auto bg-primary-action/10 rounded-full flex items-center justify-center mb-6">
            <BookOpen className="w-8 h-8 text-primary-action" />
          </div>
          <h2 className="text-xl font-semibold text-text-primary mb-3">Authentication Required</h2>
          <p className="text-text-secondary mb-6 leading-relaxed">
            Please sign in to view your assigned exams and continue your learning journey.
          </p>
          <Button 
            onClick={() => router.push('/auth/sign-in')}
            className="w-full sm:w-auto"
          >
            Sign In to Continue
          </Button>
        </div>
      </div>
    );
  }

  // Main content with enhanced semantic structure and error boundary protection
  return (
    <ErrorBoundary
      level="page"
      context="StudentExamListPage"
      showDetails={true}
      enableReporting={true}
    >
      <main className="bg-background-subtle">
        {/* Enhanced page container with consistent design tokens */}
        <div className="container mx-auto max-w-7xl px-3 sm:px-4 md:px-6 lg:px-8">
          {/* Semantic page header with improved typography hierarchy */}
          <ErrorBoundary
            level="component"
            context="ExamPageHeader"
            fallback={
              <div className="p-4 bg-section-bg-neutral-alt border-b border-gray-200">
                <h1 className="text-2xl font-bold text-text-primary">My Exams</h1>
                <p className="text-text-secondary mt-1">View your assigned exams</p>
              </div>
            }
          >
            <header className="py-4 sm:py-6 md:py-8 lg:py-10">
              <div className="w-full">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between sm:gap-6">
                  {/* Icon and title group with enhanced visual hierarchy */}
                  <div className="flex items-start gap-3 sm:gap-4 md:gap-5 flex-1">
                    <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-primary-action rounded-full flex items-center justify-center">
                      <BookOpen className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 text-background-default" aria-hidden="true" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-text-primary leading-tight mb-2 sm:mb-3 md:mb-4">
                        My Exams
                      </h1>
                      <p className="text-sm sm:text-base md:text-large text-text-secondary leading-relaxed max-w-2xl">
                        View and take your assigned exams. Click on any exam to start or continue your learning journey.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </header>
          </ErrorBoundary>

          {/* Main content area with semantic structure */}
          <section 
            className="mt-4 sm:mt-6 md:mt-8" 
            aria-label="Exam list"
            role="main"
          >
            <ExamList
              tableTitle=""
              className="bg-background-default rounded-xl sm:rounded-2xl md:rounded-3xl shadow-sm min-h-[60vh] transition-all duration-300 ease-out"
            />
          </section>
        </div>
      </main>
    </ErrorBoundary>
  );
}
