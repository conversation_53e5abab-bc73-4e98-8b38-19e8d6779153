@import 'tailwindcss';

@plugin "daisyui" {
  themes: light --default;
};

@theme {
  /* Base Color Palette - Primary Colors */
  --color-black: #000000;
  --color-white: #ffffff;

  /* Blue Accent Palette */
  --color-blue-50: #eff6ff;
  --color-blue-100: #dbeafe;
  --color-blue-200: #bfdbfe;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-800: #1e40af;

  /* Gray Neutrals */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-600: #4b5563;
  --color-gray-800: #1f2937;

  /* Semantic Color Tokens */
  --color-primary-action: var(--color-blue-600);
  --color-background-default: var(--color-white);
  --color-link-default: var(--color-blue-600);
  --color-link-hover: var(--color-blue-500);
  --color-button-pill-bg: var(--color-blue-600);
  --color-button-pill-text: var(--color-white);
  --color-tag-bg: var(--color-blue-600);
  --color-tag-text: var(--color-white);
  --color-accent-bg-light: var(--color-blue-100);
  --color-section-bg-accent: var(--color-blue-50);
  --color-interactive-hover-bg-light: var(--color-blue-200);
  --color-text-primary: var(--color-gray-800);
  --color-text-secondary: var(--color-gray-600);
  --color-section-bg-neutral-alt: var(--color-gray-100);
  --color-background-subtle: var(--color-gray-50);

  /* Typography Scale */
  --text-small: 0.75rem;
  --text-sm: 0.875rem;
  --text-large: 1.125rem;
  --text-xlarge: 1.75rem;
}

@plugin "daisyui/theme" {
  name: 'light';
  default: true;

  /* Map DaisyUI theme colors to our new system */
  --color-primary: var(--color-primary-action);
  --color-primary-content: var(--color-background-default);
  --color-secondary: var(--color-text-secondary);
  --color-secondary-content: var(--color-background-default);
  --color-accent: var(--color-link-default);
  --color-accent-content: var(--color-background-default);
  --color-neutral: var(--color-text-primary);
  --color-neutral-content: var(--color-background-default);
  --color-base-100: var(--color-background-default);
  --color-base-200: var(--color-background-subtle);
  --color-base-300: var(--color-section-bg-neutral-alt);
  --color-base-content: var(--color-text-primary);
  --color-info: var(--color-blue-500);
  --color-info-content: var(--color-background-default);
  --color-success: #10b981;
  --color-success-content: var(--color-background-default);
  --color-warning: #f59e0b;
  --color-warning-content: var(--color-background-default);
  --color-error: #ef4444;
  --color-error-content: var(--color-background-default);
}

/* Custom sticky header with -16px offset */
.sticky-header-offset {
  position: sticky;
  top: -16px;
  z-index: 10;
}

/* Custom scrollbar styling */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--color-section-bg-neutral-alt);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--color-text-secondary);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-primary);
}

/* Hide scrollbar for Chrome, Safari and Opera */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* Enhanced shimmer animation for loading skeletons */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Skeleton pulse animation with better timing */
@keyframes skeletonPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Staggered fade-in animation for skeleton elements */
@keyframes staggeredFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced animations for exam cards */
@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes progressFill {
  0% {
    width: 0%;
  }
  100% {
    width: var(--progress-width);
  }
}

/* Button press animation */
@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

/* Floating animation for hover effects */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-4px);
  }
}

/* Gentle bounce for entrance animations */
@keyframes gentleBounce {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  60% {
    opacity: 1;
    transform: translateY(-5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) scale(1);
  }
}

/* Responsive grid transition utilities */
.grid-transition {
  transition: grid-template-columns 300ms ease-out, gap 300ms ease-out;
}

/* Enhanced touch interactions for mobile */
@media (hover: none) and (pointer: coarse) {
  .touch-optimized {
    /* Larger touch targets for mobile */
    min-height: 44px;
    min-width: 44px;
  }
  
  .touch-card {
    /* Optimized card interactions for touch devices */
    transition: transform 150ms ease-out, box-shadow 150ms ease-out;
  }
  
  .touch-card:active {
    transform: scale(0.98);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
}

/* Smooth viewport transitions */
@media (prefers-reduced-motion: no-preference) {
  .viewport-transition {
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .viewport-transition,
  .grid-transition,
  .touch-card {
    transition: none;
  }
  
  /* Disable animations for users who prefer reduced motion */
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  /* Keep essential functionality but remove decorative animations */
  .progress-bar-optimized,
  .exam-card-optimized {
    will-change: auto;
    transform: none;
  }
}

/* Animation utility classes */
.animate-entrance {
  animation: questionCardEntrance 0.5s ease-out;
}

.animate-slide-left {
  animation: slideInFromLeft 0.3s ease-out;
}

.animate-slide-right {
  animation: slideInFromRight 0.3s ease-out;
}

.animate-progress-fill {
  animation: progressBarSmooth 0.7s ease-out;
}

.animate-answer-select {
  animation: answerSelectPulse 0.3s ease-out;
}

.animate-answer-correct {
  animation: answerCorrectGlow 0.5s ease-out;
}

/* Hover animation utilities */
.hover-float:hover {
  animation: floatGentle 2s ease-in-out infinite;
}

.hover-scale:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease-out;
}

.active-press:active {
  animation: scalePress 0.15s ease-out;
}

/* Focus utilities for accessibility */
.focus-ring:focus {
  outline: none;
  ring: 4px;
  ring-color: rgba(59, 130, 246, 0.2);
  ring-offset: 2px;
}

/* Performance optimization utilities */
.gpu-accelerated {
  transform: translate3d(0, 0, 0);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.smooth-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.smooth-transition-slow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Line clamp utility for responsive text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Performance optimizations for skeleton loading */
.skeleton-optimized {
  /* Use transform3d to enable hardware acceleration */
  transform: translate3d(0, 0, 0);
  /* Optimize for animations */
  will-change: transform, opacity;
  /* Prevent layout shifts during loading */
  contain: layout style paint;
}

/* Performance optimizations for exam cards */
.exam-card-optimized {
  /* Enable hardware acceleration for smooth animations */
  transform: translate3d(0, 0, 0);
  /* Optimize for hover and click animations */
  will-change: transform, box-shadow, opacity;
  /* Prevent layout shifts during interactions */
  contain: layout style paint;
  /* Use GPU compositing for better performance */
  backface-visibility: hidden;
  /* Optimize rendering performance */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Optimized hover animations for 60fps performance */
.exam-card-hover {
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1),
              opacity 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.exam-card-hover:hover {
  transform: translate3d(0, -8px, 0) scale(1.02);
}

/* Optimized click feedback animation */
.exam-card-click {
  transition: transform 150ms cubic-bezier(0.4, 0, 0.6, 1);
}

.exam-card-click:active {
  transform: translate3d(0, 0, 0) scale(0.98);
}

/* Performance-optimized progress bar animation */
.progress-bar-optimized {
  transform: translate3d(0, 0, 0);
  will-change: width;
  contain: layout style paint;
}

/* Optimized staggered entrance animations */
.staggered-entrance {
  animation: optimizedSlideIn 500ms cubic-bezier(0.4, 0, 0.2, 1) both;
}

@keyframes optimizedSlideIn {
  0% {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Optimized fade-in transition for content after loading */
.content-fade-in {
  transition: opacity 300ms ease-out, transform 300ms ease-out;
  will-change: opacity, transform;
}

/* Staggered animation utility classes */
.stagger-1 { animation-delay: 100ms; }
.stagger-2 { animation-delay: 200ms; }
.stagger-3 { animation-delay: 300ms; }
.stagger-4 { animation-delay: 400ms; }
.stagger-5 { animation-delay: 500ms; }
.stagger-6 { animation-delay: 600ms; }
.stagger-7 { animation-delay: 700ms; }
.stagger-8 { animation-delay: 800ms; }

/* Enhanced shimmer animation with better performance */
@keyframes shimmerOptimized {
  0% {
    transform: translateX(-100%) translateZ(0);
    opacity: 0;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    transform: translateX(100%) translateZ(0);
    opacity: 0;
  }
}

/* Smooth loading state transition */
@keyframes loadingFadeIn {
  0% {
    opacity: 0;
    transform: translateY(8px) translateZ(0);
  }
  100% {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

/* Mobile-specific styles for exam navigation */
.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom, 0);
}

.safe-area-pt {
  padding-top: env(safe-area-inset-top, 0);
}

/* Responsive breakpoint utilities */
@media (max-width: 374px) {
  .xs\:inline {
    display: inline;
  }
  
  .xs\:hidden {
    display: none;
  }
}

/* Enhanced mobile touch interactions for exam navigation */
@media (hover: none) and (pointer: coarse) {
  .exam-nav-touch {
    /* Larger touch targets for navigation buttons */
    min-height: 48px;
    min-width: 48px;
  }
  
  .exam-nav-touch:active {
    transform: scale(0.95);
    transition: transform 100ms ease-out;
  }
  
  /* Optimized modal interactions for mobile */
  .exam-modal-touch {
    /* Prevent overscroll on mobile modals */
    overscroll-behavior: contain;
    /* Smooth scrolling for question overview */
    -webkit-overflow-scrolling: touch;
  }
}

/* Question navigation animations */
@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Progress bar smooth animation */
@keyframes progressBarFill {
  0% {
    width: 0%;
    opacity: 0.5;
  }
  100% {
    width: var(--progress-width);
    opacity: 1;
  }
}

/* Modal backdrop animation */
@keyframes backdropFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Modal slide up animation */
@keyframes modalSlideUp {
  0% {
    opacity: 0;
    transform: translateY(100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Question card entrance animation */
@keyframes questionCardEntrance {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced answer selection feedback animations */
@keyframes answerSelectPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0.1);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

@keyframes answerCorrectGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
  }
  50% {
    box-shadow: 0 0 0 6px rgba(34, 197, 94, 0.2);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

/* Smooth progress bar animation with easing */
@keyframes progressBarSmooth {
  0% {
    width: 0%;
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    width: var(--progress-width);
    opacity: 1;
  }
}

/* Enhanced sidebar entrance animation */
@keyframes sidebarSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Question navigation item hover animation */
@keyframes navItemHover {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(4px);
  }
}

/* Floating animation for interactive elements */
@keyframes floatGentle {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

/* Scale animation for button interactions */
@keyframes scalePress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.96);
  }
  100% {
    transform: scale(1);
  }
}

/* Ripple effect for answer selections */
@keyframes rippleEffect {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Smooth modal entrance */
@keyframes modalEntranceSmooth {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced loading pulse */
@keyframes loadingPulseEnhanced {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

/* Staggered fade-in for multiple elements */
@keyframes staggerFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Sidebar collapse animation */
@keyframes sidebarCollapse {
  0% {
    width: 320px;
    opacity: 1;
  }
  100% {
    width: 64px;
    opacity: 0.8;
  }
}

@keyframes sidebarExpand {
  0% {
    width: 64px;
    opacity: 0.8;
  }
  100% {
    width: 320px;
    opacity: 1;
  }
}
/* Additional responsive utilities for exam layout */

/* Safe area support for mobile devices */
.pb-safe-area-inset-bottom {
  padding-bottom: env(safe-area-inset-bottom, 0);
}

.pt-safe-area-inset-top {
  padding-top: env(safe-area-inset-top, 0);
}

/* Responsive breakpoint utilities for extra small screens */
@media (max-width: 374px) {
  .xs\:text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }
  
  .xs\:p-2 {
    padding: 0.5rem;
  }
  
  .xs\:gap-1 {
    gap: 0.25rem;
  }
  
  .xs\:min-h-\[40px\] {
    min-height: 40px;
  }
}

/* Enhanced touch interactions for exam components */
@media (hover: none) and (pointer: coarse) {
  .exam-touch-button {
    min-height: 48px;
    min-width: 48px;
    padding: 12px 16px;
  }
  
  .exam-touch-button:active {
    transform: scale(0.95);
    transition: transform 100ms ease-out;
  }
  
  .exam-touch-card {
    transition: transform 150ms ease-out, box-shadow 150ms ease-out;
  }
  
  .exam-touch-card:active {
    transform: scale(0.98);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  .exam-nav-item {
    min-height: 52px;
    padding: 16px;
  }
  
  .exam-nav-item:active {
    transform: scale(0.96);
    transition: transform 100ms ease-out;
  }
}

/* Responsive grid utilities for exam layout */
.exam-responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .exam-responsive-grid {
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .exam-responsive-grid {
    gap: 2rem;
    grid-template-columns: 280px 1fr;
  }
}

@media (min-width: 1280px) {
  .exam-responsive-grid {
    grid-template-columns: 320px 1fr;
  }
}

/* Responsive text scaling for exam content */
.exam-text-responsive {
  font-size: 0.875rem;
  line-height: 1.5;
}

@media (min-width: 640px) {
  .exam-text-responsive {
    font-size: 1rem;
    line-height: 1.6;
  }
}

@media (min-width: 1024px) {
  .exam-text-responsive {
    font-size: 1.125rem;
    line-height: 1.7;
  }
}

/* Responsive spacing utilities */
.exam-spacing-responsive {
  padding: 1rem;
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .exam-spacing-responsive {
    padding: 1.25rem;
    margin-bottom: 1.25rem;
  }
}

@media (min-width: 1024px) {
  .exam-spacing-responsive {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }
}

/* Smooth viewport transitions for layout changes */
@media (prefers-reduced-motion: no-preference) {
  .exam-viewport-transition {
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .exam-layout-transition {
    transition: grid-template-columns 300ms ease-out, 
                gap 300ms ease-out,
                padding 300ms ease-out;
  }
}

/* Performance optimizations for exam components */
.exam-gpu-accelerated {
  transform: translate3d(0, 0, 0);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.exam-smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* Responsive modal positioning */
.exam-modal-responsive {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  max-height: 85vh;
  border-radius: 1rem 1rem 0 0;
}

@media (min-width: 768px) {
  .exam-modal-responsive {
    bottom: 1rem;
    left: 1rem;
    right: 1rem;
    top: 1rem;
    max-height: none;
    border-radius: 1rem;
  }
}

/* Enhanced focus indicators for accessibility - WCAG AA compliant */
.exam-focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.6);
  border-radius: 0.375rem;
}

.exam-focus-ring:focus-visible {
  outline: 3px solid rgba(59, 130, 246, 0.8);
  outline-offset: 2px;
}

/* High contrast focus indicators for better visibility */
.exam-focus-high-contrast:focus,
.exam-focus-high-contrast:focus-visible {
  outline: 3px solid #0066cc;
  outline-offset: 2px;
  box-shadow: 0 0 0 1px #ffffff, 0 0 0 4px #0066cc;
}

/* Skip link for keyboard navigation */
.exam-skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  font-weight: 600;
  transition: top 0.3s;
}

.exam-skip-link:focus {
  top: 6px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus trap for modals */
.focus-trap {
  position: relative;
}

.focus-trap:before,
.focus-trap:after {
  content: '';
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* Keyboard navigation indicators */
.keyboard-navigation-active .exam-nav-item:focus {
  background-color: rgba(59, 130, 246, 0.1);
  border: 2px solid rgba(59, 130, 246, 0.8);
  outline: none;
}

.keyboard-navigation-active .exam-button:focus {
  transform: scale(1.05);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.6);
}

/* Enhanced button focus states */
.exam-button-focus:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.6);
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.exam-button-focus:focus:not(:focus-visible) {
  box-shadow: none;
  transform: none;
}

/* Progress announcement styles */
.progress-announcement {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .exam-focus-ring:focus,
  .exam-focus-ring:focus-visible {
    outline: 4px solid #000;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px #fff, 0 0 0 6px #000;
  }
  
  .exam-button-focus:focus {
    outline: 4px solid #000;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px #fff, 0 0 0 6px #000;
  }
  
  /* Ensure sufficient contrast for all interactive elements */
  .exam-nav-item,
  .exam-question-card,
  .exam-answer-option {
    border: 2px solid #000;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .exam-button-focus:focus {
    transform: none;
    transition: none;
  }
  
  .keyboard-navigation-active .exam-button:focus {
    transform: none;
  }
  
  /* Disable all decorative animations but keep functional ones */
  .exam-card-hover:hover {
    transform: none;
  }
  
  .exam-nav-item:hover {
    transform: none;
  }
  
  /* Keep essential feedback but remove motion */
  .exam-answer-select {
    animation: none;
    background-color: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.8);
  }
}

/* Responsive button sizing */
.exam-button-responsive {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  min-height: 40px;
}

@media (max-width: 640px) {
  .exam-button-responsive {
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
    min-height: 48px;
  }
}

@media (hover: none) and (pointer: coarse) {
  .exam-button-responsive {
    min-height: 48px;
    min-width: 48px;
  }
}