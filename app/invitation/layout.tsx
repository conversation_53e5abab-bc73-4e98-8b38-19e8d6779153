import AuthTemplate from '@/components/templates/AuthTemplate/AuthTemplate';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Accept Invitation | EduSG',
  description: 'Complete your registration and join your school on EduSG',
};

export default function InvitationLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return <AuthTemplate>{children}</AuthTemplate>;
}
