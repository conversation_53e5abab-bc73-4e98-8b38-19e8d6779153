'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { AlertTriangle } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { LoadingSpinner } from '@/components/molecules/LoadingSpinner/LoadingSpinner';
import { validateInvitationAction } from '@/actions/invitation.action';
import { IValidateInvitationResponse } from '@/apis/invitation';
import AcceptInvitationForm from '@/components/organisms/Forms/AcceptInvitationForm/AcceptInvitationForm';


type PageState = 'loading' | 'form' | 'error';

export default function InvitationPage() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  
  const [state, setState] = useState<PageState>('loading');
  const [invitationData, setInvitationData] = useState<IValidateInvitationResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const validateToken = async () => {
      if (!token) {
        setError('No invitation token provided. Please check your invitation link.');
        setState('error');
        return;
      }

      try {
        setState('loading');
        const response = await validateInvitationAction(token);
        
        if (response.status === 'success' && response.data) {
          setInvitationData(response.data);
          setState('form');
        } else {
          // Handle error response
          const errorMessage = response.status === 'error' && typeof response.message === 'string'
            ? response.message
            : 'Invalid or expired invitation token.';
          setError(errorMessage);
          setState('error');
        }
      } catch (err: any) {
        console.error('Error validating invitation:', err);
        setError('An unexpected error occurred while validating your invitation.');
        setState('error');
      }
    };

    validateToken();
  }, [token]);

  // Loading state
  if (state === 'loading') {
    return (
      <LoadingSpinner 
        message="Validating your invitation..." 
        size="lg" 
        fullScreen 
      />
    );
  }

  // Error state
  if (state === 'error') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background-default p-4">
        <div className="w-full max-w-md mx-auto text-center">
          <div className="bg-background-subtle border border-gray-200 rounded-xl p-8">
            {/* Error Icon */}
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <AlertTriangle size={24} className="text-red-500" />
              </div>
            </div>

            {/* Error Content */}
            <h1 className="text-2xl font-bold text-text-primary mb-4">
              Invitation Invalid or Expired
            </h1>
            <p className="text-text-secondary mb-6">
              {error || 'This invitation link is no longer valid. Please contact your administrator or teacher to request a new invitation.'}
            </p>

            {/* Action Button */}
            <Button
              variant="primary"
              href="/auth/sign-in"
              className="w-full"
            >
              Return to Sign In
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Form state
  if (state === 'form' && invitationData) {
    return (
      <AcceptInvitationForm 
        invitationData={invitationData}
        token={token!}
      />
    );
  }

  // Fallback (should not reach here)
  return null;
}
