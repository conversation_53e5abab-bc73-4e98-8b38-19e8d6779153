'use client';

import React, { useState } from 'react';
import { IStudentResponse } from '@/apis/userApi';
import { ISchoolResponse } from '@/apis/schoolApi';
import { StudentListTable } from '@/components/organisms/StudentListTable/StudentListTable';
import { useSession } from 'next-auth/react';
import { EUserRole } from '@/config/enums/user';
import { FormField } from '@/components/molecules/FormField/FormField';
import { Button } from '@/components/atoms/Button/Button';
import { InviteStudentModal } from '@/components/molecules';
import { PlusIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface StudentManagementClientProps {
  students: IStudentResponse[];
  schools: ISchoolResponse[];
  initialSchoolId?: string;
}

export const StudentManagementClient: React.FC<StudentManagementClientProps> = ({
  students,
  schools,
  initialSchoolId,
}) => {
  const { data: session } = useSession();
  const router = useRouter();
  const [selectedSchool, setSelectedSchool] = useState(initialSchoolId || '');
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);

  const handleSchoolChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const schoolId = e.target.value;
    setSelectedSchool(schoolId);
    router.push(`/student-management${schoolId ? `?schoolId=${schoolId}` : ''}`);
  };

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Student Management</h1>
        <Button 
          onClick={() => setIsInviteModalOpen(true)}
          iconProps={{ variant: 'plus', className: 'w-5' }}
        >
          Add Student
        </Button>
      </div>
      {session?.user?.role === EUserRole.ADMIN && (
        <div className="mb-4">
          <FormField label="Filter by School">
            <select
              value={selectedSchool}
              onChange={handleSchoolChange}
              className="select select-bordered w-full max-w-xs"
            >
              <option value="">All Schools</option>
              {schools.map((school) => (
                <option key={school.id} value={school.id}>
                  {school.name}
                </option>
              ))}
            </select>
          </FormField>
        </div>
      )}
      <StudentListTable
        students={students}
        isLoading={false}
        error={null}
      />

      <InviteStudentModal
        isOpen={isInviteModalOpen}
        onClose={() => setIsInviteModalOpen(false)}
        onSuccess={() => {
          // Optionally refresh the page or refetch students
          window.location.reload();
        }}
      />
    </div>
  );
}; 