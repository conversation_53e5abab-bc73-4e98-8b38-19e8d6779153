'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { handleGetStudentDetailByIdAction } from '@/actions/user.action';
import { IStudentDetailResponse } from '@/apis/userApi';
import { Button } from '@/components/atoms/Button/Button';
import { LoadingSpinner } from '@/components/molecules/LoadingSpinner';
import { ErrorDisplay } from '@/components/molecules/ErrorDisplay/ErrorDisplay';
import { StudentDetailView } from '@/components/organisms/StudentDetailView/StudentDetailView';
import BottomDock from '@/components/organisms/BottomDock/BottomDock';
import { 
  User, 
  BookOpen,
  ArrowLeft
} from 'lucide-react';
import { cn } from '@/utils/cn';
import { TApiError } from '@/apis/transformResponse';
import { APP_ROUTE } from '@/constants/route.constant';

export default function StudentDetailPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const studentId = params.id as string;
  
  const [student, setStudent] = useState<IStudentDetailResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const formatErrorMessage = (message: any): string => {
    if (typeof message === 'string') {
      return message;
    }
    if (Array.isArray(message)) {
      return message.map(err => err.constraints || 'Unknown error').join(', ');
    }
    return 'An unexpected error occurred.';
  };

  useEffect(() => {
    const fetchStudentDetail = async () => {
      if (!studentId || status === 'loading') return;
      
      if (status === 'unauthenticated') {
        router.push('/auth/sign-in');
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await handleGetStudentDetailByIdAction(studentId);

        if (response.status === 'success') {
          if (response.data) {
            setStudent(response.data);
          } else {
            setError('No student details found.');
          }
        } else {
          setError(formatErrorMessage((response as TApiError).message) || 'Failed to load student details');
        }
      } catch (err: any) {
        console.error('Error fetching student detail:', err);
        setError(err.message || 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchStudentDetail();
  }, [studentId, status, router]);

  const handleGoBack = () => {
    router.push(APP_ROUTE.STUDENT_MANAGEMENT.INDEX);
  };



  if (loading) {
    return (
      <div className="min-h-screen bg-background-subtle flex items-center justify-center p-4">
        <div className="bg-background-default rounded-2xl p-8 shadow-md w-full max-w-md">
          <div className="py-12 flex flex-col items-center">
            <LoadingSpinner />
            <p className="text-text-secondary mt-4">Loading student details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background-subtle p-4">
        <div className="max-w-md mx-auto pt-20">
          <div className="bg-background-default rounded-2xl p-6 shadow-md">
            <ErrorDisplay error={error} />
            <Button onClick={() => window.location.reload()} className="mt-4 w-full" variant="primary">
              Try Again
            </Button>
          </div>
        </div>

        {/* Bottom Dock for all devices */}
        <BottomDock className="justify-start">
          <Button
            variant="outline"
            onClick={handleGoBack}
            className="w-12 h-12 rounded-full p-0 bg-white border-2 border-primary-action text-primary-action hover:bg-primary-action hover:text-white shadow-lg transition-all duration-200"
            aria-label="Back to student management"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
        </BottomDock>
      </div>
    );
  }

  if (!student) {
    return (
      <div className="min-h-screen bg-background-subtle p-4">
        <div className="max-w-md mx-auto pt-20">
          <div className="bg-background-default rounded-2xl p-6 shadow-md text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-section-bg-accent rounded-full flex items-center justify-center">
              <User className="w-8 text-primary-action" />
            </div>
            <h3 className="text-lg font-semibold text-text-primary mb-2">Student Not Found</h3>
            <p className="text-text-secondary">No details found for this student.</p>
          </div>
        </div>

        {/* Bottom Dock for all devices */}
        <BottomDock className="justify-start">
          <Button
            variant="outline"
            onClick={handleGoBack}
            className="w-12 h-12 rounded-full p-0 bg-white border-2 border-primary-action text-primary-action hover:bg-primary-action hover:text-white shadow-lg transition-all duration-200"
            aria-label="Back to student management"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
        </BottomDock>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background-subtle pb-20">
      {/* Mobile Layout: Single Column */}
      <div className="lg:hidden max-w-4xl mx-auto p-4 space-y-5">
        {/* Student Header Card */}
        <div className="bg-background-default rounded-2xl overflow-hidden border-none shadow-md">
          <div className="h-3 bg-gradient-to-r from-primary-action to-primary-action/70"></div>
          <div className="p-5">
            <div className="flex items-start justify-between gap-4 mb-5">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-14 h-14 bg-primary-action/10 rounded-full flex items-center justify-center">
                  <User className="w-7 h-7 text-primary-action" />
                </div>
                <div className="min-w-0 flex-1">
                  <h1 className="text-xl font-bold text-text-primary break-words mb-1">
                    {student.name}
                  </h1>
                  <p className="text-sm text-text-secondary break-all leading-relaxed">
                    <BookOpen className="w-3.5 h-3.5 inline mr-1.5 opacity-70" />
                    {student.email}
                  </p>
                </div>
              </div>

            </div>

            {/* Exam Results Summary */}
            <div className="bg-gray-50 rounded-xl p-4 border">
              <div className="text-center">
                <div className="text-2xl font-bold text-text-primary mb-1">
                  {student.examResults.length}
                </div>
                <p className="text-sm text-text-secondary font-medium">Total Exams</p>
              </div>
            </div>
          </div>
        </div>

        {/* Student Detail View */}
        <div className="bg-background-default rounded-2xl shadow-md border-none">
          <StudentDetailView student={student} />
        </div>
      </div>

      {/* Desktop Layout: Two Columns */}
      <div className="hidden lg:block max-w-7xl mx-auto p-6">
        <div className="grid lg:grid-cols-12 gap-6 h-full">
          {/* Left Column - Student Overview */}
          <div className="lg:col-span-4 space-y-6">
            {/* Student Header Card */}
            <div className="bg-background-default rounded-2xl shadow-md border-none overflow-hidden sticky top-6">
              <div className="h-3 bg-gradient-to-r from-primary-action to-primary-action/70"></div>
              <div className="p-6">
                <div className="flex flex-col gap-5">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="flex-shrink-0 w-16 h-16 bg-primary-action/10 rounded-full flex items-center justify-center">
                        <User className="w-8 h-8 text-primary-action" />
                      </div>
                      <div>
                        <h1 className="text-2xl font-bold text-text-primary">
                          {student.name}
                        </h1>
                        <p className="text-base text-text-secondary break-all leading-relaxed">
                          <BookOpen className="w-4 h-4 inline mr-2 opacity-70" />
                          {student.email}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Exam Summary */}
                  <div className="rounded-xl p-6 border bg-gray-50">
                    <div className="text-center">
                      <div className="text-4xl font-bold text-text-primary mb-2">
                        {student.examResults.length}
                      </div>
                      <p className="text-sm text-text-secondary font-medium">Total Exams Completed</p>
                    </div>
                  </div>


                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Student Details */}
          <div className="lg:col-span-8">
            <div className="bg-background-default rounded-2xl shadow-md border-none">
              <StudentDetailView student={student} />
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Dock for all devices */}
      <BottomDock className="justify-start lg:left-[322px] z-[-1] lg:w-[calc(100vw-330px)]">
        <Button
          variant="outline"
          onClick={handleGoBack}
          className="w-10 h-10 rounded-full p-0 bg-white border-2"
          aria-label="Back to student management"
        >
          <ArrowLeft className="w-4 h-4" />
        </Button>
      </BottomDock>
    </div>
  );
} 