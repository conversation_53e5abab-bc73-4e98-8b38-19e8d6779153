import { onSeverSession } from '@/config/auth';
import { getStudentList, IStudentResponse } from '@/apis/userApi';
import { getAllSchools, ISchoolResponse } from '@/apis/schoolApi';
import { EUserRole } from '@/config/enums/user';
import { StudentManagementClient } from './StudentManagementClient';

export default async function StudentManagementPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  const session = await onSeverSession();
  const schoolId =
    typeof searchParams.schoolId === 'string' ? searchParams.schoolId : undefined;

  const studentsResponse = await getStudentList(
    session?.user?.role === EUserRole.ADMIN ? schoolId : session?.user?.schoolId || undefined
  );
  const students = studentsResponse.status === 'success' ? studentsResponse.data : [];

  let schools: ISchoolResponse[] = [];
  if (session?.user?.role === EUserRole.ADMIN) {
    const schoolsResponse = await getAllSchools();
    if (schoolsResponse.status === 'success') {
      schools = schoolsResponse.data || [];
    }
  }

  return (
    <StudentManagementClient
      students={students as IStudentResponse[]}
      schools={schools}
      initialSchoolId={schoolId}
    />
  );
} 