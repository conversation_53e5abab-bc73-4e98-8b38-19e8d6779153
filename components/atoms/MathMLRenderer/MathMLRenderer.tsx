'use client';

import React, { useEffect, useState, useRef } from 'react';
import { cn } from '@/utils/cn';

export interface MathMLRendererProps {
  content: string;
  className?: string;
  fallbackToText?: boolean;
  onRenderError?: (error: Error) => void;
}

/**
 * MathMLRenderer component that renders MathML content with browser support detection
 * and graceful fallback for unsupported browsers.
 */
export const MathMLRenderer: React.FC<MathMLRendererProps> = ({
  content,
  className,
  fallbackToText = true,
  onRenderError,
}) => {
  const [hasMathMLSupport, setHasMathMLSupport] = useState<boolean | null>(null);
  const [renderError, setRenderError] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Check for MathML support on component mount
  useEffect(() => {
    const checkMathMLSupport = () => {
      try {
        // Create a test MathML element
        const testMath = document.createElement('math');
        testMath.innerHTML = '<mrow><mi>x</mi></mrow>';
        
        // Check if the browser recognizes MathML elements
        const supportsNamespace = testMath.namespaceURI === 'http://www.w3.org/1998/Math/MathML';
        const supportsMathMLInterface = 'MathMLElement' in window;
        
        // Additional check: see if MathML renders differently than regular text
        document.body.appendChild(testMath);
        const mathHeight = testMath.offsetHeight;
        document.body.removeChild(testMath);
        
        const textSpan = document.createElement('span');
        textSpan.textContent = 'x';
        document.body.appendChild(textSpan);
        const textHeight = textSpan.offsetHeight;
        document.body.removeChild(textSpan);
        
        // MathML is supported if it has namespace support OR MathML interface OR renders differently
        const isSupported = supportsNamespace || supportsMathMLInterface || (mathHeight !== textHeight);
        
        setHasMathMLSupport(isSupported);
        return isSupported;
      } catch (error) {
        console.warn('Error checking MathML support:', error);
        setHasMathMLSupport(false);
        return false;
      }
    };

    checkMathMLSupport();
  }, []);

  // Process content to ensure proper MathML rendering
  const processContent = (htmlContent: string): string => {
    try {
      // Ensure MathML elements have proper namespace
      let processedContent = htmlContent.replace(
        /<math(?!\s[^>]*xmlns)/gi,
        '<math xmlns="http://www.w3.org/1998/Math/MathML"'
      );

      // Clean up any malformed MathML
      processedContent = processedContent.replace(
        /&amp;#x([0-9A-Fa-f]+);/g,
        '&#x$1;'
      );

      return processedContent;
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Content processing failed');
      setRenderError(err.message);
      onRenderError?.(err);
      return htmlContent;
    }
  };

  // Extract text content from MathML for fallback
  const extractTextFromMathML = (htmlContent: string): string => {
    try {
      // Create a temporary DOM element to parse the content
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlContent;
      
      // Find all MathML elements and replace with readable text
      const mathElements = tempDiv.querySelectorAll('math');
      mathElements.forEach(mathEl => {
        const textContent = extractMathText(mathEl);
        const textSpan = document.createElement('span');
        textSpan.className = 'math-fallback';
        textSpan.textContent = textContent;
        mathEl.parentNode?.replaceChild(textSpan, mathEl);
      });
      
      return tempDiv.innerHTML;
    } catch (error) {
      console.warn('Error extracting text from MathML:', error);
      return htmlContent;
    }
  };

  // Helper function to extract readable text from MathML elements
  const extractMathText = (mathElement: Element): string => {
    const textParts: string[] = [];
    
    const processNode = (node: Node) => {
      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent?.trim();
        if (text) textParts.push(text);
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as Element;
        const tagName = element.tagName.toLowerCase();
        
        // Add appropriate spacing and symbols based on MathML elements
        switch (tagName) {
          case 'mo': // Operators
            const op = element.textContent?.trim();
            if (op) {
              // Add spaces around operators for readability
              textParts.push(` ${op} `);
            }
            break;
          case 'mfrac': // Fractions
            const numerator = element.querySelector('*:first-child')?.textContent?.trim();
            const denominator = element.querySelector('*:last-child')?.textContent?.trim();
            if (numerator && denominator) {
              textParts.push(`(${numerator})/(${denominator})`);
            }
            break;
          case 'msup': // Superscripts
            const base = element.querySelector('*:first-child')?.textContent?.trim();
            const superscript = element.querySelector('*:last-child')?.textContent?.trim();
            if (base && superscript) {
              textParts.push(`${base}^${superscript}`);
            }
            break;
          case 'msub': // Subscripts
            const baseEl = element.querySelector('*:first-child')?.textContent?.trim();
            const subscript = element.querySelector('*:last-child')?.textContent?.trim();
            if (baseEl && subscript) {
              textParts.push(`${baseEl}_${subscript}`);
            }
            break;
          case 'msqrt': // Square roots
            const radicand = element.textContent?.trim();
            if (radicand) {
              textParts.push(`√(${radicand})`);
            }
            break;
          default:
            // For other elements, process children recursively
            Array.from(element.childNodes).forEach(processNode);
            break;
        }
      }
    };
    
    Array.from(mathElement.childNodes).forEach(processNode);
    return textParts.join('').replace(/\s+/g, ' ').trim();
  };

  // Render loading state while checking support
  if (hasMathMLSupport === null) {
    return (
      <div className={cn('animate-pulse', className)}>
        <div className="h-6 bg-gray-200 rounded"></div>
      </div>
    );
  }

  // Render error state
  if (renderError) {
    return (
      <div className={cn('text-red-600 bg-red-50 p-2 rounded border border-red-200', className)}>
        <p className="text-sm font-medium">Math Rendering Error</p>
        <p className="text-xs">{renderError}</p>
        {fallbackToText && (
          <div 
            className="mt-2 text-gray-700"
            dangerouslySetInnerHTML={{ __html: extractTextFromMathML(content) }}
          />
        )}
      </div>
    );
  }

  // Render with MathML support
  if (hasMathMLSupport) {
    return (
      <div
        ref={containerRef}
        className={cn('mathml-content', className)}
        dangerouslySetInnerHTML={{ __html: processContent(content) }}
        style={{
          // Ensure proper MathML styling
          fontFamily: 'STIX Two Math, Latin Modern Math, Cambria Math, serif',
        }}
      />
    );
  }

  // Fallback for browsers without MathML support
  if (fallbackToText) {
    return (
      <div className={cn('math-fallback bg-yellow-50 border border-yellow-200 rounded p-2', className)}>
        <div className="text-xs text-yellow-700 mb-1 font-medium">
          Mathematical Expression (Browser doesn&apos;t support MathML)
        </div>
        <div 
          className="text-gray-800 font-mono text-sm"
          dangerouslySetInnerHTML={{ __html: extractTextFromMathML(content) }}
        />
      </div>
    );
  }

  // No fallback - render as-is
  return (
    <div
      className={cn('mathml-unsupported', className)}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};