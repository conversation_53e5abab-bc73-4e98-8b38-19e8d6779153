'use client';

import React from 'react';
import { cn } from '@/utils/cn';
import { useViewport, responsiveClasses } from '@/utils/responsive';

export interface ProgressIndicatorProps {
  currentQuestion: number;
  totalQuestions: number;
  answeredQuestions: number;
  timeRemaining?: number; // in minutes
  timeLimit?: number; // in minutes
  showQuestionDots?: boolean;
  className?: string;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  currentQuestion,
  totalQuestions,
  answeredQuestions,
  timeRemaining,
  timeLimit,
  showQuestionDots = true,
  className,
}) => {
  const { isMobile, isTablet } = useViewport();
  
  // Calculate progress percentage
  const progressPercentage = Math.round((answeredQuestions / totalQuestions) * 100);
  
  // Calculate time progress if time limit is provided
  const timeProgressPercentage = timeLimit && timeRemaining 
    ? Math.max(0, Math.round(((timeLimit - timeRemaining) / timeLimit) * 100))
    : 0;

  return (
    <div className={cn('space-y-4', className)}>
      {/* Main Progress Bar */}
      <div className={cn('space-y-2', responsiveClasses.spacing.sectionGap)}>
        <div className={cn(
          'flex justify-between items-center',
          responsiveClasses.text.metadata
        )}>
          <span className="text-text-secondary">Progress</span>
          <span className="text-text-primary font-medium">
            {isMobile ? `${answeredQuestions}/${totalQuestions}` : `${answeredQuestions}/${totalQuestions} questions`} ({progressPercentage}%)
          </span>
        </div>
        <div className="relative">
          <div className={cn(
            'w-full bg-gray-200 rounded-full overflow-hidden',
            // Responsive height - smaller on mobile
            isMobile ? 'h-2' : 'h-3'
          )}>
            <div 
              className="h-full bg-gradient-to-r from-primary-action to-blue-500 rounded-full transition-all duration-700 ease-out progress-bar-optimized"
              style={{ 
                width: `${progressPercentage}%`,
                '--progress-width': `${progressPercentage}%`
              } as React.CSSProperties}
              role="progressbar"
              aria-valuenow={progressPercentage}
              aria-valuemin={0}
              aria-valuemax={100}
              aria-label={`Exam progress: ${progressPercentage}% complete`}
            />
          </div>
          
          {/* Progress indicator dots - hide on mobile if too many questions */}
          {showQuestionDots && !(isMobile && totalQuestions > 10) && (
            <div className="absolute top-0 left-0 w-full h-full flex items-center">
              {Array.from({ length: totalQuestions }, (_, index) => {
                const questionNumber = index + 1;
                const isAnswered = index < answeredQuestions;
                const isCurrent = questionNumber === currentQuestion;
                const position = totalQuestions === 1 ? 50 : (index / (totalQuestions - 1)) * 100;
                
                return (
                  <div
                    key={index}
                    className="absolute transform -translate-x-1/2"
                    style={{ left: `${position}%` }}
                  >
                    <div
                      className={cn(
                        'rounded-full border-2 transition-all duration-300',
                        // Responsive dot size
                        isMobile ? 'w-2 h-2' : 'w-3 h-3',
                        {
                          'bg-primary-action border-primary-action': isAnswered,
                          'bg-background-default border-primary-action ring-2 ring-primary-action ring-offset-1': isCurrent && !isAnswered,
                          'bg-background-default border-gray-300': !isAnswered && !isCurrent,
                        }
                      )}
                      title={`Question ${questionNumber}${isAnswered ? ' (answered)' : ''}${isCurrent ? ' (current)' : ''}`}
                    />
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Time Progress Bar (if time limit exists) */}
      {timeLimit && timeRemaining !== undefined && (
        <div className="space-y-2">
          <div className="flex justify-between items-center text-sm">
            <span className="text-text-secondary">Time Used</span>
            <span className={cn(
              'font-medium',
              {
                'text-red-600': timeProgressPercentage > 90,
                'text-amber-600': timeProgressPercentage > 75 && timeProgressPercentage <= 90,
                'text-text-secondary': timeProgressPercentage <= 75,
              }
            )}>
              {Math.round(timeLimit - timeRemaining)}m / {timeLimit}m
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
            <div 
              className={cn(
                'h-full rounded-full transition-all duration-1000 ease-linear',
                {
                  'bg-gradient-to-r from-green-400 to-green-500': timeProgressPercentage <= 50,
                  'bg-gradient-to-r from-amber-400 to-amber-500': timeProgressPercentage > 50 && timeProgressPercentage <= 75,
                  'bg-gradient-to-r from-red-400 to-red-500': timeProgressPercentage > 75,
                }
              )}
              style={{ width: `${timeProgressPercentage}%` }}
              role="progressbar"
              aria-valuenow={timeProgressPercentage}
              aria-valuemin={0}
              aria-valuemax={100}
              aria-label={`Time used: ${timeProgressPercentage}% of total time`}
            />
          </div>
        </div>
      )}
    </div>
  );
};