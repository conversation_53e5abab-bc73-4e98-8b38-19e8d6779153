import React from 'react';
import { cn } from '@/utils/cn';

export type AssignmentStatus = 'assigned' | 'in_progress' | 'completed';

interface AssignmentStatusBadgeProps {
  status: AssignmentStatus;
  className?: string;
}

export const AssignmentStatusBadge: React.FC<AssignmentStatusBadgeProps> = ({ status, className }) => {
  const getStatusStyles = (status: AssignmentStatus) => {
    switch (status) {
      case 'assigned':
        return 'bg-gray-100 text-gray-700';
      case 'in_progress':
        return 'bg-blue-100 text-blue-700';
      case 'completed':
        return 'bg-green-100 text-green-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusLabel = (status: AssignmentStatus) => {
    switch (status) {
      case 'assigned':
        return 'Assigned';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      default:
        return status;
    }
  };

  return (
    <span className={cn(
      'px-2 py-1 rounded-full text-xs font-medium',
      getStatusStyles(status),
      className
    )}>
      {getStatusLabel(status)}
    </span>
  );
}; 