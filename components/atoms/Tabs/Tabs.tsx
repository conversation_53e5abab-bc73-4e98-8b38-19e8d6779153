'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { cn } from '@/utils/cn';

// Tab Context
interface TabContextType {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const TabContext = createContext<TabContextType | undefined>(undefined);

const useTabContext = () => {
  const context = useContext(TabContext);
  if (!context) {
    throw new Error('Tab components must be used within a TabContainer');
  }
  return context;
};

// Tab Container Component
interface TabContainerProps {
  children: ReactNode;
  defaultTab: string;
  className?: string;
}

export const TabContainer: React.FC<TabContainerProps> = ({
  children,
  defaultTab,
  className
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab);

  return (
    <TabContext.Provider value={{ activeTab, setActiveTab }}>
      <div className={cn('w-full', className)}>
        {children}
      </div>
    </TabContext.Provider>
  );
};

// Tab List Component
interface TabListProps {
  children: ReactNode;
  className?: string;
}

export const TabList: React.FC<TabListProps> = ({ children, className }) => {
  return (
    <div
      className={cn(
        'flex border-b border-gray-200 bg-white rounded-t-xl overflow-hidden',
        className
      )}
      role="tablist"
    >
      {children}
    </div>
  );
};

// Individual Tab Component
interface TabProps {
  value: string;
  children: ReactNode;
  className?: string;
  disabled?: boolean;
}

export const Tab: React.FC<TabProps> = ({
  value,
  children,
  className,
  disabled = false
}) => {
  const { activeTab, setActiveTab } = useTabContext();
  const isActive = activeTab === value;

  const handleClick = () => {
    if (!disabled) {
      setActiveTab(value);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  };

  return (
    <button
      className={cn(
        'relative px-6 py-4 font-medium text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset',
        isActive
          ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50',
        disabled && 'text-gray-400 cursor-not-allowed hover:text-gray-400 hover:bg-transparent',
        className
      )}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={disabled}
      role="tab"
      aria-selected={isActive}
      aria-controls={`tabpanel-${value}`}
      id={`tab-${value}`}
      tabIndex={isActive ? 0 : -1}
    >
      {children}
      {isActive && (
        <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600 transition-all duration-200" />
      )}
    </button>
  );
};

// Tab Panel Component
interface TabPanelProps {
  value: string;
  children: ReactNode;
  className?: string;
}

export const TabPanel: React.FC<TabPanelProps> = ({
  value,
  children,
  className
}) => {
  const { activeTab } = useTabContext();
  const isActive = activeTab === value;

  if (!isActive) return null;

  return (
    <div
      className={cn('w-full', className)}
      role="tabpanel"
      aria-labelledby={`tab-${value}`}
      id={`tabpanel-${value}`}
    >
      {children}
    </div>
  );
};

// Export all components
export const Tabs = {
  Container: TabContainer,
  List: TabList,
  Tab: Tab,
  Panel: TabPanel,
}; 