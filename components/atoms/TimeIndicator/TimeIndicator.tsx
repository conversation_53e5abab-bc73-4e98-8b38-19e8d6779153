'use client';

import React from 'react';
import { Clock, AlertTriangle } from 'lucide-react';
import { cn } from '@/utils/cn';
import { useViewport, responsiveClasses } from '@/utils/responsive';

export interface TimeIndicatorProps {
  timeRemaining?: number; // in minutes
  timeLimit?: number; // in minutes
  showIcon?: boolean;
  variant?: 'default' | 'compact' | 'badge';
  className?: string;
}

export const TimeIndicator: React.FC<TimeIndicatorProps> = ({
  timeRemaining,
  timeLimit,
  showIcon = true,
  variant = 'default',
  className,
}) => {
  const { isMobile, isTablet } = useViewport();
  
  // Return null if no time data
  if (timeRemaining === undefined) return null;

  // Format time display
  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  // Get time color and urgency based on remaining time
  const getTimeStatus = () => {
    if (!timeLimit) return { color: 'text-text-secondary', urgency: 'normal' };
    
    const percentage = (timeRemaining / timeLimit) * 100;
    if (percentage <= 10) return { color: 'text-red-600', urgency: 'critical' };
    if (percentage <= 25) return { color: 'text-amber-600', urgency: 'warning' };
    return { color: 'text-text-secondary', urgency: 'normal' };
  };

  const { color, urgency } = getTimeStatus();
  const isUrgent = urgency !== 'normal';

  // Responsive variant styles
  const variantStyles = {
    default: cn(
      'flex items-center gap-2',
      responsiveClasses.text.metadata
    ),
    compact: cn(
      'flex items-center gap-1',
      isMobile ? 'text-xs' : 'text-xs'
    ),
    badge: cn(
      'inline-flex items-center rounded-full font-medium',
      // Responsive padding and text size
      isMobile ? 'gap-1 px-2 py-1 text-xs' : 'gap-1.5 px-3 py-1.5 text-sm',
      {
        'bg-red-100 text-red-800': urgency === 'critical',
        'bg-amber-100 text-amber-800': urgency === 'warning',
        'bg-background-subtle text-text-secondary': urgency === 'normal',
      }
    ),
  };

  return (
    <div className={cn(variantStyles[variant], className)}>
      {showIcon && (
        <>
          {isUrgent ? (
            <AlertTriangle 
              size={variant === 'compact' ? 12 : 16} 
              className={cn(color, { 'animate-pulse': urgency === 'critical' })}
            />
          ) : (
            <Clock size={variant === 'compact' ? 12 : 16} className={color} />
          )}
        </>
      )}
      <span className={color}>
        {variant === 'badge' ? formatTime(timeRemaining) : `${formatTime(timeRemaining)} remaining`}
      </span>
      {variant === 'default' && timeLimit && (
        <span className="text-text-secondary text-xs">
          / {formatTime(timeLimit)}
        </span>
      )}
    </div>
  );
};