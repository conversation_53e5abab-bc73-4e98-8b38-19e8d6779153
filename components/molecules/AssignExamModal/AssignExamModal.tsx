'use client';

import React, { useState } from 'react';
import { X, Users, UserPlus } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/atoms/Dialog/Dialog';
import { Button } from '@/components/atoms/Button/Button';
import { StudentSelectionTable } from '../StudentSelectionTable/StudentSelectionTable';
import { InviteStudentForm } from '../InviteStudentForm/InviteStudentForm';
import { cn } from '@/utils/cn';

export interface AssignExamModalProps {
  isOpen: boolean;
  onClose: () => void;
  examId: string;
  examTitle: string;
  onSuccess?: () => void;
}

type TabType = 'assign' | 'invite';

export const AssignExamModal: React.FC<AssignExamModalProps> = ({
  isOpen,
  onClose,
  examId,
  examTitle,
  onSuccess,
}) => {
  const [activeTab, setActiveTab] = useState<TabType>('assign');

  const handleClose = () => {
    setActiveTab('assign'); // Reset to first tab when closing
    onClose();
  };

  const handleSuccess = () => {
    if (onSuccess) {
      onSuccess();
    }
    handleClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <DialogHeader className="border-b border-gray-200 pb-4">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl font-semibold text-text-primary">
                Assign Exam
              </DialogTitle>
              <p className="text-sm text-text-secondary mt-1">
                {examTitle}
              </p>
            </div>
            <Button
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={handleClose}
              aria-label="Close modal"
            >
              <X size={16} />
            </Button>
          </div>

          {/* Tab Navigation */}
          <div className="flex mt-4 border-b border-gray-200">
            <button
              className={cn(
                'px-4 py-2 text-sm font-medium border-b-2 transition-colors duration-200',
                activeTab === 'assign'
                  ? 'text-primary-action border-primary-action'
                  : 'text-text-secondary border-transparent hover:text-text-primary hover:border-gray-300'
              )}
              onClick={() => setActiveTab('assign')}
            >
              <div className="flex items-center gap-2">
                <Users size={16} />
                Assign Students
              </div>
            </button>
            <button
              className={cn(
                'px-4 py-2 text-sm font-medium border-b-2 transition-colors duration-200',
                activeTab === 'invite'
                  ? 'text-primary-action border-primary-action'
                  : 'text-text-secondary border-transparent hover:text-text-primary hover:border-gray-300'
              )}
              onClick={() => setActiveTab('invite')}
            >
              <div className="flex items-center gap-2">
                <UserPlus size={16} />
                Invite New Student
              </div>
            </button>
          </div>
        </DialogHeader>

        {/* Tab Content */}
        <div className="flex-1 overflow-hidden">
          {activeTab === 'assign' ? (
            <StudentSelectionTable
              examId={examId}
              onSuccess={handleSuccess}
              onCancel={handleClose}
            />
          ) : (
            <InviteStudentForm
              examId={examId}
              onSuccess={handleSuccess}
              onCancel={handleClose}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
