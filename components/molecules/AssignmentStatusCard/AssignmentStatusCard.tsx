import React from 'react';
import { cn } from '@/utils/cn';
import { formatDate, formatDuration } from '@/utils/date';
import { IExamAssignmentDetail } from '@/types/exam.types';
import { formatAssignmentStatus, getAssignmentStatusBadge } from '@/utils/examUtils';
import { 
  User, 
  Clock, 
  Calendar, 
  CheckCircle2, 
  PlayCircle, 
  UserCheck 
} from 'lucide-react';

interface AssignmentStatusCardProps {
  assignment: IExamAssignmentDetail;
  onClick?: () => void;
  className?: string;
}

export function AssignmentStatusCard({ 
  assignment, 
  onClick, 
  className 
}: AssignmentStatusCardProps) {
  const getStatusIcon = (status: IExamAssignmentDetail['status']) => {
    switch (status) {
      case 'completed':
        return CheckCircle2;
      case 'in_progress':
        return PlayCircle;
      case 'assigned':
      default:
        return UserCheck;
    }
  };

  const StatusIcon = getStatusIcon(assignment.status);

  const getTimeInfo = () => {
    const now = new Date();
    const assignedDate = new Date(assignment.assignedAt);
    
    if (assignment.completedAt) {
      return {
        label: 'Completed',
        value: formatDate(assignment.completedAt),
        icon: Calendar
      };
    }
    
    if (assignment.startedAt) {
      const startedDate = new Date(assignment.startedAt);
      const timeSpent = Math.floor((now.getTime() - startedDate.getTime()) / 1000);
      return {
        label: 'Started',
        value: formatDate(assignment.startedAt),
        icon: Clock
      };
    }
    
    return {
      label: 'Assigned',
      value: formatDate(assignment.assignedAt),
      icon: Calendar
    };
  };

  const timeInfo = getTimeInfo();
  const TimeIcon = timeInfo.icon;

  return (
    <div 
      className={cn(
        'bg-background-default border border-gray-100 rounded-xl p-4 transition-all duration-200',
        'hover:shadow-sm hover:border-gray-200 cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-start justify-between gap-3">
        {/* Student Info */}
        <div className="flex items-center gap-3 min-w-0 flex-1">
          <div className="w-10 h-10 bg-section-bg-accent rounded-lg flex items-center justify-center flex-shrink-0">
            <User className="w-5 text-primary-action" />
          </div>
          <div className="min-w-0 flex-1">
            <h4 className="font-medium text-text-primary truncate">
              {assignment.studentName}
            </h4>
            <p className="text-sm text-text-secondary truncate">
              {assignment.studentEmail}
            </p>
          </div>
        </div>

        {/* Status Badge */}
        <div className={getAssignmentStatusBadge(assignment.status)}>
          <StatusIcon className="w-3" />
          <span>{formatAssignmentStatus(assignment.status)}</span>
        </div>
      </div>

      {/* Additional Info */}
      <div className="mt-3 flex items-center text-sm">
        <div className="flex items-center gap-2 text-text-secondary">
          <TimeIcon className="w-4" />
          <span>{timeInfo.label}: {timeInfo.value}</span>
        </div>
      </div>
    </div>
  );
} 