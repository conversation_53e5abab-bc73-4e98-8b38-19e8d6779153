import React, { useMemo } from 'react';
import { 
  Calendar, 
  Clock, 
  CheckCircle2, 
  Award, 
  Users,
  ArrowUp,
  ArrowDown,
  ArrowUpDown 
} from 'lucide-react';
import { AssignmentStatusBadge } from '@/components/atoms/StatusBadge/AssignmentStatusBadge';
import { formatDate, formatTimeAgo } from '@/utils/date';
import { cn } from '@/utils/cn';
import { IAssignmentDetail } from '../DetailedStudentResult/types';

interface AssignmentTableProps {
  assignments: IAssignmentDetail[];
  sortConfig: { key: string; direction: 'asc' | 'desc' } | null;
  onSort: (key: string) => void;
  className?: string;
}

export const AssignmentTable: React.FC<AssignmentTableProps> = ({
  assignments,
  sortConfig,
  onSort,
  className
}) => {
  const sortedAssignments = useMemo(() => {
    if (!assignments) return [];
    
    const sortableItems = [...assignments];
    if (sortConfig !== null) {
      sortableItems.sort((a, b) => {
        const aValue = a[sortConfig.key as keyof IAssignmentDetail];
        const bValue = b[sortConfig.key as keyof IAssignmentDetail];
        
        if (aValue === undefined && bValue === undefined) return 0;
        if (aValue === undefined) return sortConfig.direction === 'asc' ? -1 : 1;
        if (bValue === undefined) return sortConfig.direction === 'asc' ? 1 : -1;
        
        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableItems;
  }, [assignments, sortConfig]);

  if (!assignments || assignments.length === 0) {
    return (
      <div className="bg-background-default rounded-lg shadow-sm p-3 md:p-6 text-center">
        <Users className="w-8 h-8 text-text-tertiary mx-auto mb-2" />
        <p className="text-text-secondary">No students have been assigned to this exam yet.</p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Desktop Table View */}
      <div className="hidden md:block overflow-x-auto">
        <table className="w-full text-sm">
          <thead className="bg-section-bg-neutral-alt text-text-secondary">
            <tr>
              <SortableHeader
                label="Student"
                sortKey="studentName"
                currentSort={sortConfig}
                onSort={onSort}
              />
              <SortableHeader
                label="Status"
                sortKey="status"
                currentSort={sortConfig}
                onSort={onSort}
              />
              <SortableHeader
                label="Assigned Date"
                sortKey="assignedAt"
                currentSort={sortConfig}
                onSort={onSort}
              />
              <th className="px-3 py-2 text-left font-medium">Started</th>
              <th className="px-3 py-2 text-left font-medium">Completed</th>
              <th className="px-3 py-2 text-left font-medium">Score</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {sortedAssignments.map((assignment) => (
              <tr key={assignment.studentId} className="hover:bg-section-bg-neutral-alt/30 transition-colors">
                <td className="px-3 py-3">
                  <div>
                    <div className="font-medium text-text-primary">{assignment.studentName}</div>
                    <div className="text-xs text-text-tertiary">{assignment.studentEmail}</div>
                  </div>
                </td>
                <td className="px-3 py-3">
                  <AssignmentStatusBadge status={assignment.status} />
                </td>
                <td className="px-3 py-3 text-text-secondary">
                  {formatDate(new Date(assignment.assignedAt))}
                </td>
                <td className="px-3 py-3 text-text-secondary">
                  {assignment.startedAt ? formatDate(new Date(assignment.startedAt)) : '-'}
                </td>
                <td className="px-3 py-3 text-text-secondary">
                  {assignment.completedAt ? formatDate(new Date(assignment.completedAt)) : '-'}
                </td>
                <td className="px-3 py-3">
                  {assignment.status === 'completed' && assignment.score !== undefined ? 
                    <span className="font-medium text-text-primary">{assignment.score}%</span> : 
                    '-'}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className="md:hidden space-y-3">
        {sortedAssignments.map((assignment) => (
          <div key={assignment.studentId} className="border rounded-lg p-3 bg-background-default shadow-sm">
            <div className="flex justify-between items-start mb-2">
              <div>
                <div className="font-medium text-text-primary">{assignment.studentName}</div>
                <div className="text-xs text-text-tertiary">{assignment.studentEmail}</div>
              </div>
              <AssignmentStatusBadge status={assignment.status} />
            </div>
            
            <div className="grid grid-cols-2 gap-2 text-sm mt-3">
              <div className="flex items-center gap-1 text-text-secondary">
                <Calendar className="w-3 h-3 text-text-tertiary" />
                <span>Assigned:</span>
              </div>
              <div className="text-text-primary">{formatTimeAgo(new Date(assignment.assignedAt))}</div>
              
              {assignment.startedAt && (
                <>
                  <div className="flex items-center gap-1 text-text-secondary">
                    <Clock className="w-3 h-3 text-text-tertiary" />
                    <span>Started:</span>
                  </div>
                  <div className="text-text-primary">{formatTimeAgo(new Date(assignment.startedAt))}</div>
                </>
              )}
              
              {assignment.completedAt && (
                <>
                  <div className="flex items-center gap-1 text-text-secondary">
                    <CheckCircle2 className="w-3 h-3 text-text-tertiary" />
                    <span>Completed:</span>
                  </div>
                  <div className="text-text-primary">{formatTimeAgo(new Date(assignment.completedAt))}</div>
                </>
              )}
              
              {assignment.status === 'completed' && assignment.score !== undefined && (
                <>
                  <div className="flex items-center gap-1 text-text-secondary">
                    <Award className="w-3 h-3 text-text-tertiary" />
                    <span>Score:</span>
                  </div>
                  <div className="font-medium text-text-primary">{assignment.score}%</div>
                </>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

interface SortableHeaderProps {
  label: string;
  sortKey: string;
  currentSort: { key: string; direction: 'asc' | 'desc' } | null;
  onSort: (key: string) => void;
}

const SortableHeader: React.FC<SortableHeaderProps> = ({
  label,
  sortKey,
  currentSort,
  onSort
}) => {
  const ArrowIcon = () => {
    if (currentSort?.key === sortKey) {
      return currentSort.direction === 'asc' ? 
        <ArrowUp className="w-3 h-3" /> : 
        <ArrowDown className="w-3 h-3" />;
    }
    return <ArrowUpDown className="w-3 h-3 text-text-tertiary" />;
  };

  return (
    <th 
      className="px-3 py-2 text-left font-medium cursor-pointer hover:bg-section-bg-neutral-alt/80 transition-colors"
      onClick={() => onSort(sortKey)}
    >
      <div className="flex items-center gap-1">
        {label}
        <ArrowIcon />
      </div>
    </th>
  );
}; 