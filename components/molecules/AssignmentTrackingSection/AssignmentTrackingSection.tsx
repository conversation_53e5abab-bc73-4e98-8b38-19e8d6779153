import React, { useState, useMemo } from 'react';
import { cn } from '@/utils/cn';
import { IExamAssignmentDetail } from '@/types/exam.types';
import { AssignmentStatusCard } from '../AssignmentStatusCard/AssignmentStatusCard';
import { formatAssignmentStatus } from '@/utils/examUtils';
import { 
  Users, 
  CheckCircle2, 
  PlayCircle, 
  UserCheck,
  TrendingUp,
  Filter,
  Search
} from 'lucide-react';
import { Input } from '@/components/atoms/Input/Input';

interface AssignmentTrackingSectionProps {
  assignments: IExamAssignmentDetail[];
  onAssignmentClick?: (assignment: IExamAssignmentDetail) => void;
  className?: string;
}

type AssignmentStatusFilter = 'all' | 'assigned' | 'in_progress' | 'completed';

export function AssignmentTrackingSection({ 
  assignments, 
  onAssignmentClick,
  className 
}: AssignmentTrackingSectionProps) {
  const [statusFilter, setStatusFilter] = useState<AssignmentStatusFilter>('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Calculate assignment statistics
  const stats = useMemo(() => {
    const total = assignments.length;
    const assigned = assignments.filter(a => a.status === 'assigned').length;
    const inProgress = assignments.filter(a => a.status === 'in_progress').length;
    const completed = assignments.filter(a => a.status === 'completed').length;
    const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;
    
    const completedWithScores = assignments.filter(a => 
      a.status === 'completed' && a.score !== undefined
    );
    const averageScore = completedWithScores.length > 0
      ? Math.round(completedWithScores.reduce((sum, a) => sum + (a.score || 0), 0) / completedWithScores.length)
      : 0;

    return {
      total,
      assigned,
      inProgress,
      completed,
      completionRate,
      averageScore
    };
  }, [assignments]);

  // Filter assignments based on status and search query
  const filteredAssignments = useMemo(() => {
    let filtered = assignments;

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(a => a.status === statusFilter);
    }

    // Filter by search query (student name or email)
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(a =>
        a.studentName.toLowerCase().includes(query) ||
        a.studentEmail.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [assignments, statusFilter, searchQuery]);

  const handleAssignmentClick = (assignment: IExamAssignmentDetail) => {
    if (onAssignmentClick) {
      onAssignmentClick(assignment);
    }
  };

  const filterOptions = [
    { value: 'all' as const, label: 'All Assignments', count: stats.total },
    { value: 'assigned' as const, label: formatAssignmentStatus('assigned'), count: stats.assigned },
    { value: 'in_progress' as const, label: formatAssignmentStatus('in_progress'), count: stats.inProgress },
    { value: 'completed' as const, label: formatAssignmentStatus('completed'), count: stats.completed },
  ];

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header with Stats */}
      <div className="bg-background-default rounded-2xl shadow-sm border border-gray-100 p-6">
        <div className="flex items-center gap-2 mb-6">
          <Users className="w-5 text-primary-action" />
          <h2 className="text-lg font-semibold text-text-primary">Assignment Tracking</h2>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center p-4 bg-section-bg-neutral-alt rounded-xl">
            <div className="text-2xl font-bold text-text-primary">{stats.total}</div>
            <div className="text-xs text-text-secondary">Total Assigned</div>
          </div>
          <div className="text-center p-4 bg-section-bg-neutral-alt rounded-xl">
            <div className="text-2xl font-bold text-blue-600">{stats.inProgress}</div>
            <div className="text-xs text-text-secondary">In Progress</div>
          </div>
          <div className="text-center p-4 bg-section-bg-neutral-alt rounded-xl">
            <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
            <div className="text-xs text-text-secondary">Completed</div>
          </div>
          <div className="text-center p-4 bg-section-bg-neutral-alt rounded-xl">
            <div className="text-2xl font-bold text-primary-action">{stats.completionRate}%</div>
            <div className="text-xs text-text-secondary">Completion Rate</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-text-secondary">Overall Progress</span>
            <span className="font-medium text-text-primary">{stats.completionRate}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="h-2 rounded-full bg-gradient-to-r from-blue-500 to-green-500 transition-all duration-300"
              style={{ width: `${stats.completionRate}%` }}
            />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-background-default rounded-2xl shadow-sm border border-gray-100 p-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Filter className="w-4 text-text-secondary" />
            <span className="text-sm font-medium text-text-secondary">Filter by Status:</span>
          </div>
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 text-text-secondary" />
            <Input
              type="text"
              placeholder="Search students..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap gap-2">
          {filterOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => setStatusFilter(option.value)}
              className={cn(
                'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200',
                'flex items-center gap-2',
                statusFilter === option.value
                  ? 'bg-primary-action text-background-default'
                  : 'bg-section-bg-neutral-alt text-text-secondary hover:bg-section-bg-accent hover:text-text-primary'
              )}
            >
              <span>{option.label}</span>
              <span className={cn(
                'px-1.5 py-0.5 rounded text-xs',
                statusFilter === option.value
                  ? 'bg-background-default/20 text-background-default'
                  : 'bg-background-default text-text-secondary'
              )}>
                {option.count}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Assignment Cards */}
      <div className="bg-background-default rounded-2xl shadow-sm border border-gray-100 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-text-primary">
            {statusFilter === 'all' ? 'All Assignments' : `${formatAssignmentStatus(statusFilter)} Assignments`}
          </h3>
          <span className="text-sm text-text-secondary">
            {filteredAssignments.length} of {stats.total} assignments
          </span>
        </div>

        {filteredAssignments.length === 0 ? (
          <div className="text-center py-12">
            <Users className="w-12 mx-auto mb-4 text-text-secondary" />
            <h4 className="text-lg font-medium text-text-primary mb-2">
              {searchQuery.trim() ? 'No assignments found' : 'No assignments yet'}
            </h4>
            <p className="text-text-secondary">
              {searchQuery.trim() 
                ? 'Try adjusting your search or filter criteria.'
                : 'Students will appear here once the exam is assigned to them.'
              }
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredAssignments.map((assignment) => (
              <AssignmentStatusCard
                key={assignment.studentId}
                assignment={assignment}
                onClick={() => handleAssignmentClick(assignment)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
} 