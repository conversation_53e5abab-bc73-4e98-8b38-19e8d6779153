'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { createExamFromWorksheet } from '@/actions/exam.action';
import { createExamFromWorksheetSchema, CreateExamFromWorksheetInput } from '@/types/exam.types';
import { FileText, X, Clock, Target, BookOpen } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/atoms/Dialog/Dialog';

export interface CreateExamModalProps {
  isOpen: boolean;
  onClose: () => void;
  worksheet: {
    id: string;
    title: string;
  };
  onSuccess?: () => void;
}

export const CreateExamModal: React.FC<CreateExamModalProps> = ({
  isOpen,
  onClose,
  worksheet,
  onSuccess,
}) => {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<Omit<CreateExamFromWorksheetInput, 'questions'> & { questions?: any }>({
    resolver: zodResolver(createExamFromWorksheetSchema.omit({ questions: true })),
    defaultValues: {
      worksheetId: worksheet.id,
      title: `${worksheet.title} - Exam`,
      selectedOptions: [
        { key: 'timeLimit', value: '60' },
        { key: 'passingScore', value: '70' },
      ],
    },
  });

  // Watch form values for easier access
  const selectedOptions = watch('selectedOptions') || [];
  const timeLimitValue = selectedOptions.find(opt => opt.key === 'timeLimit')?.value || '60';
  const passingScoreValue = selectedOptions.find(opt => opt.key === 'passingScore')?.value || '70';

  // Helper function to update selectedOptions
  const updateSelectedOption = (key: string, value: string) => {
    const currentOptions = selectedOptions.filter(opt => opt.key !== key);
    currentOptions.push({ key, value });
    setValue('selectedOptions', currentOptions);
  };

  // Get error for selectedOptions if any
  const selectedOptionsError = errors.selectedOptions?.message;

  const handleCreateExam = async (data: Omit<CreateExamFromWorksheetInput, 'questions'> & { questions?: any }) => {
    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      // Construct the complete payload - questions will be fetched by the action
      const examPayload: CreateExamFromWorksheetInput = {
        worksheetId: data.worksheetId,
        title: data.title,
        selectedOptions: data.selectedOptions,
        questions: [] // This will be populated by the action
      };

      const response = await createExamFromWorksheet(examPayload);

      if (response.status === 'success') {
        setSuccess('Exam created successfully!');
        reset();

        // Navigate to exam detail page after successful creation
        const examId = response.data?.id;
        if (examId) {
          setTimeout(() => {
            onSuccess?.();
            onClose();
            router.push(`/admin/exam-results/${examId}`);
          }, 1500);
        } else {
          // Fallback if no exam ID is returned
          setTimeout(() => {
            onSuccess?.();
            onClose();
          }, 1500);
        }
      } else {
        const message = response.message;
        if (typeof message === 'string') {
          setError(message || 'Failed to create exam');
        } else if (Array.isArray(message)) {
          setError(message.map(e => e.constraints).join(', '));
        } else {
          setError('An unexpected error occurred');
        }
      }
    } catch (err: any) {
      console.error('Error creating exam:', err);
      setError(err.message || 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setError(null);
      setSuccess(null);
      reset();
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md mx-auto">
        {/* Header with close button */}
        <DialogHeader className="relative">
          <div className="flex items-center gap-3">
            <div className="bg-blue-100 p-2 rounded-full">
              <FileText className="text-blue-600" size={20} />
            </div>
            <DialogTitle className="flex-1">Create Exam</DialogTitle>
          </div>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="absolute top-2 right-2 rounded-full p-1.5 bg-gray-100 hover:bg-gray-200 transition-colors duration-200 disabled:opacity-50"
            aria-label="Close"
          >
            <X size={16} className="text-gray-600" />
          </button>
        </DialogHeader>

        {/* Content */}
        <div className="px-4 pb-4">
          {/* Success/Error Messages */}
          {success && <AlertMessage type="success" message={success} />}
          {error && <AlertMessage type="error" message={error} />}

          {/* Form */}
          <form onSubmit={handleSubmit(handleCreateExam)} className="space-y-4">
            {/* Worksheet Info */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex items-center">
                <BookOpen size={16} className="text-gray-500 mr-2" />
                <span className="text-sm text-gray-600">Source Worksheet:</span>
              </div>
              <p className="text-sm font-medium text-gray-900 mt-1">{worksheet.title}</p>
            </div>

            {/* Exam Title */}
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                Exam Title
              </label>
              <input
                id="title"
                type="text"
                {...register('title')}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 transition-colors text-sm ${
                  errors.title
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                }`}
                placeholder="Enter exam title"
                disabled={isSubmitting}
              />
              {errors.title && (
                <p className="text-red-600 text-xs mt-1">{errors.title.message}</p>
              )}
            </div>

            {/* Time Limit */}
            <div>
              <label htmlFor="timeLimit" className="block text-sm font-medium text-gray-700 mb-1">
                <Clock size={14} className="inline mr-1" />
                Time Limit (minutes)
              </label>
              <input
                id="timeLimit"
                type="number"
                value={timeLimitValue}
                onChange={(e) => updateSelectedOption('timeLimit', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 transition-colors text-sm ${
                  selectedOptionsError
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                }`}
                placeholder="60"
                min="1"
                max="600"
                disabled={isSubmitting}
              />
              {selectedOptionsError && (
                <p className="text-red-600 text-xs mt-1">{selectedOptionsError}</p>
              )}
            </div>

            {/* Passing Score */}
            <div>
              <label htmlFor="passingScore" className="block text-sm font-medium text-gray-700 mb-1">
                <Target size={14} className="inline mr-1" />
                Passing Score (%)
              </label>
              <input
                id="passingScore"
                type="number"
                value={passingScoreValue}
                onChange={(e) => updateSelectedOption('passingScore', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 transition-colors text-sm ${
                  selectedOptionsError
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                }`}
                placeholder="70"
                min="0"
                max="100"
                disabled={isSubmitting}
              />
              {selectedOptionsError && (
                <p className="text-red-600 text-xs mt-1">{selectedOptionsError}</p>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="button"
                onClick={handleClose}
                variant="ghost"
                disabled={isSubmitting}
                className="px-4 py-2"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                isLoading={isSubmitting}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white"
                iconProps={{ variant: 'file-text', size: 4 }}
              >
                {isSubmitting ? 'Creating...' : 'Create Exam'}
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};
