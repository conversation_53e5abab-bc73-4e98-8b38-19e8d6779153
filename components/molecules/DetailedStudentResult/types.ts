export interface IAssignmentDetail {
  studentId: string;
  studentName: string;
  studentEmail: string;
  status: 'assigned' | 'in_progress' | 'completed';
  assignedAt: string;
  startedAt?: string;
  completedAt?: string;
  score?: number;
  feedback?: string;
}

export interface IQuestionDetail {
  type: string;
  content: string;
  studentAnswer: string[];
  correctAnswer: string[];
  isCorrect: boolean;
  explanation?: string;
} 