'use client';

import React, { useState, useCallback } from 'react';
import { Upload, Trash2, FileText, CheckCircle, AlertCircle } from 'lucide-react';
import { FormField } from '../FormField/FormField';
import { Input } from '../../atoms/Input/Input';
import { Button } from '../../atoms/Button/Button';
import { uploadDocuments, IDocumentUploadPayload, IDocumentUploadResponse } from '@/apis/documentUploadApi';
import { useSession } from 'next-auth/react';

export interface DocumentUploadProps {
  onSuccess?: (response: IDocumentUploadResponse) => void;
  onError?: (error: string) => void;
  className?: string;
}

export const DocumentUpload: React.FC<DocumentUploadProps> = ({
  onSuccess,
  onError,
  className = '',
}) => {
  const { data: session } = useSession();
  const [files, setFiles] = useState<File[]>([]);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []);
    setFiles(prev => [...prev, ...selectedFiles]);
    setError(null);
  }, []);

  const removeFile = useCallback((index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
    setError(null);
  }, []);

  const handleUpload = useCallback(async () => {
    if (!session?.user?.accessToken) {
      setError('You must be logged in to upload documents');
      return;
    }

    if (files.length === 0) {
      setError('Please select at least one file to upload');
      return;
    }

    setIsUploading(true);
    setError(null);
    setSuccess(false);
    setUploadProgress(0);

    try {
      const payload: IDocumentUploadPayload = {
        files,
        title: title || undefined,
        description: description || undefined,
        category: category || undefined,
      };

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      const response = await uploadDocuments(payload, session.user.accessToken);

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (response.status === 'success' && response.data) {
        setSuccess(true);
        setFiles([]);
        setTitle('');
        setDescription('');
        setCategory('');
        onSuccess?.(response.data);
        
        // Reset success state after 3 seconds
        setTimeout(() => {
          setSuccess(false);
          setUploadProgress(0);
        }, 3000);
      } else if (response.status === 'error') {
        const errorMessage = Array.isArray(response.message) 
          ? response.message.map(err => `${err.field}: ${err.constraints}`).join(', ')
          : response.message || 'Failed to upload documents';
        setError(errorMessage);
        onError?.(errorMessage);
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred');
      onError?.(err.message || 'An unexpected error occurred');
    } finally {
      setIsUploading(false);
    }
  }, [files, title, description, category, session, onSuccess, onError]);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* File Upload Section */}
      <div className="space-y-4">
        <FormField label="Upload Documents" required>
          <div className="space-y-4">
            {/* File Input */}
            <div className="relative">
              <input
                type="file"
                multiple
                onChange={handleFileSelect}
                className="hidden"
                id="document-upload"
                accept=".pdf,.doc,.docx,.txt,.rtf"
                disabled={isUploading}
              />
              <label
                htmlFor="document-upload"
                className={`
                  block w-full p-6 border-2 border-dashed rounded-lg cursor-pointer transition-all duration-200
                  ${isUploading
                    ? 'border-gray-300 bg-gray-50 cursor-not-allowed'
                    : 'border-gray-300 hover:border-indigo-400 hover:bg-indigo-50'
                  }
                `}
              >
                <div className="text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-sm text-gray-600">
                    <span className="font-medium text-indigo-600 hover:text-indigo-500">
                      Click to upload
                    </span>{' '}
                    or drag and drop
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    PDF, DOC, DOCX, TXT, RTF (Max 10MB each)
                  </p>
                </div>
              </label>
            </div>

            {/* Selected Files */}
            {files.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700">Selected Files:</h4>
                <div className="space-y-2">
                  {files.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <FileText className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">{file.name}</p>
                          <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeFile(index)}
                        className="text-red-500 hover:text-red-700 transition-colors"
                        disabled={isUploading}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </FormField>

        {/* Metadata Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField label="Title (Optional)">
            <Input
              type="text"
              placeholder="Enter document title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              disabled={isUploading}
            />
          </FormField>

          <FormField label="Category (Optional)">
            <Input
              type="text"
              placeholder="Enter category"
              value={category}
              onChange={(e) => setCategory(e.target.value)}
              disabled={isUploading}
            />
          </FormField>
        </div>

        <FormField label="Description (Optional)">
          <textarea
            className="w-full px-4 py-3 border border-gray-300 rounded-md focus:border-gray-300 focus:outline-none disabled:opacity-50"
            placeholder="Enter document description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={3}
            disabled={isUploading}
          />
        </FormField>
      </div>

      {/* Progress Bar */}
      {isUploading && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Uploading...</span>
            <span>{uploadProgress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="flex items-center space-x-2 p-4 bg-green-50 border border-green-200 rounded-lg">
          <CheckCircle className="h-5 w-5 text-green-500" />
          <p className="text-sm text-green-700">Documents uploaded successfully!</p>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-lg">
          <AlertCircle className="h-5 w-5 text-red-500" />
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}

      {/* Upload Button */}
      <Button
        onClick={handleUpload}
        disabled={isUploading || files.length === 0}
        className="w-full"
        variant="primary"
      >
        {isUploading ? 'Uploading...' : 'Upload Documents'}
      </Button>
    </div>
  );
}; 