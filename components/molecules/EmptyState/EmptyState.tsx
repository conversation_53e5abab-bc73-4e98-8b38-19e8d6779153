'use client';

import React from 'react';
import { BookO<PERSON>, Clock, CheckCircle, Play } from 'lucide-react';
import { cn } from '@/utils/cn';

interface StatusExample {
  color: string;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface EmptyStateProps {
  className?: string;
}

const statusExamples: StatusExample[] = [
  {
    color: 'bg-primary-action',
    label: 'Not Started',
    description: 'Click "Start Exam" to begin',
    icon: Play,
  },
  {
    color: 'bg-amber-500',
    label: 'In Progress',
    description: 'Click "Continue" to resume',
    icon: Clock,
  },
  {
    color: 'bg-green-500',
    label: 'Completed',
    description: 'Click "View Results" to see score',
    icon: CheckCircle,
  }
];

export const EmptyState: React.FC<EmptyStateProps> = ({ className }) => {
  return (
    <div 
      className={cn(
        "bg-background-default border border-gray-200 rounded-xl p-6",
        "shadow-sm",
        className
      )}
      role="region"
      aria-label="Empty exam dashboard"
    >
      <div className="text-center max-w-xl mx-auto">
        {/* Main Icon */}
        <div className="w-16 h-16 bg-section-bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
          <BookOpen className="w-8 h-8 text-primary-action" aria-hidden="true" />
        </div>

        {/* Main heading and description */}
        <div className="mb-6">
          <h3 className="text-xl font-bold text-text-primary mb-2">
            Welcome to Your Exam Dashboard
          </h3>
          <p className="text-sm text-text-secondary mb-4 max-w-md mx-auto">
            This is where you&apos;ll find all your assigned exams. When your teacher assigns you an exam,
            it will appear here and you can start taking it right away.
          </p>
          <div className="inline-flex items-center gap-2 px-3 py-1.5 bg-accent-bg-light text-blue-800 rounded-full text-sm font-medium">
            Ready to learn and succeed!
          </div>
        </div>

        {/* Status examples */}
        <div className="bg-background-subtle rounded-lg p-4">
          <h4 className="text-sm font-semibold text-text-primary mb-3">
            How Exam Status Works
          </h4>
          
          <div className="space-y-3">
            {statusExamples.map((example) => (
              <div key={example.label} className="flex items-center gap-3 text-left">
                <div className={cn(
                  "w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0",
                  example.color
                )}>
                  <example.icon className="w-3 h-3 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <span className="font-medium text-text-primary text-sm">{example.label}</span>
                  <span className="text-text-secondary text-xs ml-2">{example.description}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmptyState;