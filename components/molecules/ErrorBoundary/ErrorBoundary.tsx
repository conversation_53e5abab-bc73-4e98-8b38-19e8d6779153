'use client';

import React, { Component, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, Refresh<PERSON><PERSON>, ArrowLeft, Home, Bug, Copy, Check } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  level?: 'page' | 'component' | 'critical';
  context?: string;
  showDetails?: boolean;
  enableReporting?: boolean;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
  retryCount: number;
  isRetrying: boolean;
  errorId: string;
  isCopied: boolean;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: NodeJS.Timeout | null = null;
  private maxRetries = 3;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { 
      hasError: false, 
      retryCount: 0, 
      isRetrying: false,
      errorId: '',
      isCopied: false
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Generate unique error ID for tracking
    const errorId = `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return { 
      hasError: true, 
      error,
      errorId
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({ errorInfo });
    
    // Call custom error handler
    this.props.onError?.(error, errorInfo);
    
    // Report error to monitoring service (if enabled)
    if (this.props.enableReporting) {
      this.reportError(error, errorInfo);
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  reportError = async (error: Error, errorInfo: React.ErrorInfo) => {
    try {
      // In a real app, this would send to your error reporting service
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        context: this.props.context,
        level: this.props.level,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        errorId: this.state.errorId
      };
      
      console.log('Error Report:', errorReport);
      // await sendErrorReport(errorReport);
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  handleRetry = () => {
    if (this.state.retryCount >= this.maxRetries) {
      return;
    }

    this.setState({ isRetrying: true });
    
    // Add delay to prevent rapid retries
    this.retryTimeoutId = setTimeout(() => {
      this.setState(prevState => ({ 
        hasError: false, 
        error: undefined, 
        errorInfo: undefined,
        retryCount: prevState.retryCount + 1,
        isRetrying: false
      }));
    }, 1000);
  };

  handleGoBack = () => {
    if (typeof window !== 'undefined') {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = '/';
      }
    }
  };

  handleGoHome = () => {
    if (typeof window !== 'undefined') {
      window.location.href = '/';
    }
  };

  handleRefreshPage = () => {
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  };

  handleCopyError = async () => {
    const errorText = `Error ID: ${this.state.errorId}\nMessage: ${this.state.error?.message}\nStack: ${this.state.error?.stack}`;
    
    try {
      await navigator.clipboard.writeText(errorText);
      this.setState({ isCopied: true });
      setTimeout(() => this.setState({ isCopied: false }), 2000);
    } catch (err) {
      console.error('Failed to copy error details:', err);
    }
  };

  getErrorSeverity = () => {
    const error = this.state.error;
    if (!error) return 'low';
    
    const message = error.message.toLowerCase();
    if (message.includes('chunk') || message.includes('loading')) return 'low';
    if (message.includes('network') || message.includes('fetch')) return 'medium';
    return 'high';
  };

  renderComponentLevelError = () => {
    const severity = this.getErrorSeverity();
    
    return (
      <div className={cn(
        "rounded-lg border p-4 my-4",
        severity === 'high' ? "bg-red-50 border-red-200" : 
        severity === 'medium' ? "bg-orange-50 border-orange-200" : 
        "bg-yellow-50 border-yellow-200"
      )}>
        <div className="flex items-start gap-3">
          <AlertTriangle className={cn(
            "w-5 h-5 flex-shrink-0 mt-0.5",
            severity === 'high' ? "text-red-500" : 
            severity === 'medium' ? "text-orange-500" : 
            "text-yellow-500"
          )} />
          <div className="flex-1 min-w-0">
            <h3 className={cn(
              "text-sm font-semibold",
              severity === 'high' ? "text-red-800" : 
              severity === 'medium' ? "text-orange-800" : 
              "text-yellow-800"
            )}>
              Component Error
            </h3>
            <p className={cn(
              "text-sm mt-1",
              severity === 'high' ? "text-red-700" : 
              severity === 'medium' ? "text-orange-700" : 
              "text-yellow-700"
            )}>
              {this.props.context ? `Error in ${this.props.context}` : 'A component failed to render properly'}
            </p>
            {this.props.showDetails && (
              <details className="mt-2">
                <summary className="text-xs cursor-pointer hover:underline">
                  Show error details
                </summary>
                <pre className="text-xs mt-2 p-2 bg-white/50 rounded border overflow-auto">
                  {this.state.error?.message}
                </pre>
              </details>
            )}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={this.handleRetry}
            disabled={this.state.isRetrying || this.state.retryCount >= this.maxRetries}
            className="flex-shrink-0"
          >
            {this.state.isRetrying ? (
              <RefreshCw className="w-3 h-3 animate-spin" />
            ) : (
              <RefreshCw className="w-3 h-3" />
            )}
          </Button>
        </div>
      </div>
    );
  };

  renderPageLevelError = () => {
    const canRetry = this.state.retryCount < this.maxRetries;
    
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
        <div className="max-w-lg w-full">
          <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
            {/* Error Icon */}
            <div className="w-16 h-16 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
            
            {/* Error Title */}
            <h1 className="text-2xl font-bold text-gray-900 mb-3">
              Oops! Something went wrong
            </h1>
            
            {/* Error Description */}
            <p className="text-gray-600 mb-6 leading-relaxed">
              {this.state.retryCount > 0 
                ? `We've tried ${this.state.retryCount} time${this.state.retryCount > 1 ? 's' : ''} to fix this issue. `
                : ''
              }
              Don&apos;t worry - this happens sometimes. You can try refreshing the page or go back to continue.
            </p>
            
            {/* Error ID */}
            <div className="bg-gray-50 rounded-lg p-3 mb-6 text-sm">
              <div className="flex items-center justify-between">
                <span className="text-gray-500">Error ID: {this.state.errorId}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={this.handleCopyError}
                  className="text-xs"
                >
                  {this.state.isCopied ? (
                    <>
                      <Check className="w-3 h-3 mr-1" />
                      Copied
                    </>
                  ) : (
                    <>
                      <Copy className="w-3 h-3 mr-1" />
                      Copy
                    </>
                  )}
                </Button>
              </div>
            </div>
            
            {/* Error Details (Collapsible) */}
            {this.props.showDetails && (
              <details className="text-left mb-6">
                <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700 mb-2">
                  <Bug className="w-4 h-4 inline mr-1" />
                  Technical Details
                </summary>
                <div className="bg-gray-50 rounded-lg p-4 text-xs font-mono text-gray-700 overflow-auto max-h-32">
                  <div className="mb-2">
                    <strong>Error:</strong> {this.state.error?.message}
                  </div>
                  {this.state.error?.stack && (
                    <div>
                      <strong>Stack:</strong>
                      <pre className="mt-1 whitespace-pre-wrap">{this.state.error.stack}</pre>
                    </div>
                  )}
                </div>
              </details>
            )}
            
            {/* Action Buttons */}
            <div className="space-y-3">
              <div className="flex gap-3">
                {canRetry && (
                  <Button
                    variant="primary"
                    onClick={this.handleRetry}
                    disabled={this.state.isRetrying}
                    className="flex-1"
                  >
                    {this.state.isRetrying ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Retrying...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Try Again
                      </>
                    )}
                  </Button>
                )}
                <Button
                  variant="secondary"
                  onClick={this.handleRefreshPage}
                  className="flex-1"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh Page
                </Button>
              </div>
              
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={this.handleGoBack}
                  className="flex-1"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Go Back
                </Button>
                <Button
                  variant="outline"
                  onClick={this.handleGoHome}
                  className="flex-1"
                >
                  <Home className="w-4 h-4 mr-2" />
                  Home
                </Button>
              </div>
            </div>
            
            {/* Retry Limit Message */}
            {!canRetry && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  Maximum retry attempts reached. Please refresh the page or contact support if the problem persists.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Render different error UIs based on level
      switch (this.props.level) {
        case 'component':
          return this.renderComponentLevelError();
        case 'critical':
        case 'page':
        default:
          return this.renderPageLevelError();
      }
    }

    return this.props.children;
  }
}
