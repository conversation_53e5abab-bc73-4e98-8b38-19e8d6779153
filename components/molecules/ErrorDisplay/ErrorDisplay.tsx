'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>fresh<PERSON><PERSON>, Wifi, WifiOff, AlertCircle, Info } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';

export interface ErrorDisplayProps {
  error: string | null;
  className?: string;
  onRetry?: () => void;
  retryText?: string;
  title?: string;
  description?: string;
  variant?: 'default' | 'network' | 'permission' | 'validation' | 'critical';
  showIcon?: boolean;
  showRetry?: boolean;
  isRetrying?: boolean;
  fallbackContent?: React.ReactNode;
}

// Enhanced error type detection
const getErrorType = (error: string): ErrorDisplayProps['variant'] => {
  const errorLower = error.toLowerCase();
  
  if (errorLower.includes('network') || errorLower.includes('fetch') || errorLower.includes('connection')) {
    return 'network';
  }
  if (errorLower.includes('unauthorized') || errorLower.includes('forbidden') || errorLower.includes('permission')) {
    return 'permission';
  }
  if (errorLower.includes('validation') || errorLower.includes('invalid') || errorLower.includes('required')) {
    return 'validation';
  }
  if (errorLower.includes('critical') || errorLower.includes('fatal') || errorLower.includes('crash')) {
    return 'critical';
  }
  return 'default';
};

// Error variant configurations
const errorVariants = {
  default: {
    icon: AlertTriangle,
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    iconColor: 'text-red-500',
    titleColor: 'text-red-800',
    textColor: 'text-red-700',
    buttonVariant: 'secondary' as const,
    title: 'Something went wrong',
    description: 'An unexpected error occurred. Please try again.'
  },
  network: {
    icon: WifiOff,
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    iconColor: 'text-orange-500',
    titleColor: 'text-orange-800',
    textColor: 'text-orange-700',
    buttonVariant: 'secondary' as const,
    title: 'Connection Problem',
    description: 'Unable to connect to the server. Please check your internet connection and try again.'
  },
  permission: {
    icon: AlertCircle,
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    iconColor: 'text-yellow-600',
    titleColor: 'text-yellow-800',
    textColor: 'text-yellow-700',
    buttonVariant: 'secondary' as const,
    title: 'Access Denied',
    description: 'You don\'t have permission to access this resource. Please contact your administrator.'
  },
  validation: {
    icon: Info,
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    iconColor: 'text-blue-500',
    titleColor: 'text-blue-800',
    textColor: 'text-blue-700',
    buttonVariant: 'secondary' as const,
    title: 'Invalid Input',
    description: 'Please check your input and try again.'
  },
  critical: {
    icon: AlertTriangle,
    bgColor: 'bg-red-100',
    borderColor: 'border-red-300',
    iconColor: 'text-red-600',
    titleColor: 'text-red-900',
    textColor: 'text-red-800',
    buttonVariant: 'primary' as const,
    title: 'Critical Error',
    description: 'A critical error occurred. Please refresh the page or contact support if the problem persists.'
  }
};

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  className = '',
  onRetry,
  retryText = 'Try Again',
  title,
  description,
  variant,
  showIcon = true,
  showRetry = true,
  isRetrying = false,
  fallbackContent,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  if (!error) return null;

  // Auto-detect error type if variant not provided
  const errorVariant = variant || getErrorType(error);
  const config = errorVariants[errorVariant];

  const handleRetry = async () => {
    if (onRetry && !isRetrying) {
      setRetryCount(prev => prev + 1);
      try {
        await onRetry();
      } catch (err) {
        console.error('Retry failed:', err);
      }
    }
  };

  // Show fallback content for critical errors after multiple retry attempts
  if (errorVariant === 'critical' && retryCount >= 3 && fallbackContent) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className={cn(
          "rounded-lg border p-4",
          config.bgColor,
          config.borderColor
        )}>
          <div className="flex items-start gap-3">
            {showIcon && (
              <config.icon className={cn("w-5 h-5 flex-shrink-0 mt-0.5", config.iconColor)} />
            )}
            <div className="flex-1 min-w-0">
              <h3 className={cn("text-sm font-semibold", config.titleColor)}>
                Switching to Safe Mode
              </h3>
              <p className={cn("text-sm mt-1", config.textColor)}>
                Multiple errors detected. Showing simplified content to ensure functionality.
              </p>
            </div>
          </div>
        </div>
        {fallbackContent}
      </div>
    );
  }

  return (
    <div className={cn("rounded-lg border p-4 transition-all duration-200", config.bgColor, config.borderColor, className)} role="alert" aria-live="polite">
      <div className="flex items-start gap-3">
        {showIcon && (
          <div className="flex-shrink-0">
            <config.icon className={cn("w-5 h-5", config.iconColor)} />
          </div>
        )}
        
        <div className="flex-1 min-w-0">
          {/* Error title */}
          <h3 className={cn("text-sm font-semibold", config.titleColor)}>
            {title || config.title}
          </h3>
          
          {/* Error description */}
          <p className={cn("text-sm mt-1", config.textColor)}>
            {description || config.description}
          </p>
          
          {/* Detailed error message (collapsible) */}
          <div className="mt-2">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className={cn(
                "text-xs font-medium underline hover:no-underline focus:outline-none focus:ring-2 focus:ring-offset-1 rounded",
                config.textColor,
                "focus:ring-current"
              )}
              aria-expanded={isExpanded}
              aria-controls="error-details"
            >
              {isExpanded ? 'Hide' : 'Show'} technical details
            </button>
            
            {isExpanded && (
              <div 
                id="error-details"
                className={cn(
                  "mt-2 p-3 rounded border text-xs font-mono",
                  "bg-white/50 border-current/20",
                  config.textColor
                )}
              >
                {error}
                {retryCount > 0 && (
                  <div className="mt-2 pt-2 border-t border-current/20">
                    <span className="text-xs">Retry attempts: {retryCount}</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        
        {/* Retry button */}
        {showRetry && onRetry && (
          <div className="flex-shrink-0">
            <Button
              variant={config.buttonVariant}
              size="sm"
              onClick={handleRetry}
              disabled={isRetrying}
              className={cn(
                "min-w-[80px] transition-all duration-200",
                isRetrying && "cursor-not-allowed opacity-75"
              )}
              aria-label={`${retryText}${retryCount > 0 ? ` (attempt ${retryCount + 1})` : ''}`}
            >
              {isRetrying ? (
                <>
                  <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                  <span className="text-xs">Retrying...</span>
                </>
              ) : (
                <>
                  <RefreshCw className="w-3 h-3 mr-1" />
                  <span className="text-xs">{retryText}</span>
                </>
              )}
            </Button>
          </div>
        )}
      </div>
      
      {/* Network status indicator for network errors */}
      {errorVariant === 'network' && (
        <div className="mt-3 pt-3 border-t border-current/20">
          <div className="flex items-center gap-2 text-xs">
            <div className="flex items-center gap-1">
              {navigator.onLine ? (
                <>
                  <Wifi className="w-3 h-3 text-green-500" />
                  <span className="text-green-600">Connected</span>
                </>
              ) : (
                <>
                  <WifiOff className="w-3 h-3 text-red-500" />
                  <span className="text-red-600">Offline</span>
                </>
              )}
            </div>
            <span className={config.textColor}>
              • Check your internet connection
            </span>
          </div>
        </div>
      )}
      
      {/* Retry count indicator */}
      {retryCount > 0 && (
        <div className="mt-2 text-xs opacity-75">
          <span className={config.textColor}>
            {retryCount === 1 ? '1 retry attempt' : `${retryCount} retry attempts`}
          </span>
        </div>
      )}
    </div>
  );
};