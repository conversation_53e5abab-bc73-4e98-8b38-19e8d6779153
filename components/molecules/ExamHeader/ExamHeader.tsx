'use client';

import React from 'react';
import { Clock, CheckCircle } from 'lucide-react';
import { cn } from '@/utils/cn';
import { useViewport } from '@/utils/responsive';
import { ExamTimer } from '@/components/molecules/ExamTimer';

export interface ExamHeaderProps {
  examTitle: string;
  currentQuestion: number;
  totalQuestions: number;
  answeredQuestions: number;
  timeRemaining?: number; // in minutes
  timeLimit?: number; // in minutes
  examId?: string;
  onTimeExpired?: () => void;
  className?: string;
}

export const ExamHeader: React.FC<ExamHeaderProps> = ({
  examTitle,
  currentQuestion,
  totalQuestions,
  answeredQuestions,
  timeRemaining,
  timeLimit,
  examId,
  onTimeExpired,
  className,
}) => {
  const { isMobile } = useViewport();

  // Calculate progress percentage
  const progressPercentage = Math.round((answeredQuestions / totalQuestions) * 100);

  return (
    <div className={cn(
      'bg-white/95 backdrop-blur-xl border-b border-gray-100 shadow-sm',
      className
    )}>
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-4 sm:py-5">
          {/* Left Section: Title and metadata */}
          <div className="flex items-center min-w-0 flex-1">
            <div className="min-w-0 flex-1">
              <h1 className={cn(
                'font-bold text-gray-900 truncate tracking-tight',
                isMobile ? 'text-lg' : 'text-xl'
              )}>
                {examTitle}
              </h1>
              <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                <div className="flex items-center gap-1.5">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="font-medium">Question {currentQuestion} of {totalQuestions}</span>
                </div>
                <div className="flex items-center gap-1.5">
                  <CheckCircle size={14} className="text-emerald-500" />
                  <span>{answeredQuestions} completed</span>
                </div>
              </div>
            </div>
          </div>

          {/* Center Section: Enhanced Progress bar (desktop only) */}
          {!isMobile && (
            <div className="flex-1 max-w-sm mx-8">
              <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                <span className="font-medium">Progress</span>
                <span className="font-semibold text-gray-900">{progressPercentage}%</span>
              </div>
              <div className="relative">
                <div className="w-full bg-gray-100 rounded-full h-2.5 overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full transition-all duration-500 ease-out shadow-sm"
                    style={{ width: `${progressPercentage}%` }}
                  />
                </div>
                {/* Progress dots */}
                <div className="absolute top-0 left-0 w-full h-full flex items-center">
                  {Array.from({ length: Math.min(totalQuestions, 10) }, (_, index) => {
                    const position = totalQuestions === 1 ? 50 : (index / (Math.min(totalQuestions, 10) - 1)) * 100;
                    const isAnswered = index < (answeredQuestions * Math.min(totalQuestions, 10) / totalQuestions);

                    return (
                      <div
                        key={index}
                        className="absolute transform -translate-x-1/2"
                        style={{ left: `${position}%` }}
                      >
                        <div
                          className={cn(
                            'w-2 h-2 rounded-full border-2 transition-all duration-300',
                            isAnswered
                              ? 'bg-white border-blue-500 shadow-sm'
                              : 'bg-gray-100 border-gray-200'
                          )}
                        />
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Right Section: Time and stats */}
          <div className="flex items-center gap-4 flex-shrink-0">
            {/* Mobile progress indicator */}
            {isMobile && (
              <div className="text-center bg-blue-50 rounded-lg px-3 py-2">
                <div className="text-lg font-bold text-blue-600">
                  {progressPercentage}%
                </div>
                <div className="text-xs text-blue-500 font-medium">complete</div>
              </div>
            )}


          </div>
        </div>
      </div>
    </div>
  );
};