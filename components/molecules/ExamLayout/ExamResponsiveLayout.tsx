'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/utils/cn';
import { useViewport, getResponsiveConfig, responsiveClasses } from '@/utils/responsive';

interface ExamResponsiveLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
  bottomNav?: React.ReactNode;
  className?: string;
}

export const ExamResponsiveLayout: React.FC<ExamResponsiveLayoutProps> = ({
  children,
  sidebar,
  header,
  bottomNav,
  className,
}) => {
  const { currentBreakpoint, isMobile, isTablet, isDesktop } = useViewport();
  const config = getResponsiveConfig(currentBreakpoint);
  const [mounted, setMounted] = useState(false);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <div className="min-h-screen bg-background-subtle animate-pulse">
        <div className="h-32 bg-background-default border-b border-gray-200" />
        <div className="flex">
          <div className="hidden lg:block w-80 bg-background-default border-r border-gray-200 h-screen" />
          <div className="flex-1 p-8">
            <div className="space-y-6">
              <div className="h-64 bg-background-default rounded-lg" />
              <div className="h-48 bg-background-default rounded-lg" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('bg-background-subtle', className)}>
      {/* Header - Always visible */}
      {header && (
        <div className="bg-background-default border-b border-gray-200">
          {header}
        </div>
      )}

      {/* Main Layout Container */}
      <div className="flex relative bg-gray-50 pb-8">
        {/* Desktop Sidebar */}
        {sidebar && isDesktop && (
          <div className="flex-shrink-0">
            {sidebar}
          </div>
        )}

        {/* Main Content Area */}
        <div className={cn(
          'flex-1 flex flex-col min-w-0',
          // Add padding bottom for mobile bottom navigation
          isMobile && 'pb-20',
          isTablet && 'pb-18'
        )}>
          {/* Content Container with responsive padding */}
          <div className={cn(
            'flex-1 p-4 lg:p-6',
            // Responsive spacing
            'space-y-4 md:space-y-5 lg:space-y-6'
          )}>
            <div className={cn(
              'max-w-none mx-auto',
              // Responsive max-width for better readability
              'lg:max-w-3xl xl:max-w-4xl 2xl:max-w-5xl'
            )}>
              {children}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile/Tablet Bottom Navigation */}
      {bottomNav && (isMobile || isTablet) && (
        <div className={cn(
          'fixed bottom-0 left-0 right-0 z-50',
          'bg-background-default border-t border-gray-200',
          // Safe area support for mobile devices
          'pb-safe-area-inset-bottom'
        )}>
          {bottomNav}
        </div>
      )}
    </div>
  );
};

/**
 * Responsive Grid Layout for Questions
 */
interface ExamQuestionGridProps {
  children: React.ReactNode;
  className?: string;
}

export const ExamQuestionGrid: React.FC<ExamQuestionGridProps> = ({
  children,
  className,
}) => {
  const { currentBreakpoint } = useViewport();
  const config = getResponsiveConfig(currentBreakpoint);

  return (
    <div className={cn(
      // Base grid layout
      'grid grid-cols-1',
      // Responsive gaps
      'gap-4 md:gap-5 lg:gap-6',
      className
    )}>
      {children}
    </div>
  );
};

/**
 * Responsive Card Container
 */
interface ExamCardContainerProps {
  children: React.ReactNode;
  variant?: 'question' | 'instruction' | 'navigation';
  className?: string;
}

export const ExamCardContainer: React.FC<ExamCardContainerProps> = ({
  children,
  variant = 'question',
  className,
}) => {
  const { isMobile, isTablet } = useViewport();

  const getVariantClasses = () => {
    switch (variant) {
      case 'question':
        return cn(
          'bg-white rounded-lg border border-gray-200',
          'shadow-sm',
          // Responsive padding
          'p-4 sm:p-5 lg:p-5'
        );
      case 'instruction':
        return cn(
          'bg-blue-50 border border-blue-100 rounded-lg',
          'p-4 sm:p-5 lg:p-5',
          'shadow-sm'
        );
      case 'navigation':
        return cn(
          'bg-white rounded-lg border border-gray-200',
          'p-3 sm:p-4',
          'shadow-sm'
        );
      default:
        return '';
    }
  };

  return (
    <div className={cn(
      getVariantClasses(),
      className
    )}>
      {children}
    </div>
  );
};

/**
 * Responsive Button Group
 */
interface ExamButtonGroupProps {
  children: React.ReactNode;
  orientation?: 'horizontal' | 'vertical';
  className?: string;
}

export const ExamButtonGroup: React.FC<ExamButtonGroupProps> = ({
  children,
  orientation = 'horizontal',
  className,
}) => {
  const { isMobile } = useViewport();

  return (
    <div className={cn(
      'flex',
      // Responsive orientation
      orientation === 'horizontal' 
        ? 'flex-row gap-2 sm:gap-3 lg:gap-4'
        : 'flex-col gap-2 sm:gap-3',
      // Mobile adjustments
      isMobile && orientation === 'horizontal' && 'flex-wrap',
      // Responsive alignment
      'items-center justify-center sm:justify-start',
      className
    )}>
      {children}
    </div>
  );
};

/**
 * Responsive Text Container
 */
interface ExamTextContainerProps {
  children: React.ReactNode;
  variant?: 'title' | 'content' | 'metadata';
  className?: string;
}

export const ExamTextContainer: React.FC<ExamTextContainerProps> = ({
  children,
  variant = 'content',
  className,
}) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'title':
        return responsiveClasses.text.examTitle;
      case 'content':
        return responsiveClasses.text.questionContent;
      case 'metadata':
        return responsiveClasses.text.metadata;
      default:
        return '';
    }
  };

  return (
    <div className={cn(
      getVariantClasses(),
      // Responsive line height
      'leading-relaxed sm:leading-relaxed lg:leading-loose',
      className
    )}>
      {children}
    </div>
  );
};

