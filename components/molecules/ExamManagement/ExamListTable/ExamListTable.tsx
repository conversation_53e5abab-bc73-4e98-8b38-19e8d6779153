'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getTeacherExamsAction } from '@/actions/exam.action';
import { IGetAllExamsResponse, IExamApiResponse } from '@/apis/exam';
import { ITeacherExamsQueryParams } from '@/types/exam.types';
import { Button } from '@/components/atoms/Button/Button';
import { MobileOptimizedTablePagination } from '@/components/molecules/TablePagination/MobileOptimizedTablePagination';
import { DesktopTableSkeleton } from '@/components/molecules/CustomTable/TableSkeleton';
import { AssignExamModal } from '@/components/molecules/AssignExamModal/AssignExamModal';
import {
  Users,
  TrendingUp,
  Clock,
  Calendar,
  Plus,
  FileText,
  Search,
  UserPlus
} from 'lucide-react';
import {
  extractExamSettings,
  formatTimeLimit,
  formatPassingScore,
  getExamStatusBadge,
  formatExamStatus,
  getQuestionCount,
  formatExamDate
} from '@/utils/examUtils';

export interface ExamListTableProps {
  initialData?: IGetAllExamsResponse;
  isLoading?: boolean;
  error?: string | null;
  tableTitle?: string;
  showCreateButton?: boolean;
  onCreateExam?: () => void;
  onViewResults?: (examId: string, examTitle: string) => void;
  // Pagination props for server-side pagination
  currentPageBackend?: number;
  totalPagesBackend?: number;
  totalItemsBackend?: number;
  onBackendPageChange?: (page: number) => void;
  onBackendRowsPerPageChange?: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  rowsPerPageBackend?: number;
}

const DEFAULT_PAGE_SIZE = 10;
const DEFAULT_PAGE_INDEX = 0;

export const ExamListTable: React.FC<ExamListTableProps> = ({
  initialData,
  isLoading: externalLoading = false,
  error: externalError = null,
  tableTitle = "Your Exams",
  showCreateButton = true,
  onCreateExam,
  onViewResults,
  currentPageBackend,
  totalPagesBackend,
  totalItemsBackend,
  onBackendPageChange,
  onBackendRowsPerPageChange,
  rowsPerPageBackend,
}) => {
  const router = useRouter();
  
  // Local state for data management
  const [examData, setExamData] = useState<IGetAllExamsResponse | null>(initialData || null);
  const [isLoading, setIsLoading] = useState(externalLoading);
  const [error, setError] = useState<string | null>(externalError);
  
  // Pagination state
  const [pagination, setPagination] = useState({
    pageIndex: DEFAULT_PAGE_INDEX,
    pageSize: DEFAULT_PAGE_SIZE,
  });
  
  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');

  // Modal state for assign exam
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [selectedExamForAssign, setSelectedExamForAssign] = useState<{
    id: string;
    title: string;
  } | null>(null);

  // Fetch data function
  const fetchData = useCallback(async () => {
    if (initialData && !onBackendPageChange) {
      // If initial data is provided and no backend pagination handler, use initial data
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const params: ITeacherExamsQueryParams = {
        page: pagination.pageIndex + 1,
        limit: pagination.pageSize,
        sortBy: 'createdAt',
        sortOrder: 'desc',
        status: statusFilter,
        ...(searchTerm.trim() && { search: searchTerm.trim() })
      };

      const response = await getTeacherExamsAction(params);
      
      if (response.status === 'success' && response.data) {
        setExamData(response.data);
      } else {
        setError('Failed to load exams');
      }
    } catch (err: any) {
      console.error('Error fetching exams:', err);
      setError(err.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [pagination, statusFilter, searchTerm, initialData, onBackendPageChange]);

  // Effect to fetch data when dependencies change
  useEffect(() => {
    if (!initialData || onBackendPageChange) {
      fetchData();
    }
  }, [fetchData, initialData, onBackendPageChange]);

  // Handler for pagination changes
  const handlePageChange = (newPage: number) => {
    if (onBackendPageChange) {
      onBackendPageChange(newPage);
    } else {
      setPagination((prev) => ({ ...prev, pageIndex: newPage - 1 }));
    }
  };

  // Handler for rows per page changes
  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    
    if (onBackendRowsPerPageChange) {
      onBackendRowsPerPageChange(event);
    } else {
      setPagination((prev) => ({
        ...prev,
        pageSize: newRowsPerPage,
        pageIndex: 0, // Reset to first page
      }));
    }
  };

  // Handler for viewing exam results
  const handleViewResults = (examId: string, examTitle: string) => {
    if (onViewResults) {
      onViewResults(examId, examTitle);
    } else {
      router.push(`/admin/exam-results/${examId}`);
    }
  };

  // Handler for viewing worksheet
  const handleViewWorksheet = (worksheetId: string) => {
    router.push(`/manage-worksheet?type=review&id=${worksheetId}`);
  };

  // Handler for creating new exam
  const handleCreateExam = () => {
    if (onCreateExam) {
      onCreateExam();
    } else {
      router.push('/manage-worksheet');
    }
  };

  // Handler for opening assign exam modal
  const handleAssignExam = (examId: string, examTitle: string) => {
    setSelectedExamForAssign({ id: examId, title: examTitle });
    setIsAssignModalOpen(true);
  };

  // Handler for closing assign exam modal
  const handleCloseAssignModal = () => {
    setIsAssignModalOpen(false);
    setSelectedExamForAssign(null);
  };

  // Handler for successful assignment
  const handleAssignSuccess = () => {
    // Optionally refresh the data or show a success message
    fetchData();
  };

  // Helper function to get exam settings for display
  const getExamDisplayData = (exam: IExamApiResponse) => {
    const settings = extractExamSettings(exam.selectedOptions);
    return {
      timeLimit: formatTimeLimit(settings.timeLimit),
      passingScore: formatPassingScore(settings.passingScore),
      questionCount: getQuestionCount(exam.questions),
      formattedDate: formatExamDate(exam.createdAt),
      statusBadge: getExamStatusBadge(exam.status),
      statusText: formatExamStatus(exam.status)
    };
  };

  // Calculate current pagination values
  const currentPage = currentPageBackend || pagination.pageIndex + 1;
  const currentTotalPages = totalPagesBackend || examData?.totalPages || 0;
  const currentTotalItems = totalItemsBackend || examData?.totalItems || 0;
  const rowsPerPage = rowsPerPageBackend || pagination.pageSize;

  // Error state
  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="text-center py-8">
          <div className="text-red-500 mb-2">Error loading exams</div>
          <div className="text-gray-600 text-sm">{error}</div>
          <Button 
            onClick={fetchData} 
            variant="outline" 
            className="mt-4"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{tableTitle}</h3>
            {currentTotalItems > 0 && (
              <p className="text-sm text-gray-600 mt-1">
                {currentTotalItems} total exams
              </p>
            )}
          </div>
          
          {showCreateButton && (
            <Button
              onClick={handleCreateExam}
              variant="primary"
              className="flex items-center gap-2"
              iconProps={{
                variant:'plus',
                className:'w-3'
              }}
            >
              Create New Exam
            </Button>
          )}
        </div>

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search exams..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="p-6">
          <DesktopTableSkeleton rows={5} columns={8} />
        </div>
      )}

      {/* Empty State */}
      {!isLoading && (!examData?.items.length || currentTotalItems === 0) && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No exams found</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || statusFilter !== 'all' 
              ? 'Try adjusting your search or filter criteria.'
              : 'Start by creating your first exam from a worksheet.'
            }
          </p>
          {showCreateButton && (
            <Button onClick={handleCreateExam} variant="primary">
              <Plus className="w-4 h-4 mr-2" />
              Create Your First Exam
            </Button>
          )}
        </div>
      )}

      {/* Table Content */}
      {!isLoading && examData?.items && examData.items.length > 0 && (
        <>
          {/* Desktop Table */}
          <div className="hidden md:block overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Exam Title
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Submissions
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Avg Score
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Time Limit
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {examData.items.map((exam: IExamApiResponse) => {
                  const displayData = getExamDisplayData(exam);
                  return (
                    <tr
                      key={exam.id}
                      className="hover:bg-gray-50 cursor-pointer"
                      onClick={() => handleViewResults(exam.id, exam.title)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="font-medium text-gray-900">{exam.title}</div>
                        {displayData.questionCount > 0 && (
                          <div className="text-xs text-gray-500 mt-1">
                            {displayData.questionCount} questions
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex items-center justify-center gap-1">
                          <Users className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-900">
                            {exam.totalSubmissions ?? 0}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex items-center justify-center gap-1">
                          <TrendingUp className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-900">
                            {exam.averageScore ? `${Math.round(exam.averageScore * 100) / 100}%` : '0%'}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex items-center justify-center gap-1">
                          <Clock className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-900">{displayData.timeLimit}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <span className={displayData.statusBadge}>
                          {displayData.statusText}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex items-center justify-center gap-1">
                          <Calendar className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-900">{displayData.formattedDate}</span>
                        </div>
                      </td>
                      <td
                        className="px-6 py-4 whitespace-nowrap text-center"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div className="flex items-center justify-center gap-2">
                          <Button
                            variant="outline"
                            onClick={() => handleAssignExam(exam.id, exam.title)}
                            className="flex items-center border-0 gap-1 text-sm px-3 py-1 hover:bg-green-50"
                            title="Assign exam to students"
                          >
                            <UserPlus className="w-4 h-4 text-green-600" />
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => handleViewWorksheet(exam.worksheetId)}
                            className="flex items-center border-0 gap-1 text-sm px-3 py-1 hover:bg-blue-50"
                            title="View original worksheet used for this exam"
                          >
                            <FileText className="w-4 h-4 text-blue-600" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="md:hidden">
            <div className="divide-y divide-gray-200">
              {examData.items.map((exam: IExamApiResponse) => {
                const displayData = getExamDisplayData(exam);
                return (
                  <div
                    key={exam.id}
                    className="p-4 hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleViewResults(exam.id, exam.title)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 mb-1">{exam.title}</h4>
                        {displayData.questionCount > 0 && (
                          <p className="text-xs text-gray-500 mt-1">
                            {displayData.questionCount} questions
                          </p>
                        )}
                      </div>
                      <span className={displayData.statusBadge}>
                        {displayData.statusText}
                      </span>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center gap-1">
                        <Users className="w-4 h-4" />
                        <span className="text-gray-900">
                           {exam.totalSubmissions ?? 0} submissions
                         </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <TrendingUp className="w-4 h-4" />
                        <span className="text-gray-900">
                           {exam.averageScore ? `${Math.round(exam.averageScore * 100) / 100}%` : '0%'} avg
                         </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        <span>{displayData.timeLimit}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span>{displayData.formattedDate}</span>
                      </div>
                    </div>

                    <div className="flex justify-center gap-2">
                      <Button
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAssignExam(exam.id, exam.title);
                        }}
                        className="flex items-center border-0 justify-center gap-2 hover:bg-green-50 py-3 px-4"
                        title="Assign exam to students"
                      >
                        <div className="flex items-center justify-center gap-2">
                          <UserPlus className="w-4 h-4 text-green-600" />
                          <span className="text-sm text-green-600">Assign</span>
                        </div>
                      </Button>
                      <Button
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewWorksheet(exam.worksheetId);
                        }}
                        className="flex items-center border-0 justify-center gap-2 hover:bg-blue-50 py-3 px-4"
                        title="View original worksheet used for this exam"
                      >
                        <div className="flex items-center justify-center gap-2">
                          <FileText className="w-4 h-4 text-blue-600" />
                          <span className="text-sm text-blue-600">Worksheet</span>
                        </div>
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Pagination */}
          {currentTotalItems > 0 && (
            <div className="border-t border-gray-200">
              <MobileOptimizedTablePagination
                currentPage={currentPage}
                totalPages={currentTotalPages}
                rowsPerPage={rowsPerPage}
                totalItems={currentTotalItems}
                onPageChange={handlePageChange}
                onRowsPerPageChange={handleRowsPerPageChange}
              />
            </div>
          )}
        </>
      )}

      {/* Assign Exam Modal */}
      {selectedExamForAssign && (
        <AssignExamModal
          isOpen={isAssignModalOpen}
          onClose={handleCloseAssignModal}
          examId={selectedExamForAssign.id}
          examTitle={selectedExamForAssign.title}
          onSuccess={handleAssignSuccess}
        />
      )}
    </div>
  );
};
