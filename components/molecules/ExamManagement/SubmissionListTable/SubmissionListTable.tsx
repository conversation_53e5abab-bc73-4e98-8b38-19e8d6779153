'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  IExamResultDetail,
  IExamResultsResponse,
  IExamResultsQueryParams,
  getExamResultsAction
} from '@/actions/exam.action';
import { Button } from '@/components/atoms/Button/Button';
import { MobileOptimizedTablePagination } from '@/components/molecules/TablePagination/MobileOptimizedTablePagination';
import { DesktopTableSkeleton } from '@/components/molecules/CustomTable/TableSkeleton';
import {
  CheckCircle2,
  XCircle,
  Clock,
  Calendar,
  User,
  Mail,
  Target,
  Download,
  Search,
  Eye
} from 'lucide-react';
import { cn } from '@/utils/cn';

export interface SubmissionListTableProps {
  examId: string;
  initialData?: IExamResultsResponse;
  isLoading?: boolean;
  error?: string | null;
  tableTitle?: string;
  showExportButton?: boolean;
  onExportResults?: () => void;
  // Pagination props for server-side pagination
  currentPageBackend?: number;
  totalPagesBackend?: number;
  totalItemsBackend?: number;
  onBackendPageChange?: (page: number) => void;
  onBackendRowsPerPageChange?: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  rowsPerPageBackend?: number;
}

const DEFAULT_PAGE_SIZE = 20;
const DEFAULT_PAGE_INDEX = 0;

export const SubmissionListTable: React.FC<SubmissionListTableProps> = ({
  examId,
  initialData,
  isLoading: externalLoading = false,
  error: externalError = null,
  tableTitle,
  currentPageBackend,
  totalPagesBackend,
  totalItemsBackend,
  onBackendPageChange,
  onBackendRowsPerPageChange,
  rowsPerPageBackend,
}) => {
  const router = useRouter();

  // Local state for data management
  const [submissionData, setSubmissionData] = useState<IExamResultsResponse | null>(initialData || null);
  const [isLoading, setIsLoading] = useState(externalLoading);
  const [error, setError] = useState<string | null>(externalError);
  
  // Pagination state
  const [pagination, setPagination] = useState({
    pageIndex: DEFAULT_PAGE_INDEX,
    pageSize: DEFAULT_PAGE_SIZE,
  });
  
  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [passedFilter, setPassedFilter] = useState<'all' | 'passed' | 'failed'>('all');
  const [sortBy, setSortBy] = useState<'studentName' | 'score' | 'submittedAt' | 'timeSpent'>('submittedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Fetch data function
  const fetchData = useCallback(async () => {
    if (!examId) return;
    
    if (initialData && !onBackendPageChange) {
      // If initial data is provided and no backend pagination handler, use initial data
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const params: IExamResultsQueryParams = {
        page: pagination.pageIndex + 1,
        limit: pagination.pageSize,
        sortBy,
        sortOrder,
        ...(passedFilter !== 'all' && { passed: passedFilter === 'passed' }),
        ...(searchTerm.trim() && { search: searchTerm.trim() })
      };

      const response = await getExamResultsAction(examId, params);
      console.log('response', response);
      if (response.status === 'success' && response.data) {
        setSubmissionData(response.data);
      } else {
        setError(response.message || 'Failed to load exam results');
      }
    } catch (err: any) {
      console.error('Error fetching exam results:', err);
      setError(err.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [examId, pagination, passedFilter, searchTerm, sortBy, sortOrder, initialData, onBackendPageChange]);

  // Effect to fetch data when dependencies change
  useEffect(() => {
    if (!initialData || onBackendPageChange) {
      fetchData();
    }
    console.log('initialData', initialData);
  }, [fetchData, initialData, onBackendPageChange]);

  // Handler for pagination changes
  const handlePageChange = (newPage: number) => {
    if (onBackendPageChange) {
      onBackendPageChange(newPage);
    } else {
      setPagination((prev) => ({ ...prev, pageIndex: newPage - 1 }));
    }
  };

  // Handler for rows per page changes
  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    
    if (onBackendRowsPerPageChange) {
      onBackendRowsPerPageChange(event);
    } else {
      setPagination((prev) => ({
        ...prev,
        pageSize: newRowsPerPage,
        pageIndex: 0, // Reset to first page
      }));
    }
  };

  // Format date helper
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Format time spent helper
  const formatTimeSpent = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  // Get pass/fail badge styling
  const getPassBadge = (passed: boolean) => {
    return cn(
      'inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full',
      passed
        ? 'bg-green-100 text-green-800'
        : 'bg-red-100 text-red-800'
    );
  };

  // Calculate current pagination values
  const currentPage = currentPageBackend || pagination.pageIndex + 1;
  const currentTotalPages = totalPagesBackend || submissionData?.totalPages || 0;
  const currentTotalItems = totalItemsBackend || submissionData?.totalCount || 0;
  const rowsPerPage = rowsPerPageBackend || pagination.pageSize;

  // Determine table title
  const displayTitle = tableTitle || (submissionData?.examTitle ? `${submissionData.examTitle} - Student Results` : 'Student Results');

  // Error state
  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="text-center py-8">
          <div className="text-red-500 mb-2">Error loading results</div>
          <div className="text-gray-600 text-sm">{error}</div>
          <Button 
            onClick={fetchData} 
            variant="outline" 
            className="mt-4"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{displayTitle}</h3>
            {currentTotalItems > 0 && (
              <p className="text-sm text-gray-600 mt-1">
                {currentTotalItems} total submissions
              </p>
            )}
          </div>
        </div>

        {/* Stats Summary */}
        {submissionData?.stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">{submissionData.stats.totalSubmissions}</div>
              <div className="text-sm text-gray-600">Total Submissions</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">{submissionData.stats.averageScore.toFixed(1)}%</div>
              <div className="text-sm text-gray-600">Average Score</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">{submissionData.stats.passRate.toFixed(1)}%</div>
              <div className="text-sm text-gray-600">Pass Rate</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">{formatTimeSpent(submissionData.stats.averageTimeSpent)}</div>
              <div className="text-sm text-gray-600">Avg Time</div>
            </div>
          </div>
        )}

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search students..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <select
            value={passedFilter}
            onChange={(e) => setPassedFilter(e.target.value as 'all' | 'passed' | 'failed')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Results</option>
            <option value="passed">Passed Only</option>
            <option value="failed">Failed Only</option>
          </select>

          <select
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [newSortBy, newSortOrder] = e.target.value.split('-') as [typeof sortBy, typeof sortOrder];
              setSortBy(newSortBy);
              setSortOrder(newSortOrder);
            }}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="submittedAt-desc">Latest First</option>
            <option value="submittedAt-asc">Oldest First</option>
            <option value="score-desc">Highest Score</option>
            <option value="score-asc">Lowest Score</option>
            <option value="studentName-asc">Name A-Z</option>
            <option value="studentName-desc">Name Z-A</option>
            <option value="timeSpent-desc">Most Time</option>
            <option value="timeSpent-asc">Least Time</option>
          </select>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="p-6">
          <DesktopTableSkeleton rows={5} columns={6} />
        </div>
      )}

      {/* Empty State */}
      {!isLoading && (!submissionData?.results.length) && (
        <div className="text-center py-12">
          <Target className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No submissions found</h3>
          <p className="text-gray-600">
            {searchTerm || passedFilter !== 'all' 
              ? 'Try adjusting your search or filter criteria.'
              : 'No students have submitted this exam yet.'
            }
          </p>
        </div>
      )}

      {/* Table Content */}
      {!isLoading && submissionData?.results.length > 0 && (
        <>
          {/* Desktop Table */}
          <div className="hidden md:block overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Student
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Score
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Correct/Total
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Time Spent
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Submitted
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {submissionData.results.map((result) => (
                  <tr key={`submissionData-${result.id}`} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <User className="w-4 h-4 text-gray-400 mr-2" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">{result.studentName}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <span className={cn(
                        'text-lg font-semibold',
                        result.passed ? 'text-green-600' : 'text-red-600'
                      )}>
                        {result.score.toFixed(1)}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <span className="text-sm text-gray-900">
                        {result.correctAnswers}/{result.totalQuestions}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="flex items-center justify-center gap-1">
                        <Clock className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-900">{formatTimeSpent(result.timeSpent)}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <span className={getPassBadge(result.passed)}>
                        {result.passed ? (
                          <React.Fragment key="passed">
                            <CheckCircle2 className="w-3 h-3" />
                            Passed
                          </React.Fragment>
                        ) : (
                          <React.Fragment key="failed">
                            <XCircle className="w-3 h-3" />
                            Failed
                          </React.Fragment>
                        )}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="flex items-center justify-center gap-1">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-900">{formatDate(result.submittedAt)}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <Button
                        variant="outline"
                        onClick={() => router.push(`/admin/exam-results/${examId}/student/${result.studentId}`)}
                        className="flex items-center gap-1"
                        iconProps={{
                          variant:'eye',
                          className:'w-4'
                        }}
                      >
                        View Details
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="md:hidden">
            <div className="divide-y divide-gray-200">
              {submissionData.results.map((result) => (
                <div key={`submissionDatamb-${result.id}`} className="p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="flex items-center mb-1">
                        <User className="w-4 h-4 text-gray-400 mr-2" />
                        <h4 className="font-medium text-gray-900">{result.studentName}</h4>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={cn(
                        'text-lg font-semibold mb-1',
                        result.passed ? 'text-green-600' : 'text-red-600'
                      )}>
                        {result.score.toFixed(1)}%
                      </div>
                      <span className={getPassBadge(result.passed)}>
                        {result.passed ? (
                          <React.Fragment key="passed">
                            <CheckCircle2 className="w-3 h-3" />
                            Passed
                          </React.Fragment>
                        ) : (
                          <React.Fragment key="failed">
                            <XCircle className="w-3 h-3" />
                            Failed
                          </React.Fragment>
                        )}
                      </span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mt-3">
                    <div>
                      <span className="font-medium">Correct:</span> {result.correctAnswers}/{result.totalQuestions}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>{formatTimeSpent(result.timeSpent)}</span>
                    </div>
                    <div className="flex items-center gap-1 col-span-2">
                      <Calendar className="w-4 h-4" />
                      <span>Submitted: {formatDate(result.submittedAt)}</span>
                    </div>
                  </div>

                  {/* Mobile Action Button */}
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <Button
                      variant="outline"
                      onClick={() => router.push(`/admin/exam-results/${examId}/student/${result.studentId}`)}
                      className="w-full flex items-center justify-center gap-2"
                      iconProps={{
                        variant:'eye',
                        className:'w-4'
                      }}
                    >
                      View Detailed Results
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Pagination */}
          {currentTotalItems > 0 && (
            <div className="border-t border-gray-200">
              <MobileOptimizedTablePagination
                currentPage={currentPage}
                totalPages={currentTotalPages}
                rowsPerPage={rowsPerPage}
                totalItems={currentTotalItems}
                onPageChange={handlePageChange}
                onRowsPerPageChange={handleRowsPerPageChange}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};
