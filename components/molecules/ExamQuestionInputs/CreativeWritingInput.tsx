'use client';

import React, { useState, useEffect, useRef } from 'react';
import { UseFormRegister, UseFormSetValue, UseFormWatch, FieldError } from 'react-hook-form';
import { cn } from '@/utils/cn';
import { ExamFormData } from '@/components/organisms/ExamTaking/ExamTaking.schema';
import { CheckCircle2, AlertCircle, Target, TrendingUp, Type, Maximize2 } from 'lucide-react';

interface CreativeWritingInputProps {
  questionIndex: number;
  content: string;
  register: UseFormRegister<ExamFormData>;
  setValue: UseFormSetValue<ExamFormData>;
  watch: UseFormWatch<ExamFormData>;
  error?: FieldError;
  minWords?: number;
  maxWords?: number;
  className?: string;
}

export const CreativeWritingInput: React.FC<CreativeWritingInputProps> = ({
  questionIndex,
  content,
  register,
  setValue,
  watch,
  error,
  minWords = 50,
  maxWords = 500,
  className,
}) => {
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const [hasStartedWriting, setHasStartedWriting] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  // Watch the current answer for this question
  const currentAnswer = watch(`answers.${questionIndex}.userAnswer.0`) || '';

  // Update word and character count when answer changes
  useEffect(() => {
    const words = currentAnswer.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(currentAnswer.trim() === '' ? 0 : words.length);
    setCharCount(currentAnswer.length);
    setHasStartedWriting(currentAnswer.trim().length > 0);
  }, [currentAnswer]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = Math.max(textareaRef.current.scrollHeight, 200) + 'px';
    }
  }, [currentAnswer]);

  const handleTextChange = (value: string) => {
    setValue(`answers.${questionIndex}.userAnswer.0`, value);
  };

  const isWordCountValid = wordCount >= minWords && wordCount <= maxWords;
  const progressPercentage = Math.min((wordCount / maxWords) * 100, 100);
  const minProgressPercentage = (minWords / maxWords) * 100;
  
  const getWordCountStatus = () => {
    if (wordCount === 0) return { color: 'text-gray-500', status: 'Not started' };
    if (wordCount < minWords) return { color: 'text-orange-600', status: 'Keep writing' };
    if (wordCount > maxWords) return { color: 'text-red-600', status: 'Too many words' };
    return { color: 'text-green-600', status: 'Perfect length' };
  };

  const wordCountStatus = getWordCountStatus();

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Writing Guidelines & Progress */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Word Count Guidelines */}
        <div className="p-4 bg-background-subtle rounded-xl border border-gray-200">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <Target className="w-4 h-4 text-primary-action" />
              <span className="text-sm font-medium text-text-primary">
                Target: {minWords} - {maxWords} words
              </span>
            </div>
            <span className={cn('text-sm font-semibold', wordCountStatus.color)}>
              {wordCount} words
            </span>
          </div>
          
          {/* Progress Bar */}
          <div className="relative w-full bg-gray-200 rounded-full h-3 overflow-hidden">
            {/* Minimum threshold indicator */}
            <div 
              className="absolute top-0 h-full w-0.5 bg-orange-400 z-10"
              style={{ left: `${minProgressPercentage}%` }}
            />
            
            {/* Progress fill */}
            <div
              className={cn(
                'h-full rounded-full transition-all duration-500 ease-out',
                wordCount === 0 && 'bg-gray-300',
                wordCount > 0 && wordCount < minWords && 'bg-gradient-to-r from-orange-400 to-orange-500',
                wordCount >= minWords && wordCount <= maxWords && 'bg-gradient-to-r from-green-400 to-green-500',
                wordCount > maxWords && 'bg-gradient-to-r from-red-400 to-red-500'
              )}
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          
          <div className="flex items-center justify-between mt-2">
            <span className="text-xs text-text-secondary">0</span>
            <span className={cn('text-xs font-medium', wordCountStatus.color)}>
              {wordCountStatus.status}
            </span>
            <span className="text-xs text-text-secondary">{maxWords}</span>
          </div>
        </div>
        
        {/* Writing Stats */}
        <div className="p-4 bg-background-subtle rounded-xl border border-gray-200">
          <div className="flex items-center space-x-2 mb-3">
            <TrendingUp className="w-4 h-4 text-primary-action" />
            <span className="text-sm font-medium text-text-primary">Writing Stats</span>
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center">
              <div className="text-lg font-semibold text-text-primary">{charCount}</div>
              <div className="text-xs text-text-secondary">Characters</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-text-primary">
                {currentAnswer.split('\n').length}
              </div>
              <div className="text-xs text-text-secondary">Paragraphs</div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Enhanced Text Editor */}
      <div className="space-y-4">
        <div className={cn(
          'relative rounded-xl border-2 transition-all duration-200',
          isFullscreen && 'fixed inset-4 z-50 bg-white shadow-2xl',
          !error && wordCount === 0 && 'border-gray-300',
          !error && wordCount > 0 && wordCount < minWords && 'border-orange-300',
          !error && isWordCountValid && 'border-green-300',
          !error && wordCount > maxWords && 'border-red-300',
          error && 'border-red-300'
        )}>
          {/* Editor Header */}
          <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50 rounded-t-xl">
            <div className="flex items-center space-x-2">
              <Type className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                Write your response
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500">
                {charCount} characters
              </span>
              <button
                type="button"
                onClick={toggleFullscreen}
                className="p-1 hover:bg-gray-200 rounded transition-colors"
                title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
              >
                <Maximize2 className="w-4 h-4 text-gray-500" />
              </button>
            </div>
          </div>

          {/* Enhanced Textarea */}
          <textarea
            {...register(`answers.${questionIndex}.userAnswer.0` as const, {
              required: false
            })}
            ref={(e) => {
              // Merge both refs
              if (textareaRef.current !== e) {
                textareaRef.current = e;
              }
            }}
            onChange={(e) => handleTextChange(e.target.value)}
            className={cn(
              'w-full p-6 resize-none outline-none rounded-b-xl',
              'text-base leading-relaxed font-normal',
              'placeholder-gray-400',
              'transition-all duration-200',
              isFullscreen ? 'min-h-[calc(100vh-12rem)]' : 'min-h-48',
              // Background states
              !error && wordCount === 0 && 'bg-white',
              !error && wordCount > 0 && wordCount < minWords && 'bg-orange-50',
              !error && isWordCountValid && 'bg-green-50',
              !error && wordCount > maxWords && 'bg-red-50',
              error && 'bg-red-50'
            )}
            placeholder="Start writing your response here... Take your time to craft a thoughtful and well-structured answer."
            style={{
              lineHeight: '1.6',
              fontFamily: 'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif'
            }}
          />

          {/* Live Word Count Indicator */}
          <div className="absolute bottom-3 right-3 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-md border shadow-sm">
            <span className={cn('text-xs font-medium', wordCountStatus.color)}>
              {wordCount}/{maxWords}
            </span>
          </div>
        </div>
        
        {/* Real-time Feedback */}
        {hasStartedWriting && (
          <div className={cn(
            'p-4 rounded-lg border-2 transition-all duration-200',
            isWordCountValid && 'bg-green-50 border-green-200',
            wordCount < minWords && 'bg-orange-50 border-orange-200',
            wordCount > maxWords && 'bg-red-50 border-red-200'
          )}>
            <div className="flex items-center space-x-3">
              {isWordCountValid ? (
                <CheckCircle2 className="w-5 h-5 text-green-500" />
              ) : (
                <AlertCircle className="w-5 h-5 text-orange-500" />
              )}
              <div className="flex-1">
                {wordCount < minWords && (
                  <div>
                    <p className="text-sm font-medium text-orange-800">
                      Keep writing! You need {minWords - wordCount} more words.
                    </p>
                    <p className="text-xs text-orange-600 mt-1">
                      You&apos;re {Math.round((wordCount / minWords) * 100)}% of the way to the minimum.
                    </p>
                  </div>
                )}
                
                {wordCount > maxWords && (
                  <div>
                    <p className="text-sm font-medium text-red-800">
                      Please reduce your response by {wordCount - maxWords} words.
                    </p>
                    <p className="text-xs text-red-600 mt-1">
                      You&apos;re {Math.round(((wordCount - maxWords) / maxWords) * 100)}% over the limit.
                    </p>
                  </div>
                )}
                
                {isWordCountValid && (
                  <div>
                    <p className="text-sm font-medium text-green-800">
                      Excellent! Your response meets the requirements.
                    </p>
                    <p className="text-xs text-green-600 mt-1">
                      You have {maxWords - wordCount} words remaining if you want to add more.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
        
        {/* Writing Tips */}
        {!hasStartedWriting && (
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-start space-x-3">
              <Type className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-blue-800 mb-2">
                  Writing Tips
                </p>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li>• Plan your response before you start writing</li>
                  <li>• Use clear and concise language</li>
                  <li>• Support your points with examples when relevant</li>
                  <li>• Review and edit your work before submitting</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Error Message */}
      {error && (
        <div className="p-4 bg-red-50 border-2 border-red-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
            <div>
              <p className="text-sm text-red-600 font-medium">
                {error.message || 'Please provide a response that meets the word count requirements'}
              </p>
              {!isWordCountValid && wordCount > 0 && (
                <p className="text-xs text-red-500 mt-1">
                  Current word count: {wordCount} (Required: {minWords}-{maxWords})
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Fullscreen Overlay */}
      {isFullscreen && (
        <div 
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
          onClick={toggleFullscreen}
        />
      )}
    </div>
  );
};
