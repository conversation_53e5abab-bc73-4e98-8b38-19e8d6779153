'use client';

import React from 'react';
import { UseFormRegister, Control, UseFormWatch, UseFormSetValue, FieldError } from 'react-hook-form';
import { AlertTriangle } from 'lucide-react';

import { SingleChoiceInput } from './SingleChoiceInput';
import { MultipleChoiceInput } from './MultipleChoiceInput';
import { FillBlankInput } from './FillBlankInput';
import { CreativeWritingInput } from './CreativeWritingInput';
import { IExamQuestion } from '@/types/exam.types';

// Define the form data structure for exam answers
export interface ExamFormData {
  answers: Array<{
    questionIndex: number;
    userAnswer: string[];
  }>;
}

export interface ExamQuestionRendererProps {
  question: IExamQuestion;
  questionIndex: number;
  register: UseFormRegister<ExamFormData>;
  control: Control<ExamFormData>;
  watch: UseFormWatch<ExamFormData>;
  setValue: UseFormSetValue<ExamFormData>;
  error?: FieldError;
  className?: string;
}

export const ExamQuestionRenderer: React.FC<ExamQuestionRendererProps> = ({
  question,
  questionIndex,
  register,
  control,
  watch,
  setValue,
  error,
  className,
}) => {
  // Render the appropriate exam input component based on question type
  switch (question.type) {
    case 'single_choice':
      return (
        <SingleChoiceInput
          questionIndex={questionIndex}
          content={question.content}
          options={question.options || []}
          register={register}
          watch={watch}
          setValue={setValue}
          error={error}
          className={className}
        />
      );

    case 'multiple_choice':
      return (
        <MultipleChoiceInput
          questionIndex={questionIndex}
          content={question.content}
          options={question.options || []}
          control={control}
          error={error}
          className={className}
        />
      );

    case 'fill_blank':
      return (
        <FillBlankInput
          questionIndex={questionIndex}
          content={question.content}
          setValue={setValue}
          watch={watch}
          error={error}
          className={className}
        />
      );

    case 'creative_writing':
      return (
        <CreativeWritingInput
          questionIndex={questionIndex}
          content={question.content}
          register={register}
          setValue={setValue}
          watch={watch}
          error={error}
          className={className}
        />
      );

    default:
      return (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-yellow-600" />
            <span className="text-yellow-800">
              Unsupported question type: {question.type}
            </span>
          </div>
        </div>
      );
  }
};
