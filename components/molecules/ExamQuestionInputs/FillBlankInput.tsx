'use client';

import React, { useMemo, useCallback, useState, useEffect } from 'react';
import { UseFormSetValue, UseFormWatch, FieldError } from 'react-hook-form';
import { cn } from '@/utils/cn';
import { ExamFormData } from '@/components/organisms/ExamTaking/ExamTaking.schema';
import { CheckCircle2, AlertCircle, Edit3 } from 'lucide-react';
import { MathContentRenderer } from '@/components/molecules/MathContentRenderer';

type TextPart = {
  type: 'text';
  content: string;
};

type BlankPart = {
  type: 'blank';
  index: number;
  originalPattern: string;
};

type ContentPart = TextPart | BlankPart;

interface FillBlankInputProps {
  questionIndex: number;
  content: string;
  setValue: UseFormSetValue<ExamFormData>;
  watch: UseFormWatch<ExamFormData>;
  error?: FieldError;
  className?: string;
}

export const FillBlankInput: React.FC<FillBlankInputProps> = ({
  questionIndex,
  content,
  setValue,
  watch,
  error,
  className,
}) => {
  const [userAnswers, setUserAnswers] = useState<string[]>([]);
  const [focusedBlank, setFocusedBlank] = useState<number | null>(null);
  const [animatingBlanks, setAnimatingBlanks] = useState<Set<number>>(new Set());
  
  // Initialize user answers from form state
  useEffect(() => {
    // Watch the current answers for this question
    const currentAnswers = watch(`answers.${questionIndex}.userAnswer`) || [''];
    if (currentAnswers.length > 0 && currentAnswers[0] !== '') {
      setUserAnswers(currentAnswers);
    }
  }, [watch, questionIndex]);

  // Process content to identify blanks and text parts with improved accuracy
  const processContent = useCallback((text: string): ContentPart[] => {
    if (!text) return [];

    // Development logging
    if (process.env.NODE_ENV === 'development') {
      console.log('Processing fill blank content:', text);
    }
    
    // Robust regex for common blank patterns
    const blankPatterns = /_{3,}|___\*|___\.|___,|___!|___\?|\[blank\]|\{blank\}|\(__+\)/g;
    
    // Find all matches with their positions
    const matches = [];
    let match;
    
    while ((match = blankPatterns.exec(text)) !== null) {
      // Additional validation to filter out false matches
      const beforeChar = match.index > 0 ? text[match.index - 1] : '';
      const afterChar = match.index + match[0].length < text.length ? text[match.index + match[0].length] : '';
      
      // Skip if the underscore pattern is part of a number (like 345,678)
      if (/\d/.test(beforeChar) && /[\d,]/.test(afterChar)) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Skipping number pattern:', match[0]);
        }
        continue;
      }
      
      // Skip if underscore is part of a word/variable name
      if (/[a-zA-Z_]/.test(beforeChar) || /[a-zA-Z_]/.test(afterChar)) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Skipping word pattern:', match[0]);
        }
        continue;
      }
      
      matches.push({
        pattern: match[0],
        index: match.index,
        length: match[0].length
      });
    }

    // Conservative fallback if too many blanks detected (likely parsing error)
    if (matches.length > 5) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('Too many blanks detected, using conservative parsing');
      }
      
      const conservativeMatches = [];
      const conservativePattern = /_{3,}/g;
      let conservativeMatch;
      
      while ((conservativeMatch = conservativePattern.exec(text)) !== null) {
        // Only include if surrounded by spaces or punctuation
        const beforeChar = conservativeMatch.index > 0 ? text[conservativeMatch.index - 1] : ' ';
        const afterChar = conservativeMatch.index + conservativeMatch[0].length < text.length ? 
          text[conservativeMatch.index + conservativeMatch[0].length] : ' ';
        
        if (/[\s.,!?;:]/.test(beforeChar) && /[\s.,!?;:]/.test(afterChar)) {
          conservativeMatches.push({
            pattern: conservativeMatch[0],
            index: conservativeMatch.index,
            length: conservativeMatch[0].length
          });
        }
      }
      
      if (conservativeMatches.length > 0 && conservativeMatches.length < matches.length) {
        matches.length = 0;
        matches.push(...conservativeMatches);
      }
    }

    // Sort matches by position
    matches.sort((a, b) => a.index - b.index);

    const processed: ContentPart[] = [];
    let lastIndex = 0;
    let blankIndex = 0;

    // Process text segments and blanks in order
    for (const match of matches) {
      // Add text segment before this blank
      if (match.index > lastIndex) {
        const textContent = text.substring(lastIndex, match.index);
        if (textContent.trim()) {
          processed.push({
            type: 'text',
            content: textContent,
          } as TextPart);
        }
      }

      // Add the blank
      processed.push({
        type: 'blank',
        index: blankIndex,
        originalPattern: match.pattern,
      } as BlankPart);

      blankIndex++;
      lastIndex = match.index + match.length;
    }

    // Add remaining text after the last blank
    if (lastIndex < text.length) {
      const remainingText = text.substring(lastIndex);
      if (remainingText.trim()) {
        processed.push({
          type: 'text',
          content: remainingText,
        } as TextPart);
      }
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('Processed content parts:', processed);
    }
    
    return processed;
  }, []);

  const contentParts = useMemo(() => processContent(content), [content, processContent]);
  
  // Count the number of blanks
  const blankCount = contentParts.filter(part => part.type === 'blank').length;
  const filledBlanks = userAnswers.filter(answer => answer.trim() !== '').length;
  const completionPercentage = blankCount > 0 ? (filledBlanks / blankCount) * 100 : 0;

  // Handle user answer change
  const handleUserAnswerChange = useCallback((blankIndex: number, value: string) => {
    // Add animation state
    setAnimatingBlanks(prev => new Set(prev).add(blankIndex));
    
    const newAnswers = [...userAnswers];
    
    // Ensure the array is large enough
    while (newAnswers.length <= blankIndex) {
      newAnswers.push('');
    }
    
    newAnswers[blankIndex] = value;
    setUserAnswers(newAnswers);
    
    // Update form state
    setValue(`answers.${questionIndex}.userAnswer`, newAnswers);
    
    // Remove animation state after animation completes
    setTimeout(() => {
      setAnimatingBlanks(prev => {
        const newSet = new Set(prev);
        newSet.delete(blankIndex);
        return newSet;
      });
    }, 300);
  }, [userAnswers, setValue, questionIndex]);

  const handleBlankFocus = (blankIndex: number) => {
    setFocusedBlank(blankIndex);
  };

  const handleBlankBlur = () => {
    setFocusedBlank(null);
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header with Progress */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-base font-semibold text-text-primary flex items-center space-x-2">
            <Edit3 className="w-5 h-5 text-primary-action" />
            <span>Fill in the blanks</span>
          </h4>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-text-secondary">
              {filledBlanks}/{blankCount} completed
            </span>
            <div className={cn(
              'w-3 h-3 rounded-full transition-colors duration-200',
              completionPercentage === 100 ? 'bg-green-500' : 'bg-primary-action'
            )} />
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
          <div 
            className="h-full bg-gradient-to-r from-primary-action to-blue-500 transition-all duration-500 ease-out"
            style={{ width: `${completionPercentage}%` }}
          />
        </div>
      </div>
      
            {/* Content with Blanks */}
      <div className="bg-section-bg-accent border-2 border-blue-200 rounded-xl p-6 shadow-sm">
        <div className="text-base leading-relaxed">
          {contentParts.map((part: ContentPart, index: number) => {
            if (part.type === 'text') {
              // Strip HTML tags and render as clean text
              const cleanText = (part.content || '').replace(/<[^>]*>/g, '');
              return (
                <span key={`text-${index}`} className="align-baseline">
                  {cleanText}
                </span>
              );
            } else {
              return (
                <span key={`input-${index}`} className="inline-block align-baseline mx-1">
                  <input
                    type="text"
                    className={cn(
                      'border-1 bg-transparent text-center outline-none',
                      'text-base font-medium placeholder-gray-400',
                      'transition-all duration-200',
                      // Sizing to match text baseline
                      'min-w-12 max-w-32 px-2 py-1',
                      // Vertical alignment
                      'align-baseline',
                      // Base styling - subtle background instead of borders
                      'bg-background-default rounded-md',
                      // Focus state - enhanced background
                      focusedBlank === (part as BlankPart).index && 'bg-blue-100 ring-2 ring-blue-300 ring-opacity-50',
                      // Filled state - success styling
                      userAnswers[(part as BlankPart).index]?.trim() 
                        ? 'bg-green-100 text-green-800' 
                        : 'hover:bg-gray-200',
                      // Error state
                      error && 'bg-red-100 text-red-700 ring-2 ring-red-300 ring-opacity-50',
                      // Animation state
                      animatingBlanks.has((part as BlankPart).index) && 'animate-pulse'
                    )}
                    value={userAnswers[(part as BlankPart).index] || ''}
                    onChange={(e) => handleUserAnswerChange((part as BlankPart).index, e.target.value)}
                    onFocus={() => handleBlankFocus((part as BlankPart).index)}
                    onBlur={handleBlankBlur}
                    style={{ 
                      width: Math.max(48, (userAnswers[(part as BlankPart).index] || '').length * 10 + 16) + 'px',
                      height: '1.5em',
                      lineHeight: '1.5',
                      verticalAlign: 'baseline'
                    }}
                  />
                  
                  {/* Subtle Status Indicator */}
                  {userAnswers[(part as BlankPart).index]?.trim() && (
                    <CheckCircle2 className="w-3 h-3 text-green-500 absolute -top-1 -right-1" />
                  )}
                </span>
              );
            }
          })}
        </div>
      </div>
      
      {/* Blank Status Overview */}
      {blankCount > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {/* Progress Summary */}
          <div className={cn(
            'p-4 rounded-lg border-2 transition-all duration-200',
            completionPercentage === 100 
              ? 'bg-green-50 border-green-200' 
              : 'bg-blue-50 border-blue-200'
          )}>
            <div className="flex items-center space-x-3">
              {completionPercentage === 100 ? (
                <CheckCircle2 className="w-5 h-5 text-green-500" />
              ) : (
                <Edit3 className="w-5 h-5 text-primary-action" />
              )}
              <div>
                <p className={cn(
                  'text-sm font-medium',
                  completionPercentage === 100 ? 'text-green-800' : 'text-primary-action'
                )}>
                  {completionPercentage === 100 ? 'All blanks completed!' : 'Keep filling the blanks'}
                </p>
                <p className="text-xs text-text-secondary">
                  {Math.round(completionPercentage)}% complete
                </p>
              </div>
            </div>
          </div>
          
          {/* Blank Counter */}
          <div className="p-4 bg-background-subtle rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  {Array.from({ length: Math.min(blankCount, 5) }).map((_, i) => (
                    <div
                      key={i}
                      className={cn(
                        'w-2 h-2 rounded-full transition-colors duration-200',
                        i < filledBlanks ? 'bg-green-500' : 'bg-gray-300'
                      )}
                    />
                  ))}
                  {blankCount > 5 && (
                    <span className="text-xs text-text-secondary ml-1">
                      +{blankCount - 5}
                    </span>
                  )}
                </div>
              </div>
              <span className="text-sm font-medium text-text-primary">
                {filledBlanks}/{blankCount}
              </span>
            </div>
          </div>
        </div>
      )}
      
      {/* Error Message */}
      {error && (
        <div className="p-4 bg-red-50 border-2 border-red-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
            <div>
              <p className="text-sm text-red-600 font-medium">
                {error.message || 'Please fill in all the blanks'}
              </p>
              <p className="text-xs text-red-500 mt-1">
                {blankCount - filledBlanks} blank{blankCount - filledBlanks !== 1 ? 's' : ''} remaining
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
