'use client';

import React, { useState } from 'react';
import { Control, Controller, FieldError } from 'react-hook-form';
import { cn } from '@/utils/cn';
import { ExamFormData } from '@/components/organisms/ExamTaking/ExamTaking.schema';
import { Check } from 'lucide-react';
import { MathContentRenderer } from '@/components/molecules/MathContentRenderer';
import { mathMLToText, containsMathML } from '@/utils/mathUtils';

interface MultipleChoiceInputProps {
  questionIndex: number;
  content: string;
  options: string[];
  control: Control<ExamFormData>;
  error?: FieldError;
  className?: string;
}

export const MultipleChoiceInput: React.FC<MultipleChoiceInputProps> = ({
  questionIndex,
  content,
  options,
  control,
  error,
  className,
}) => {
  const [animatingOptions, setAnimatingOptions] = useState<Set<string>>(new Set());

  // Helper function to get clean text for preview chips
  const getPreviewText = (option: string): string => {
    if (containsMathML(option)) {
      // Convert MathML to readable text
      const cleanText = mathMLToText(option);
      return cleanText.length > 20 ? `${cleanText.substring(0, 20)}...` : cleanText;
    }
    // For regular text, strip HTML tags and truncate
    const textOnly = option.replace(/<[^>]*>/g, '').trim();
    return textOnly.length > 20 ? `${textOnly.substring(0, 20)}...` : textOnly;
  };

  return (
    <Controller
      name={`answers.${questionIndex}`}
      control={control}
      render={({ field: { value, onChange } }) => {
        // Ensure we have a proper answer structure
        const currentAnswer = value || { questionIndex, userAnswer: [''] };
        const selectedOptions = Array.isArray(currentAnswer.userAnswer) && 
          currentAnswer.userAnswer.length > 0 && 
          currentAnswer.userAnswer[0] !== '' ? currentAnswer.userAnswer : [];

        const handleOptionChange = (option: string, checked: boolean) => {
          // Add animation state
          setAnimatingOptions(prev => new Set(prev).add(option));

          let newSelectedOptions: string[];

          if (checked) {
            newSelectedOptions = [...selectedOptions, option];
          } else {
            newSelectedOptions = selectedOptions.filter(opt => opt !== option);
          }

          // Update form state immediately with complete answer structure
          const formValue = {
            questionIndex,
            userAnswer: newSelectedOptions.length > 0 ? newSelectedOptions : ['']
          };
          
          console.log('MultipleChoiceInput - updating form value:', {
            questionIndex,
            option,
            checked,
            newSelectedOptions,
            formValue,
            currentFormValue: value
          });
          
          onChange(formValue);

          // Remove animation state after animation completes
          setTimeout(() => {
            setAnimatingOptions(prev => {
              const newSet = new Set(prev);
              newSet.delete(option);
              return newSet;
            });
          }, 200);
        };

        return (
          <div className={cn('space-y-4', className)}>
            {/* Instruction */}
            <div className="mb-6">
              <p className="text-sm text-text-secondary font-medium mb-2">
                Select all that apply:
              </p>
              <div className="h-1 w-full bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-primary-action transition-all duration-300 ease-out"
                  style={{ width: `${(selectedOptions.length / options.length) * 100}%` }}
                />
              </div>
            </div>

            {/* Options */}
            <div className="space-y-3">
              {options.map((option, optionIndex) => {
                const isSelected = selectedOptions.includes(option);
                const isAnimating = animatingOptions.has(option);

                return (
                  <label
                    key={optionIndex}
                    className={cn(
                      'group relative flex items-start space-x-4 p-4 rounded-xl border-2 cursor-pointer',
                      'transition-all duration-200 ease-out transform',
                      'hover:scale-[1.02] hover:shadow-md active:scale-[0.98]',
                      // Base states
                      !isSelected && !error && 'border-gray-200 bg-background-default hover:border-blue-300 hover:bg-accent-bg-light',
                      // Selected state
                      isSelected && 'border-primary-action bg-accent-bg-light shadow-sm',
                      // Error state
                      error && 'border-red-300 bg-red-50',
                      // Animation state
                      isAnimating && 'animate-pulse',
                      // Focus state
                      'focus-within:ring-4 focus-within:ring-primary-action focus-within:ring-opacity-20'
                    )}
                  >
                    {/* Custom Checkbox */}
                    <div className="relative flex-shrink-0 mt-0.5">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={(e) => handleOptionChange(option, e.target.checked)}
                        className="sr-only"
                      />
                      <div className={cn(
                        'w-5 h-5 rounded-md border-2 flex items-center justify-center transition-all duration-200',
                        isSelected
                          ? 'border-primary-action bg-primary-action shadow-sm'
                          : 'border-gray-300 bg-background-default group-hover:border-primary-action'
                      )}>
                        <Check
                          className={cn(
                            'w-3 h-3 text-white transition-all duration-200',
                            isSelected ? 'opacity-100 scale-100' : 'opacity-0 scale-50'
                          )}
                        />
                      </div>
                    </div>

                    {/* Option Text with MathML Support */}
                    <div className={cn(
                      'flex-1 leading-relaxed transition-colors duration-200',
                      isSelected ? 'text-text-primary font-medium' : 'text-text-primary',
                      'group-hover:text-text-primary'
                    )}>
                      <MathContentRenderer
                        content={option}
                        fallbackToText={true}
                        onRenderError={(error) => {
                          console.warn('Math rendering error in option:', optionIndex, error);
                        }}
                      />
                    </div>

                    {/* Selection Indicator */}
                    {isSelected && (
                      <div className="flex-shrink-0">
                        <div className="w-2 h-2 bg-primary-action rounded-full animate-pulse" />
                      </div>
                    )}
                  </label>
                );
              })}
            </div>

            {/* Enhanced Selection Summary */}
            <div className="mt-6 p-4 bg-section-bg-accent rounded-lg border border-blue-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={cn(
                    'w-3 h-3 rounded-full transition-colors duration-200',
                    selectedOptions.length > 0 ? 'bg-green-500' : 'bg-gray-300'
                  )} />
                  <span className="text-sm font-medium text-text-primary">
                    {selectedOptions.length > 0
                      ? `${selectedOptions.length} option${selectedOptions.length !== 1 ? 's' : ''} selected`
                      : 'No options selected'
                    }
                  </span>
                </div>

                {selectedOptions.length > 0 && (
                  <div className="text-xs text-text-secondary">
                    {Math.round((selectedOptions.length / options.length) * 100)}% complete
                  </div>
                )}
              </div>

              {/* Selected Options Preview with Mathematical Formulas */}
              {selectedOptions.length > 0 && (
                <div className="mt-3 flex flex-wrap gap-2">
                  {selectedOptions.slice(0, 3).map((option, index) => (
                    <div
                      key={index}
                      className="inline-flex items-center px-3 py-2 bg-primary-action text-white text-sm rounded-full max-w-xs"
                    >
                      <div className="truncate">
                        <MathContentRenderer
                          content={option}
                          className="text-white [&_math]:text-white [&_.math-fallback]:bg-blue-700 [&_.math-fallback]:border-blue-600 [&_.math-fallback]:text-white"
                          fallbackToText={true}
                          onRenderError={(error) => {
                            console.warn('Math rendering error in preview:', error);
                          }}
                        />
                      </div>
                    </div>
                  ))}
                  {selectedOptions.length > 3 && (
                    <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-text-secondary text-xs rounded-full">
                      +{selectedOptions.length - 3} more
                    </span>
                  )}
                </div>
              )}
            </div>

            {/* Error Message */}
            {error && (
              <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600 font-medium">
                  {error.message || 'Please select at least one option'}
                </p>
              </div>
            )}
          </div>
        );
      }}
    />
  );
};
