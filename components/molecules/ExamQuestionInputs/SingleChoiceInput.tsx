'use client';

import React, { useState, useEffect } from 'react';
import { UseFormRegister, UseFormWatch, UseFormSetValue, FieldError } from 'react-hook-form';
import { cn } from '@/utils/cn';
import { ExamFormData } from '@/components/organisms/ExamTaking/ExamTaking.schema';
import { Circle, CheckCircle2 } from 'lucide-react';
import { MathContentRenderer } from '@/components/molecules/MathContentRenderer';

interface SingleChoiceInputProps {
  questionIndex: number;
  content: string;
  options: string[];
  register: UseFormRegister<ExamFormData>;
  watch: UseFormWatch<ExamFormData>;
  setValue: UseFormSetValue<ExamFormData>;
  error?: FieldError;
  className?: string;
}

export const SingleChoiceInput: React.FC<SingleChoiceInputProps> = ({
  questionIndex,
  content,
  options,
  register,
  watch,
  setValue,
  error,
  className,
}) => {
  const [animatingOption, setAnimatingOption] = useState<string | null>(null);
  
  // Watch the current answer for this question
  const currentAnswer = watch(`answers.${questionIndex}.userAnswer.0`) || '';

  // Ensure currentAnswer is always a string to prevent controlled/uncontrolled input warning
  const selectedOption = typeof currentAnswer === 'string' ? currentAnswer : '';

  const handleOptionClick = (option: string) => {
    // Update form state using setValue with complete answer structure
    const formValue = {
      questionIndex,
      userAnswer: [option]
    };

    setValue(`answers.${questionIndex}`, formValue, { shouldDirty: true });

    // Add animation state
    setAnimatingOption(option);

    // Remove animation state after animation completes
    setTimeout(() => {
      setAnimatingOption(null);
    }, 300);
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Instruction */}
      <div className="mb-6">
        <p className="text-sm text-text-secondary font-medium mb-2">
          Select one option:
        </p>
        <div className="flex items-center space-x-2">
          <div className={cn(
            'w-3 h-3 rounded-full transition-colors duration-200',
            selectedOption ? 'bg-green-500' : 'bg-gray-300'
          )} />
          <span className="text-xs text-text-secondary">
            {selectedOption ? 'Option selected' : 'No option selected'}
          </span>
        </div>
      </div>
      
      {/* Options */}
      <div className="space-y-3">
        {options.map((option, optionIndex) => {
          const isSelected = selectedOption === option;
          const isAnimating = animatingOption === option;
          
          return (
            <label
              key={optionIndex}
              onClick={() => handleOptionClick(option)}
              className={cn(
                'group relative flex items-start space-x-4 p-4 rounded-xl border-2 cursor-pointer',
                'transition-all duration-200 ease-out transform',
                'hover:scale-[1.01] hover:shadow-md active:scale-[0.99]',
                // Base states
                !isSelected && !error && 'border-gray-200 bg-background-default hover:border-blue-300 hover:bg-accent-bg-light',
                // Selected state
                isSelected && 'border-primary-action bg-accent-bg-light shadow-sm ring-2 ring-primary-action ring-opacity-20',
                // Error state
                error && 'border-red-300 bg-red-50',
                // Animation state
                isAnimating && 'animate-pulse',
                // Focus state
                'focus-within:ring-4 focus-within:ring-primary-action focus-within:ring-opacity-20'
              )}
            >
              {/* Custom Radio Button */}
              <div className="relative flex-shrink-0 mt-0.5">
                <input
                  type="radio"
                  name={`question-${questionIndex}`}
                  value={option}
                  checked={selectedOption === option}
                  onChange={() => { /* onChange is handled by the label's onClick */ }}
                  className="sr-only"
                />
                <div className={cn(
                  'w-5 h-5 rounded-full border-2 flex items-center justify-center transition-all duration-200',
                  isSelected 
                    ? 'border-primary-action bg-primary-action shadow-sm' 
                    : 'border-gray-300 bg-background-default group-hover:border-primary-action'
                )}>
                  {isSelected ? (
                    <CheckCircle2 
                      className="w-3 h-3 text-white transition-all duration-200 scale-100" 
                    />
                  ) : (
                    <Circle 
                      className={cn(
                        'w-3 h-3 transition-all duration-200',
                        'text-gray-400 group-hover:text-primary-action opacity-60'
                      )}
                    />
                  )}
                </div>
              </div>
              
              {/* Option Text with MathML Support */}
              <div className={cn(
                'flex-1 leading-relaxed transition-all duration-200',
                isSelected ? 'text-text-primary font-semibold' : 'text-text-primary',
                'group-hover:text-text-primary'
              )}>
                <MathContentRenderer
                  content={option}
                  fallbackToText={true}
                  onRenderError={(error) => {
                    console.warn('Math rendering error in option:', optionIndex, error);
                  }}
                />
              </div>
              
              {/* Selection Indicator */}
              {isSelected && (
                <div className="flex-shrink-0 flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span className="text-xs text-green-600 font-medium">Selected</span>
                </div>
              )}
              
              {/* Hover Indicator */}
              {!isSelected && (
                <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <div className="w-2 h-2 bg-primary-action rounded-full" />
                </div>
              )}
            </label>
          );
        })}
      </div>
      
      {/* Selection Summary */}
      {selectedOption && (
        <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
          <div className="flex items-start space-x-3">
            <CheckCircle2 className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <p className="text-sm font-medium text-green-800 mb-1">
                Your selection:
              </p>
              <p className="text-sm text-green-700 leading-relaxed">
                &quot;{selectedOption.length > 100 ? `${selectedOption.substring(0, 100)}...` : selectedOption}&quot;
              </p>
            </div>
          </div>
        </div>
      )}
      
      {/* Error Message */}
      {error && (
        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <Circle className="w-4 h-4 text-red-500" />
            <p className="text-sm text-red-600 font-medium">
              {error.message || 'Please select an option'}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};
