'use client';

import React from 'react';
import { Check, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/utils/cn';
import { useViewport } from '@/utils/responsive';

export interface QuestionProgress {
  questionIndex: number;
  isAnswered: boolean;
  isComplete: boolean;
}

export interface CompactQuestionNavProps {
  currentQuestion: number;
  totalQuestions: number;
  questionProgress: QuestionProgress[];
  onQuestionSelect: (questionIndex: number) => void;
  className?: string;
}

export const CompactQuestionNav: React.FC<CompactQuestionNavProps> = ({
  currentQuestion,
  totalQuestions,
  questionProgress,
  onQuestionSelect,
  className,
}) => {
  const { isMobile, isTablet } = useViewport();
  const answeredCount = questionProgress.filter(q => q.isAnswered).length;
  const completionPercentage = Math.round((answeredCount / totalQuestions) * 100);

  // Only render on mobile/tablet
  if (!isMobile && !isTablet) {
    return null;
  }

  // Calculate visible questions (current + 2 before and after if possible)
  const visibleQuestions = [];
  const minVisible = Math.max(0, currentQuestion - 2);
  const maxVisible = Math.min(totalQuestions - 1, currentQuestion + 2);

  for (let i = minVisible; i <= maxVisible; i++) {
    visibleQuestions.push(i);
  }

  return (
    <div className={cn(
      'border-b border-slate-200/60 shadow-sm backdrop-blur-sm bg-white/95',
      className
    )}>
      {/* Professional Mobile Header */}
      <div className="px-4 py-3">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-gradient-to-r from-blue-600 to-blue-700 rounded-md flex items-center justify-center">
              <span className="text-white text-xs font-bold">{currentQuestion + 1}</span>
            </div>
            <div>
              <h3 className="text-sm font-semibold text-slate-900">Question Progress</h3>
              <p className="text-xs text-slate-500">
                {answeredCount} of {totalQuestions} completed
              </p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-lg font-bold text-slate-900">{completionPercentage}%</div>
            <div className="text-xs text-slate-500">Complete</div>
          </div>
        </div>

        {/* Professional Progress Bar */}
        <div className="relative mb-4">
          <div className="w-full bg-slate-200 rounded-full h-2 overflow-hidden">
            <div
              className="h-2 bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 rounded-full transition-all duration-700 ease-out"
              style={{ width: `${completionPercentage}%` }}
            />
          </div>
          <div
            className="absolute top-0 h-2 bg-gradient-to-r from-blue-400/50 to-blue-600/50 rounded-full blur-sm transition-all duration-700 ease-out"
            style={{ width: `${completionPercentage}%` }}
          />
        </div>

        {/* Professional Question Grid */}
        <div className="grid grid-cols-8 gap-2 mb-4">
          {Array.from({ length: Math.min(totalQuestions, 16) }, (_, index) => {
            // Show current question and surrounding questions for large sets
            let questionIndex = index;
            if (totalQuestions > 16) {
              const start = Math.max(0, currentQuestion - 8);
              questionIndex = start + index;
              if (questionIndex >= totalQuestions) return null;
            }

            const progress = questionProgress.find(p => p.questionIndex === questionIndex);
            const isAnswered = progress?.isAnswered || false;
            const isCurrent = questionIndex === currentQuestion;

            return (
              <button
                key={questionIndex}
                onClick={() => onQuestionSelect(questionIndex)}
                className={cn(
                  'relative h-8 w-8 rounded-lg text-xs font-semibold transition-all duration-200',
                  'flex items-center justify-center',
                  'focus:outline-none focus:ring-2 focus:ring-blue-500/20',
                  'border',
                  isCurrent && [
                    'bg-gradient-to-r from-blue-600 to-blue-700',
                    'text-white shadow-lg shadow-blue-500/25',
                    'border-blue-600 scale-110'
                  ],
                  !isCurrent && isAnswered && [
                    'bg-gradient-to-r from-emerald-500 to-emerald-600',
                    'text-white shadow-md shadow-emerald-500/20',
                    'border-emerald-500'
                  ],
                  !isCurrent && !isAnswered && [
                    'bg-white text-slate-700 border-slate-200',
                    'hover:bg-slate-50 hover:border-slate-300'
                  ]
                )}
                aria-label={`Go to question ${questionIndex + 1}${isAnswered ? ' (completed)' : ''}`}
              >
                {isAnswered && !isCurrent ? (
                  <Check className="w-3 h-3" strokeWidth={2.5} />
                ) : (
                  <span>{questionIndex + 1}</span>
                )}

                {isCurrent && (
                  <div className="absolute inset-0 rounded-lg bg-blue-600/20 animate-pulse" />
                )}
              </button>
            );
          })}
        </div>

        {/* Professional Navigation Controls */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => currentQuestion > 0 && onQuestionSelect(currentQuestion - 1)}
            disabled={currentQuestion === 0}
            className={cn(
              'flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200',
              'focus:outline-none focus:ring-2 focus:ring-blue-500/20',
              currentQuestion === 0
                ? 'text-slate-400 cursor-not-allowed bg-slate-50'
                : 'text-blue-600 hover:bg-blue-50 active:bg-blue-100'
            )}
            aria-label="Previous question"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>Previous</span>
          </button>

          <div className="flex items-center space-x-1 text-sm text-slate-600">
            <span className="font-medium">{currentQuestion + 1}</span>
            <span>of</span>
            <span className="font-medium">{totalQuestions}</span>
          </div>

          <button
            onClick={() => currentQuestion < totalQuestions - 1 && onQuestionSelect(currentQuestion + 1)}
            disabled={currentQuestion === totalQuestions - 1}
            className={cn(
              'flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200',
              'focus:outline-none focus:ring-2 focus:ring-blue-500/20',
              currentQuestion === totalQuestions - 1
                ? 'text-slate-400 cursor-not-allowed bg-slate-50'
                : 'text-blue-600 hover:bg-blue-50 active:bg-blue-100'
            )}
            aria-label="Next question"
          >
            <span>Next</span>
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};
