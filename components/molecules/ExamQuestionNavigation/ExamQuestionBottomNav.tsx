'use client';

import React, { useRef, useEffect } from 'react';
import { ChevronLeft, ChevronRight, List, Send, FileText, ArrowLeft } from 'lucide-react';
import { cn } from '@/utils/cn';
import { Button } from '@/components/atoms/Button/Button';
import { useViewport, responsiveClasses, touchUtils } from '@/utils/responsive';
import { useAccessibility } from '@/hooks/useAccessibility';

export interface ExamQuestionBottomNavProps {
  currentQuestion: number;
  totalQuestions: number;
  answeredQuestions: number;
  onPrevious: () => void;
  onNext: () => void;
  onShowOverview: () => void;
  onShowReview?: () => void;
  onSubmit?: () => void;
  onBackToExamList?: () => void;
  isSubmitting?: boolean;
  canSubmit?: boolean;
  className?: string;
}

export const ExamQuestionBottomNav: React.FC<ExamQuestionBottomNavProps> = ({
  currentQuestion,
  totalQuestions,
  answeredQuestions,
  onPrevious,
  onNext,
  onShowOverview,
  onShowReview,
  onSubmit,
  onBackToExamList,
  isSubmitting = false,
  canSubmit = false,
  className,
}) => {
  const { isMobile, isTablet, currentBreakpoint } = useViewport();
  const isFirstQuestion = currentQuestion === 0;
  const isLastQuestion = currentQuestion === totalQuestions - 1;
  const completionPercentage = Math.round((answeredQuestions / totalQuestions) * 100);
  
  // Accessibility hooks
  const {
    getNavigationAriaLabel,
    getProgressAriaLabel,
    focusManagement
  } = useAccessibility();
  
  const navRef = useRef<HTMLDivElement>(null);

  // Show on all devices for now (can be made responsive later if needed)
  // if (!isMobile && !isTablet) {
  //   return null;
  // }

  // Get touch-optimized button size
  const touchButtonSize = touchUtils.getTouchTargetSize(currentBreakpoint);

  return (
    <nav
      ref={navRef}
      className={cn(
        'fixed bottom-0 left-0 right-0 bg-background-default border-t border-gray-200',
        'z-50 shadow-lg backdrop-blur-sm bg-background-default/95',
        'animate-[slideInUp_0.5s_ease-out] stagger-3',
        // Safe area support for mobile devices
        'pb-safe-area-inset-bottom',
        // Performance optimizations
        'transform-gpu will-change-transform',
        className
      )}
      role="navigation"
      aria-label="Exam navigation"
    >
      {/* Progress Bar with accessibility */}
      <div 
        className="w-full bg-background-subtle h-1"
        role="progressbar"
        aria-valuenow={completionPercentage}
        aria-valuemin={0}
        aria-valuemax={100}
        aria-label={`Exam progress: ${completionPercentage}% complete, ${answeredQuestions} of ${totalQuestions} questions answered`}
      >
        <div
          className="bg-primary-action h-1 transition-all duration-500"
          style={{ width: `${completionPercentage}%` }}
        />
      </div>

      {/* Navigation Content */}
      <div className={cn(
        // Responsive padding
        'px-3 py-2 sm:px-4 sm:py-3',
        // Safe area support
        'pb-safe-area-inset-bottom'
      )}>
        <div className="flex items-center justify-between gap-2 sm:gap-3">
          {/* Left Section: Back and Previous buttons */}
          <div className="flex items-center gap-2 flex-shrink-0">
            {/* Back Button */}
            {onBackToExamList && (
              <Button
                variant="ghost"
                onClick={onBackToExamList}
                className={cn(
                  'flex items-center gap-1 flex-shrink-0 exam-button-focus',
                  // Touch-optimized sizing
                  'h-8 w-8 sm:h-10 sm:w-auto sm:px-3',
                  // Touch interactions
                  touchUtils.touchClasses.button,
                  // Responsive text
                  'text-xs sm:text-sm'
                )}
                aria-label="Back to exam list"
              >
                <ArrowLeft className="w-4 h-4" aria-hidden="true" />
                <span className="hidden sm:inline">Back</span>
              </Button>
            )}

            {/* Previous Button */}
            <Button
              variant="outline"
              onClick={onPrevious}
              disabled={isFirstQuestion}
              className={cn(
                'flex items-center gap-1 sm:gap-2 flex-shrink-0 exam-button-focus',
                // Touch-optimized sizing
                touchButtonSize,
                // Responsive text sizing
                'text-sm sm:text-base',
                // Touch interactions
                touchUtils.touchClasses.button,
                isFirstQuestion && 'opacity-50 cursor-not-allowed'
              )}
              aria-label={getNavigationAriaLabel('previous', currentQuestion + 1, totalQuestions)}
              aria-disabled={isFirstQuestion}
            >
              <ChevronLeft className="w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
              <span className="hidden sm:inline">Previous</span>
            </Button>
          </div>

          {/* Center Content */}
          <div className="flex items-center gap-1 sm:gap-2 flex-1 justify-center min-w-0">
            {/* Question Counter */}
            <div className="text-center min-w-0 flex-shrink-0">
              <div className={cn(
                'font-medium text-text-primary',
                responsiveClasses.text.navigation
              )}>
                {currentQuestion + 1} of {totalQuestions}
              </div>
              <div className={cn(
                'text-text-secondary',
                responsiveClasses.text.metadata
              )}>
                {answeredQuestions} answered
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-1 flex-shrink-0">
              {/* Overview Button */}
              <Button
                variant="ghost"
                onClick={onShowOverview}
                className={cn(
                  'flex items-center gap-1 flex-shrink-0 exam-button-focus',
                  // Touch-optimized sizing
                  'h-8 w-8 sm:h-10 sm:w-auto sm:px-3',
                  // Touch interactions
                  touchUtils.touchClasses.button,
                  // Responsive text
                  'text-xs sm:text-sm'
                )}
                aria-label={`Show question overview. View all ${totalQuestions} questions and their completion status.`}
                aria-describedby="overview-help"
              >
                <List className="w-4 h-4" aria-hidden="true" />
                <span className="hidden sm:inline">Overview</span>
                <span id="overview-help" className="sr-only">
                  Opens a modal showing all questions with their answered status
                </span>
              </Button>

              {/* Review Button */}
              {onShowReview && (
                <Button
                  variant="ghost"
                  onClick={onShowReview}
                  className={cn(
                    'flex items-center gap-1 flex-shrink-0 exam-button-focus',
                    // Touch-optimized sizing
                    'h-8 w-8 sm:h-10 sm:w-auto sm:px-3',
                    // Touch interactions
                    touchUtils.touchClasses.button,
                    // Responsive text
                    'text-xs sm:text-sm'
                  )}
                  aria-label={`Review all answers before submitting. ${answeredQuestions} of ${totalQuestions} questions answered.`}
                  aria-describedby="review-help"
                >
                  <FileText className="w-4 h-4" aria-hidden="true" />
                  <span className="hidden sm:inline">Review</span>
                  <span id="review-help" className="sr-only">
                    Opens a summary of all your answers for final review
                  </span>
                </Button>
              )}
            </div>
          </div>

          {/* Next/Submit Button */}
          {isLastQuestion && canSubmit ? (
            <Button
              variant="primary"
              onClick={onSubmit}
              disabled={isSubmitting}
              className={cn(
                'flex items-center gap-1 sm:gap-2 flex-shrink-0 exam-button-focus',
                // Touch-optimized sizing
                touchButtonSize,
                // Touch interactions
                touchUtils.touchClasses.button,
                // Responsive text
                'text-sm sm:text-base'
              )}
              aria-label={isSubmitting 
                ? "Submitting exam, please wait" 
                : `Submit exam. ${answeredQuestions} of ${totalQuestions} questions answered.`
              }
              aria-describedby={isSubmitting ? "submit-status" : "submit-help"}
            >
              {isSubmitting ? (
                <>
                  <div 
                    className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-current border-t-transparent rounded-full animate-spin" 
                    aria-hidden="true"
                  />
                  <span className="hidden sm:inline">Submitting...</span>
                  <span id="submit-status" className="sr-only">
                    Your exam is being submitted. Please do not close this window.
                  </span>
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
                  <span className="hidden sm:inline">Submit</span>
                  <span id="submit-help" className="sr-only">
                    Submit your exam for grading. You cannot change answers after submission.
                  </span>
                </>
              )}
            </Button>
          ) : (
            <Button
              variant="outline"
              onClick={onNext}
              disabled={isLastQuestion}
              className={cn(
                'flex items-center gap-1 sm:gap-2 flex-shrink-0 exam-button-focus',
                // Touch-optimized sizing
                touchButtonSize,
                // Touch interactions
                touchUtils.touchClasses.button,
                // Responsive text
                'text-sm sm:text-base',
                isLastQuestion && 'opacity-50 cursor-not-allowed'
              )}
              aria-label={getNavigationAriaLabel('next', currentQuestion + 1, totalQuestions)}
              aria-disabled={isLastQuestion}
            >
              <span className="hidden sm:inline">Next</span>
              <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
            </Button>
          )}
        </div>
      </div>
    </nav>
  );
};