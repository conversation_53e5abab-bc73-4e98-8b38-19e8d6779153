'use client';

import React from 'react';
import { X, CheckCircle, Circle, Clock, ChevronRight } from 'lucide-react';
import { cn } from '@/utils/cn';
import { Button } from '@/components/atoms/Button/Button';
import { useViewport, responsiveClasses, touchUtils } from '@/utils/responsive';

export interface QuestionProgress {
  questionIndex: number;
  isAnswered: boolean;
  isComplete: boolean;
  timeSpent?: number;
}

export interface ExamQuestionOverviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentQuestion: number;
  totalQuestions: number;
  questionProgress: QuestionProgress[];
  onQuestionSelect: (questionIndex: number) => void;
  className?: string;
}

const getQuestionStatusColor = (
  questionIndex: number,
  currentQuestion: number,
  isAnswered: boolean
): string => {
  if (questionIndex === currentQuestion) {
    return 'bg-primary-action text-background-default border-primary-action';
  }
  if (isAnswered) {
    return 'bg-green-100 text-green-800 border-green-300';
  }
  return 'bg-background-subtle text-text-secondary border-gray-300';
};

const getQuestionStatusIcon = (
  questionIndex: number,
  currentQuestion: number,
  isAnswered: boolean
) => {
  if (questionIndex === currentQuestion) {
    return <ChevronRight className="w-4 h-4" />;
  }
  if (isAnswered) {
    return <CheckCircle className="w-4 h-4" />;
  }
  return <Circle className="w-4 h-4" />;
};

export const ExamQuestionOverviewModal: React.FC<ExamQuestionOverviewModalProps> = ({
  isOpen,
  onClose,
  currentQuestion,
  totalQuestions,
  questionProgress,
  onQuestionSelect,
  className,
}) => {
  const { isMobile, isTablet, currentBreakpoint } = useViewport();
  const answeredCount = questionProgress.filter(q => q.isAnswered).length;
  const completionPercentage = Math.round((answeredCount / totalQuestions) * 100);

  const handleQuestionSelect = (questionIndex: number) => {
    onQuestionSelect(questionIndex);
    onClose();
  };

  // Don't render on desktop
  if (!isMobile && !isTablet) return null;
  if (!isOpen) return null;

  // Get responsive grid columns based on screen size
  const getGridCols = () => {
    if (isMobile) {
      return totalQuestions <= 6 ? 'grid-cols-2' : 'grid-cols-3';
    }
    return totalQuestions <= 9 ? 'grid-cols-3' : 'grid-cols-4';
  };

  return (
    <div className={cn('fixed inset-0 z-50', className)}>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 animate-[backdropFadeIn_0.3s_ease-out] backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal Content */}
      <div className={cn(
        'fixed bg-background-default flex flex-col animate-[modalSlideUp_0.4s_ease-out]',
        // Responsive positioning and sizing
        isMobile 
          ? 'inset-x-0 bottom-0 rounded-t-xl max-h-[85vh]'
          : 'inset-x-4 bottom-4 top-4 rounded-xl max-h-none',
        // Performance optimizations
        'transform-gpu will-change-transform',
        // Safe area support
        'pb-safe-area-inset-bottom'
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div>
            <h2 className="text-lg font-semibold text-text-primary">
              Question Overview
            </h2>
            <p className="text-sm text-text-secondary">
              {answeredCount} of {totalQuestions} answered ({completionPercentage}%)
            </p>
          </div>
          <Button
            variant="ghost"
            onClick={onClose}
            className="p-2"
            aria-label="Close overview"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Progress Bar */}
        <div className="px-4 py-3 border-b border-gray-200">
          <div className="w-full bg-background-subtle rounded-full h-2">
            <div
              className="bg-primary-action h-2 rounded-full transition-all duration-500"
              style={{ width: `${completionPercentage}%` }}
            />
          </div>
        </div>

        {/* Question Grid */}
        <div className={cn(
          'flex-1 overflow-y-auto',
          // Responsive padding
          'p-3 sm:p-4',
          // Touch scrolling optimization
          'overscroll-contain'
        )}>
          <div className={cn(
            'grid gap-3 sm:gap-4',
            // Responsive grid columns
            getGridCols()
          )}>
            {Array.from({ length: totalQuestions }, (_, index) => {
              const progress = questionProgress.find(p => p.questionIndex === index);
              const isAnswered = progress?.isAnswered || false;
              const timeSpent = progress?.timeSpent;

              return (
                <button
                  key={index}
                  onClick={() => handleQuestionSelect(index)}
                  className={cn(
                    'rounded-lg border transition-all duration-200',
                    'flex flex-col items-center gap-2 text-center',
                    'animate-[staggerFadeIn_0.3s_ease-out] transform',
                    'focus:outline-none focus:ring-2 focus:ring-primary-action focus:ring-opacity-20',
                    // Responsive padding and sizing - larger for mobile
                    'p-4 sm:p-4',
                    // Touch-optimized sizing - minimum 48px
                    'min-h-[48px] min-w-[48px]',
                    // Touch interactions with better feedback
                    'active:scale-95 transition-transform duration-100 ease-out',
                    // Hover effects (only on non-touch devices)
                    'hover:shadow-md hover:scale-105',
                    // Better visual feedback for touch
                    'shadow-sm hover:shadow-md',
                    getQuestionStatusColor(index, currentQuestion, isAnswered)
                  )}
                  style={{ animationDelay: `${(index * 30) + 200}ms` }}
                  aria-label={`Go to question ${index + 1}${isAnswered ? ' (answered)' : ''}`}
                >
                  {/* Question Number */}
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-current bg-opacity-10">
                    <span className="text-sm font-semibold">
                      {index + 1}
                    </span>
                  </div>

                  {/* Status Icon */}
                  <div className="flex items-center justify-center">
                    {getQuestionStatusIcon(index, currentQuestion, isAnswered)}
                  </div>

                  {/* Status Text */}
                  <div className="text-xs">
                    {index === currentQuestion ? (
                      <span className="font-medium">Current</span>
                    ) : isAnswered ? (
                      <span className="font-medium">Answered</span>
                    ) : (
                      <span>Not answered</span>
                    )}
                  </div>

                  {/* Time Spent */}
                  {timeSpent && (
                    <div className="flex items-center gap-1 text-xs opacity-75">
                      <Clock className="w-3 h-3" />
                      <span>
                        {Math.floor(timeSpent / 60)}:{(timeSpent % 60).toString().padStart(2, '0')}
                      </span>
                    </div>
                  )}
                </button>
              );
            })}
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 bg-section-bg-accent">
          <div className="flex items-center justify-between text-sm">
            <div className="text-text-secondary">
              Progress: {completionPercentage}%
            </div>
            <div className="text-text-secondary">
              {totalQuestions - answeredCount} remaining
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};