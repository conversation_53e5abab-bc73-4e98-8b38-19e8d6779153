'use client';

import React, { useState } from 'react';
import { Check, ChevronLeft, Clock, Target } from 'lucide-react';
import { cn } from '@/utils/cn';
import { useViewport } from '@/utils/responsive';
import { Button } from '@/components/atoms';
import { ExamTimer } from '@/components/molecules/ExamTimer';

export interface QuestionProgress {
  questionIndex: number;
  isAnswered: boolean;
  isComplete: boolean;
  timeSpent?: number;
}

export interface ExamQuestionSidebarProps {
  currentQuestion: number;
  totalQuestions: number;
  questionProgress: QuestionProgress[];
  onQuestionSelect: (questionIndex: number) => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
  className?: string;
  onPrevious?: () => void;
  onNext?: () => void;
  onSubmit?: () => void;
  // Timer props
  timeLimit?: number;
  examId?: string;
  onTimeExpired?: () => void;
}

const QuestionButton = ({
  index,
  isCurrent,
  isAnswered,
  onClick,
  isCollapsed
}: {
  index: number;
  isCurrent: boolean;
  isAnswered: boolean;
  onClick: () => void;
  isCollapsed: boolean;
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <button
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={cn(
        'relative group transition-all duration-200 ease-out',
        'focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:ring-offset-1',
        isCollapsed ? 'w-10 h-10' : 'w-full h-10',
        'rounded-lg border',
        // Current question styling
        isCurrent && [
          'bg-gradient-to-r from-blue-600 to-blue-700',
          'text-white shadow-lg shadow-blue-500/25',
          'border-blue-600 ring-2 ring-blue-500/20'
        ],
        // Answered question styling
        !isCurrent && isAnswered && [
          'bg-gradient-to-r from-emerald-500 to-emerald-600',
          'text-white shadow-md shadow-emerald-500/20',
          'border-emerald-500 hover:shadow-lg hover:shadow-emerald-500/30'
        ],
        // Unanswered question styling
        !isCurrent && !isAnswered && [
          'bg-white text-slate-700 border-slate-200',
          'hover:bg-slate-50 hover:border-slate-300 hover:shadow-sm'
        ]
      )}
      aria-label={`Question ${index + 1}${isAnswered ? ' (completed)' : ''}${isCurrent ? ' (current)' : ''}`}
    >
      {/* Background glow effect for current question */}
      {isCurrent && (
        <div className="absolute inset-0 rounded-lg bg-blue-600/20 animate-pulse" />
      )}

      {/* Question number or check icon */}
      <div className="relative z-10 flex items-center justify-center h-full">
        {isAnswered && !isCurrent ? (
          <Check className="w-4 h-4" strokeWidth={2.5} />
        ) : (
          <span className={cn(
            'font-semibold text-sm',
            isCurrent && 'text-white',
            !isCurrent && isAnswered && 'text-white',
            !isCurrent && !isAnswered && 'text-slate-700'
          )}>
            {index + 1}
          </span>
        )}
      </div>

      {/* Hover tooltip for collapsed state */}
      {isCollapsed && isHovered && (
        <div className="absolute left-full ml-2 px-2 py-1 bg-slate-900 text-white text-xs rounded-md whitespace-nowrap z-50 shadow-lg">
          Question {index + 1}
          {isAnswered && ' ✓'}
          <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-slate-900 rotate-45" />
        </div>
      )}
    </button>
  );
};

export const ExamQuestionSidebar: React.FC<ExamQuestionSidebarProps> = ({
  currentQuestion,
  totalQuestions,
  questionProgress,
  onQuestionSelect,
  isCollapsed = false,
  onToggleCollapse,
  className,
  onPrevious,
  onNext,
  onSubmit,
  timeLimit,
  examId,
  onTimeExpired
}) => {
  const { isDesktop } = useViewport();
  const answeredCount = questionProgress.filter(q => q.isAnswered).length;
  const completionPercentage = Math.round((answeredCount / totalQuestions) * 100);

  // Don't render on mobile/tablet
  if (!isDesktop) {
    return null;
  }

  return (
    <div
      className={cn(
        'bg-white border-r border-slate-200/60 shadow-sm',
        // Professional width with smooth transitions
        isCollapsed ? 'w-16' : 'w-72',
        'hidden lg:flex flex-col transition-all duration-300 ease-in-out',
        'backdrop-blur-sm bg-white/95',
        className
      )}
    >
      {/* Professional Header */}
      <div className="px-4 py-5 border-b border-slate-200/60 bg-gradient-to-r from-slate-50/50 to-white">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                <Target className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="text-sm font-semibold text-slate-900">Questions</h3>
                <p className="text-xs text-slate-500">
                  {answeredCount} of {totalQuestions} completed
                </p>
              </div>
            </div>
          )}

          {onToggleCollapse && (
            <button
              onClick={onToggleCollapse}
              className={cn(
                'p-2 rounded-lg transition-all duration-200',
                'hover:bg-slate-100 text-slate-400 hover:text-slate-600',
                'focus:outline-none focus:ring-2 focus:ring-blue-500/20',
                isCollapsed && 'mx-auto'
              )}
              aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            >
              <ChevronLeft
                className={cn(
                  'w-4 h-4 transition-transform duration-300',
                  isCollapsed ? 'rotate-180' : 'rotate-0'
                )}
              />
            </button>
          )}
        </div>

        {/* Professional Progress Section */}
        {!isCollapsed && (
          <div className="mt-4 space-y-2">
            <div className="flex items-center justify-between text-xs">
              <span className="text-slate-600 font-medium">Progress</span>
              <span className="text-slate-900 font-semibold">{completionPercentage}%</span>
            </div>

            <div className="relative">
              <div className="w-full bg-slate-200 rounded-full h-2 overflow-hidden">
                <div
                  className="h-2 bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 rounded-full transition-all duration-700 ease-out shadow-sm"
                  style={{ width: `${completionPercentage}%` }}
                />
              </div>
              {/* Progress glow effect */}
              <div
                className="absolute top-0 h-2 bg-gradient-to-r from-blue-400/50 to-blue-600/50 rounded-full blur-sm transition-all duration-700 ease-out"
                style={{ width: `${completionPercentage}%` }}
              />
            </div>

            <div className="flex items-center justify-between text-xs text-slate-500">
              <span>{answeredCount} answered</span>
              <span>{totalQuestions - answeredCount} remaining</span>
            </div>
          </div>
        )}

        {/* Timer Section */}
        {!isCollapsed && timeLimit && (
          <div className="mt-4">
            <ExamTimer
              timeLimit={timeLimit}
              examId={examId}
              onTimeExpired={onTimeExpired}
              className="mx-0"
            />
          </div>
        )}
      </div>

      {/* Professional Question Grid */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className={cn(
          'grid gap-2',
          isCollapsed ? 'grid-cols-1' : 'grid-cols-4'
        )}>
          {Array.from({ length: totalQuestions }, (_, index) => {
            const progress = questionProgress.find(p => p.questionIndex === index);
            const isAnswered = progress?.isAnswered || false;
            const isCurrent = index === currentQuestion;

            return (
              <QuestionButton
                key={index}
                index={index}
                isCurrent={isCurrent}
                isAnswered={isAnswered}
                onClick={() => onQuestionSelect(index)}
                isCollapsed={isCollapsed}
              />
            );
          })}
        </div>
      </div>

      {/* Navigation Bar (Previous/Next/Counter) */}
      {!isCollapsed && (
        <div className="px-4 py-3 border-t border-slate-200/60 bg-gradient-to-r from-slate-50/30 to-white flex items-center justify-between">
          <Button
            type="button"
            onClick={onPrevious}
            disabled={currentQuestion === 0}
            variant="outline"
            iconProps={{
              variant: 'arrow-left',
              className: 'w-4'
            }}
            aria-label="Previous question"
          >
            Previous
          </Button>
          {currentQuestion !== totalQuestions - 1 && (
            <Button
              type="button"
              onClick={onNext}
              variant="outline"
              className="flex flex-row-reverse"
              iconProps={{
                variant: 'arrow-right',
                className: 'w-4 !m-0'
              }}
              aria-label="Next question"
            >
              Next
            </Button>
          )}
          {currentQuestion === totalQuestions - 1 && (
            <Button
              type="button"
              onClick={onSubmit}
              variant="primary"
              className="ml-auto"
              aria-label="Submit exam"
            >
              Submit
            </Button>
          )}
        </div>
      )}

      {/* Professional Footer */}
      {!isCollapsed && (
        <div className="px-4 py-3 border-t border-slate-200/60 bg-gradient-to-r from-slate-50/30 to-white">
          <div className="flex items-center justify-center space-x-1 text-xs text-slate-500">
            <Clock className="w-3 h-3" />
            <span>Question {currentQuestion + 1} of {totalQuestions}</span>
          </div>
        </div>
      )}
    </div>
  );
};