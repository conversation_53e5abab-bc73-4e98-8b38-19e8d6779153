'use client';

import React from 'react';
import { Check, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/utils/cn';

export interface QuestionProgress {
  questionIndex: number;
  isAnswered: boolean;
  isComplete: boolean;
}

export interface ModernQuestionNavProps {
  currentQuestion: number;
  totalQuestions: number;
  questionProgress: QuestionProgress[];
  onQuestionSelect: (questionIndex: number) => void;
  className?: string;
}

export const ModernQuestionNav: React.FC<ModernQuestionNavProps> = ({
  currentQuestion,
  totalQuestions,
  questionProgress,
  onQuestionSelect,
  className,
}) => {
  const answeredCount = questionProgress.filter(q => q.isAnswered).length;
  const completionPercentage = Math.round((answeredCount / totalQuestions) * 100);

  const getQuestionStatus = (index: number) => {
    const progress = questionProgress.find(p => p.questionIndex === index);
    const isAnswered = progress?.isAnswered || false;
    const isCurrent = index === currentQuestion;
    
    if (isCurrent) {
      return {
        className: 'bg-blue-600 text-white shadow-lg border-blue-600',
        icon: null
      };
    }
    
    if (isAnswered) {
      return {
        className: 'bg-emerald-500 text-white border-emerald-500',
        icon: <Check className="w-3 h-3" />
      };
    }
    
    return {
      className: 'bg-white text-gray-700 border-gray-200 hover:bg-gray-50 hover:border-gray-300',
      icon: null
    };
  };

  return (
    <div className={cn('bg-white rounded-lg border border-gray-200 shadow-sm p-4', className)}>
      {/* Header with Progress */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-semibold text-gray-900">Questions</h3>
          <span className="text-xs font-medium text-gray-500">
            {answeredCount}/{totalQuestions}
          </span>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-100 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${completionPercentage}%` }}
          />
        </div>
        
        <div className="text-xs text-gray-500 mt-1 text-center">
          {completionPercentage}% Complete
        </div>
      </div>

      {/* Question Grid */}
      <div className="grid grid-cols-5 gap-2 mb-4">
        {Array.from({ length: totalQuestions }, (_, index) => {
          const status = getQuestionStatus(index);
          const isCurrent = index === currentQuestion;
          
          return (
            <button
              key={index}
              onClick={() => onQuestionSelect(index)}
              className={cn(
                'relative h-10 w-10 rounded-lg text-xs font-semibold transition-all duration-200',
                'flex items-center justify-center',
                'focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50',
                'hover:scale-105 active:scale-95',
                'border',
                status.className
              )}
              aria-label={`Go to question ${index + 1}${status.icon ? ' (answered)' : ''}`}
              title={`Question ${index + 1}${status.icon ? ' (answered)' : ''}`}
            >
              {status.icon ? (
                <div className="flex items-center justify-center">
                  {status.icon}
                </div>
              ) : (
                <span>{index + 1}</span>
              )}
              
              {/* Current question pulse effect */}
              {isCurrent && (
                <div className="absolute inset-0 rounded-lg bg-blue-600 opacity-20 animate-pulse" />
              )}
            </button>
          );
        })}
      </div>

      {/* Navigation Controls */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => currentQuestion > 0 && onQuestionSelect(currentQuestion - 1)}
          disabled={currentQuestion === 0}
          className={cn(
            'flex items-center gap-1 px-3 py-2 rounded-md text-xs font-medium transition-colors',
            currentQuestion === 0 
              ? 'text-gray-400 cursor-not-allowed' 
              : 'text-blue-600 hover:bg-blue-50'
          )}
          aria-label="Previous question"
        >
          <ChevronLeft className="w-3 h-3" />
          Previous
        </button>

        <div className="text-xs text-gray-500">
          {currentQuestion + 1} of {totalQuestions}
        </div>

        <button
          onClick={() => currentQuestion < totalQuestions - 1 && onQuestionSelect(currentQuestion + 1)}
          disabled={currentQuestion === totalQuestions - 1}
          className={cn(
            'flex items-center gap-1 px-3 py-2 rounded-md text-xs font-medium transition-colors',
            currentQuestion === totalQuestions - 1 
              ? 'text-gray-400 cursor-not-allowed' 
              : 'text-blue-600 hover:bg-blue-50'
          )}
          aria-label="Next question"
        >
          Next
          <ChevronRight className="w-3 h-3" />
        </button>
      </div>
    </div>
  );
};

export default ModernQuestionNav;
