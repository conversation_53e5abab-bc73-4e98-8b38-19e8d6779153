'use client';

import React, { useMemo, useState } from 'react';
import {
  ColumnDef,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import { IExamResult } from '@/apis/userApi';
import CustomTable from '@/components/molecules/CustomTable/CustomTable';

import { CheckCircle, XCircle, Calendar, Target, BookOpen, TrendingUp } from 'lucide-react';
import { cn } from '@/utils/cn';

export interface ExamResultsTableProps {
  results: IExamResult[];
}

// Mobile Card Component
const ExamResultCard: React.FC<{ result: IExamResult }> = ({ result }) => {
  const isPassed = result.status === 'passed';
  
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1 min-w-0">
          <h3 className="text-base font-semibold text-text-primary truncate mb-1">
            {result.examTitle}
          </h3>
          <div className="flex items-center gap-1 text-sm text-text-secondary">
            <BookOpen className="w-4 h-4" />
            <span>{result.subject}</span>
          </div>
        </div>
        
        {/* Status Badge */}
        <div className={cn(
          'inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-semibold flex-shrink-0',
          isPassed 
            ? 'bg-green-50 text-green-700 border border-green-200' 
            : 'bg-red-50 text-red-700 border border-red-200'
        )}>
          {isPassed ? (
            <CheckCircle className="w-3.5 h-3.5" />
          ) : (
            <XCircle className="w-3.5 h-3.5" />
          )}
          <span>{isPassed ? 'Passed' : 'Failed'}</span>
        </div>
      </div>

      {/* Score Section */}
      <div className="bg-gray-50 rounded-lg p-3 mb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={cn(
              'w-8 h-8 rounded-full flex items-center justify-center',
              isPassed ? 'bg-green-100' : 'bg-red-100'
            )}>
              <Target className={cn(
                'w-4 h-4',
                isPassed ? 'text-green-600' : 'text-red-600'
              )} />
            </div>
            <div>
              <div className="text-lg font-bold text-text-primary">
                {result.score}/{result.total}
              </div>
              <div className="text-xs text-text-secondary">Score</div>
            </div>
          </div>
          
          <div className="text-right">
            <div className={cn(
              'text-lg font-bold',
              isPassed ? 'text-green-600' : 'text-red-600'
            )}>
              {result.percentage.toFixed(1)}%
            </div>
            <div className="text-xs text-text-secondary">Percentage</div>
          </div>
        </div>
      </div>

      {/* Date */}
      <div className="flex items-center gap-2 text-sm text-text-secondary">
        <Calendar className="w-4 h-4" />
        <span>Submitted: {formatDate(result.submittedAt)}</span>
      </div>
    </div>
  );
};

export const ExamResultsTable: React.FC<ExamResultsTableProps> = ({
  results,
}) => {
  const [sorting, setSorting] = useState<SortingState>([]);

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const columns = useMemo<ColumnDef<IExamResult, any>[]>(() => [
    {
      accessorKey: 'examTitle',
      header: 'Exam Title',
      enableSorting: true,
    },
    {
      accessorKey: 'subject',
      header: 'Subject',
      enableSorting: true,
    },
    {
      accessorKey: 'score',
      header: 'Score',
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <Target className="w-4 h-4 text-gray-500" />
          <span className="font-medium">
            {row.original.score}/{row.original.total}
          </span>
          <span className="text-sm text-gray-500">
            ({row.original.percentage.toFixed(1)}%)
          </span>
        </div>
      ),
      enableSorting: true,
      sortingFn: (rowA, rowB) => {
        return rowA.original.percentage - rowB.original.percentage;
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.original.status;
        const isPassed = status === 'passed';
        
        return (
          <div className={cn(
            'inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-sm font-semibold',
            isPassed 
              ? 'bg-green-50 text-green-700 border border-green-200' 
              : 'bg-red-50 text-red-700 border border-red-200'
          )}>
            {isPassed ? (
              <CheckCircle className="w-4 h-4" />
            ) : (
              <XCircle className="w-4 h-4" />
            )}
            <span>{isPassed ? 'Passed' : 'Failed'}</span>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: 'submittedAt',
      header: 'Submitted At',
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <Calendar className="w-4 h-4 text-gray-500" />
          <span className="text-sm">
            {formatDate(row.original.submittedAt)}
          </span>
        </div>
      ),
      enableSorting: true,
      sortingFn: (rowA, rowB) => {
        return new Date(rowA.original.submittedAt).getTime() - new Date(rowB.original.submittedAt).getTime();
      },
    },
  ], []);

  const table = useReactTable({
    data: results,
    columns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  if (results.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        No exam results found for this student.
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Mobile Card Layout - Show on small screens */}
      <div className="block sm:hidden space-y-3">
        {results
          .sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime())
          .map((result, index) => (
            <ExamResultCard key={index} result={result} />
          ))}
      </div>

      {/* Desktop Table Layout - Show on larger screens */}
      <div className="hidden sm:block">
        <CustomTable
          columns={columns}
          tableData={table.getRowModel().rows.map((row) => row.original)}
          clientSidePagination={false}
          clientSideSorting
          sortingState={sorting}
          onSortingChange={setSorting}
        />
      </div>
    </div>
  );
}; 