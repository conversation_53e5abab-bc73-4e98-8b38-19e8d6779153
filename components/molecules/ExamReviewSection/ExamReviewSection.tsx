'use client';

import React, { useRef, useEffect } from 'react';
import { CheckCircle, AlertCircle, FileText, ChevronRight } from 'lucide-react';
import { cn } from '@/utils/cn';
import { IExamQuestion } from '@/types/exam.types';
import { useAccessibility } from '@/hooks/useAccessibility';

import { ExamProgressData } from '@/hooks/useExamProgress';

export interface ExamReviewSectionProps {
  questions: IExamQuestion[];
  answers: Array<{
    questionIndex: number;
    userAnswer: string[];
  }>;
  examTitle: string;
  timeSpent?: number;
  onQuestionSelect?: (questionIndex: number) => void;
  className?: string;
  // Add centralized progress data to eliminate duplication
  progressData?: ExamProgressData;
}

export const ExamReviewSection: React.FC<ExamReviewSectionProps> = ({
  questions,
  answers,
  examTitle,
  timeSpent,
  onQuestionSelect,
  className,
  progressData
}) => {
  const reviewSectionRef = useRef<HTMLDivElement>(null);

  // Accessibility hooks
  const { announce } = useAccessibility();

  // Use centralized progress data if available, otherwise fallback to local calculation
  const totalQuestions = progressData?.totalQuestions ?? questions.length;
  const answeredQuestions = progressData?.answeredQuestions ?? answers.filter(answer =>
    answer.userAnswer && answer.userAnswer.some(ans => ans.trim() !== '')
  ).length;
  const unansweredQuestions = progressData?.unansweredQuestions ?? (totalQuestions - answeredQuestions);

  // Announce review section when it opens
  useEffect(() => {
    if (reviewSectionRef.current) {
      announce(
        `Review section opened. ${examTitle}. ${answeredQuestions} of ${totalQuestions} questions answered. ${unansweredQuestions} questions unanswered.`,
        'polite'
      );
    }
  }, [announce, examTitle, answeredQuestions, totalQuestions, unansweredQuestions]);

  // Format time spent
  const formatTime = (seconds?: number) => {
    if (!seconds) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Get answer display text
  const getAnswerDisplay = (question: IExamQuestion, userAnswer: string[]) => {
    if (!userAnswer || userAnswer.length === 0 || userAnswer.every(ans => ans.trim() === '')) {
      return 'No answer provided';
    }

    switch (question.type) {
      case 'multiple_choice':
      case 'single_choice':
        return userAnswer.join(', ');
      case 'fill_blank':
        return userAnswer.join(' / ');
      case 'creative_writing':
        return userAnswer[0]?.substring(0, 100) + (userAnswer[0]?.length > 100 ? '...' : '');
      default:
        return userAnswer.join(', ');
    }
  };

  return (
    <section
      ref={reviewSectionRef}
      className={cn('bg-white rounded-xl shadow-sm border border-gray-200', className)}
      role="region"
      aria-labelledby="review-title"
      aria-describedby="review-description"
    >
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-blue-100 rounded-lg" aria-hidden="true">
            <FileText className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h2 id="review-title" className="text-xl font-semibold text-gray-900">
              Review Your Answers
            </h2>
            <p id="review-description" className="text-sm text-gray-600">
              Please review your responses before submitting
            </p>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="text-2xl font-bold text-gray-900">{totalQuestions}</div>
            <div className="text-sm text-gray-600">Total Questions</div>
          </div>
          <div className="bg-green-50 rounded-lg p-3">
            <div className="text-2xl font-bold text-green-600">{answeredQuestions}</div>
            <div className="text-sm text-gray-600">Answered</div>
          </div>
          <div className="bg-amber-50 rounded-lg p-3">
            <div className="text-2xl font-bold text-amber-600">{unansweredQuestions}</div>
            <div className="text-sm text-gray-600">Unanswered</div>
          </div>
          <div className="bg-blue-50 rounded-lg p-3">
            <div className="text-2xl font-bold text-blue-600">{formatTime(timeSpent)}</div>
            <div className="text-sm text-gray-600">Time Spent</div>
          </div>
        </div>
      </div>

      {/* Questions Review */}
      <div className="p-6">
        <div className="space-y-4">
          {questions.map((question, index) => {
            const userAnswer = answers.find(ans => ans.questionIndex === index)?.userAnswer || [];
            const isAnswered = userAnswer.some(ans => ans.trim() !== '');
            const answerDisplay = getAnswerDisplay(question, userAnswer);

            return (
              <div
                key={index}
                className={cn(
                  'border rounded-lg p-4 transition-all duration-200',
                  isAnswered
                    ? 'border-green-200 bg-green-50/50'
                    : 'border-amber-200 bg-amber-50/50',
                  onQuestionSelect && 'cursor-pointer hover:shadow-sm'
                )}
                onClick={() => onQuestionSelect?.(index)}
              >
                <div className="flex items-start gap-3">
                  {/* Status Icon */}
                  <div className="flex-shrink-0 mt-1">
                    {isAnswered ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <AlertCircle className="w-5 h-5 text-amber-600" />
                    )}
                  </div>

                  {/* Question Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-sm font-medium text-gray-900">
                        Question {index + 1}
                      </span>
                      <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                        {question.type.replace('_', ' ')}
                      </span>
                    </div>

                    <div className="text-sm text-gray-700 mb-3 line-clamp-2">
                      {question.content}
                    </div>

                    <div className="bg-white rounded-md p-3 border border-gray-200">
                      <div className="text-xs text-gray-500 mb-1">Your Answer:</div>
                      <div className={cn(
                        'text-sm',
                        isAnswered ? 'text-gray-900' : 'text-gray-500 italic'
                      )}>
                        {answerDisplay}
                      </div>
                    </div>
                  </div>

                  {/* Navigation Arrow */}
                  {onQuestionSelect && (
                    <div className="flex-shrink-0">
                      <ChevronRight className="w-4 h-4 text-gray-400" />
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Warning for unanswered questions */}
        {unansweredQuestions > 0 && (
          <div className="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-amber-600" />
              <div>
                <div className="text-sm font-medium text-amber-800">
                  {unansweredQuestions} question{unansweredQuestions > 1 ? 's' : ''} unanswered
                </div>
                <div className="text-sm text-amber-700">
                  You can still submit your exam, but consider reviewing unanswered questions.
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};