import React from 'react';
import { 
  Users, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  Award,
  AlertCircle
} from 'lucide-react';
import { IExamResultsResponse } from '@/types/exam.types';
import { cn } from '@/utils/cn';

interface ExamStatsOverviewProps {
  resultsData?: IExamResultsResponse | null;
  totalAssignments?: number;
  className?: string;
}

interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  variant?: 'default' | 'success' | 'warning' | 'info';
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  description,
  icon,
  trend,
  variant = 'default'
}) => {
  const variantStyles = {
    default: 'bg-white border-gray-200',
    success: 'bg-green-50 border-green-200',
    warning: 'bg-amber-50 border-amber-200',
    info: 'bg-blue-50 border-blue-200'
  };

  const iconStyles = {
    default: 'text-gray-600',
    success: 'text-green-600',
    warning: 'text-amber-600',
    info: 'text-blue-600'
  };

  return (
    <div className={cn(
      'p-6 rounded-xl border transition-all duration-200 hover:shadow-md',
      variantStyles[variant]
    )}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <div className={cn('p-2 rounded-lg bg-white/60', iconStyles[variant])}>
              {icon}
            </div>
            <h3 className="text-sm font-medium text-gray-600">{title}</h3>
          </div>
          <div className="space-y-1">
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {description && (
              <p className="text-sm text-gray-600">{description}</p>
            )}
          </div>
        </div>
        {trend && (
          <div className={cn(
            'flex items-center gap-1 text-xs font-medium px-2 py-1 rounded-full',
            trend.isPositive 
              ? 'text-green-700 bg-green-100' 
              : 'text-red-700 bg-red-100'
          )}>
            <TrendingUp className={cn(
              'w-3 h-3',
              !trend.isPositive && 'rotate-180'
            )} />
            {Math.abs(trend.value)}%
          </div>
        )}
      </div>
    </div>
  );
};

export const ExamStatsOverview: React.FC<ExamStatsOverviewProps> = ({
  resultsData,
  totalAssignments = 0,
  className
}) => {
  const stats = resultsData?.stats;
  const results = resultsData?.results || [];

  // Calculate completion rate
  const completionRate = totalAssignments > 0 
    ? Math.round((results.length / totalAssignments) * 100)
    : 0;

  // Calculate average score
  const averageScore = results.length > 0
    ? Math.round(results.reduce((sum, result) => sum + (result.score || 0), 0) / results.length)
    : 0;

  // Calculate pass rate (assuming 70% is passing)
  const passThreshold = 70;
  const passedCount = results.filter(result => (result.score || 0) >= passThreshold).length;
  const passRate = results.length > 0 
    ? Math.round((passedCount / results.length) * 100)
    : 0;

  // Calculate average time (if available)
  // Note: timeSpent might not be available in IExamResultDetail, this is for future enhancement
  const resultsWithTime = results.filter(result => (result as any).timeSpent);
  const averageTime = resultsWithTime.length > 0
    ? Math.round(resultsWithTime.reduce((sum, result) => sum + ((result as any).timeSpent || 0), 0) / resultsWithTime.length)
    : 0;

  const formatTime = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Main Statistics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total Assigned"
          value={totalAssignments}
          description="Students assigned to exam"
          icon={<Users className="w-5 h-5" />}
          variant="info"
        />
        
        <StatCard
          title="Completed"
          value={`${results.length}/${totalAssignments}`}
          description={`${completionRate}% completion rate`}
          icon={<CheckCircle className="w-5 h-5" />}
          variant={completionRate >= 80 ? 'success' : completionRate >= 50 ? 'warning' : 'default'}
        />
        
        <StatCard
          title="Average Score"
          value={`${averageScore}%`}
          description={`${passedCount} students passed`}
          icon={<Award className="w-5 h-5" />}
          variant={averageScore >= 80 ? 'success' : averageScore >= 60 ? 'warning' : 'default'}
        />
        
        <StatCard
          title="Average Time"
          value={averageTime > 0 ? formatTime(averageTime) : 'N/A'}
          description={resultsWithTime.length > 0 ? `${resultsWithTime.length} completed` : 'No time data'}
          icon={<Clock className="w-5 h-5" />}
          variant="default"
        />
      </div>

      {/* Performance Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-blue-600" />
            Performance Overview
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Pass Rate</span>
              <span className={cn(
                'font-semibold',
                passRate >= 80 ? 'text-green-600' : passRate >= 60 ? 'text-amber-600' : 'text-red-600'
              )}>
                {passRate}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={cn(
                  'h-2 rounded-full transition-all duration-500',
                  passRate >= 80 ? 'bg-green-500' : passRate >= 60 ? 'bg-amber-500' : 'bg-red-500'
                )}
                style={{ width: `${passRate}%` }}
              />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-amber-600" />
            Quick Insights
          </h3>
          <div className="space-y-2">
            <div className="text-sm">
              <span className="text-gray-600">Completion: </span>
              <span className={cn(
                'font-medium',
                completionRate >= 80 ? 'text-green-600' : 
                completionRate >= 50 ? 'text-amber-600' : 'text-red-600'
              )}>
                {completionRate >= 80 ? 'Excellent' : 
                 completionRate >= 50 ? 'Good' : 'Needs Attention'}
              </span>
            </div>
            <div className="text-sm">
              <span className="text-gray-600">Performance: </span>
              <span className={cn(
                'font-medium',
                averageScore >= 80 ? 'text-green-600' : 
                averageScore >= 60 ? 'text-amber-600' : 'text-red-600'
              )}>
                {averageScore >= 80 ? 'Above Average' : 
                 averageScore >= 60 ? 'Average' : 'Below Average'}
              </span>
            </div>
            {totalAssignments > results.length && (
              <div className="text-sm">
                <span className="text-gray-600">Pending: </span>
                <span className="font-medium text-blue-600">
                  {totalAssignments - results.length} students still to complete
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}; 