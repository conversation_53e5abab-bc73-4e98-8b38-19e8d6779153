'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/atoms/Dialog/Dialog';
import { CheckCircle, AlertTriangle, Clock, Send, X, RefreshCw, ArrowRight } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';
import { useAccessibility, useFocusRestore } from '@/hooks/useAccessibility';

export interface ExamSubmissionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  onRetry?: () => void;
  examTitle: string;
  totalQuestions: number;
  answeredQuestions: number;
  timeSpent?: number;
  isSubmitting?: boolean;
  submitError?: string | null;
  submitSuccess?: boolean;
  className?: string;
}

export const ExamSubmissionDialog: React.FC<ExamSubmissionDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  onRetry,
  examTitle,
  totalQuestions,
  answeredQuestions,
  timeSpent,
  isSubmitting = false,
  submitError = null,
  submitSuccess = false,
  className
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const unansweredQuestions = totalQuestions - answeredQuestions;
  const completionPercentage = Math.round((answeredQuestions / totalQuestions) * 100);
  
  // Accessibility hooks
  const { announce, focusManagement } = useAccessibility();
  const { saveFocus, restoreFocus } = useFocusRestore();
  const dialogRef = useRef<HTMLDivElement>(null);

  // Format time spent
  const formatTime = (seconds?: number) => {
    if (!seconds) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Determine dialog state
  const getDialogState = () => {
    if (submitSuccess) return 'success';
    if (submitError) return 'error';
    if (isSubmitting) return 'submitting';
    return 'confirm';
  };

  const dialogState = getDialogState();

  // Focus management and announcements
  useEffect(() => {
    if (isOpen) {
      saveFocus();
      
      // Announce dialog state to screen readers
      const stateMessages = {
        success: `Exam submitted successfully! ${answeredQuestions} of ${totalQuestions} questions answered.`,
        error: `Submission failed: ${submitError || 'An error occurred'}. Your answers are preserved.`,
        submitting: 'Submitting your exam. Please wait and do not close this window.',
        confirm: `Ready to submit exam: ${examTitle}. ${answeredQuestions} of ${totalQuestions} questions answered.`
      };
      
      announce(stateMessages[dialogState], dialogState === 'error' ? 'assertive' : 'polite');
    } else {
      restoreFocus();
    }
  }, [isOpen, dialogState, saveFocus, restoreFocus, announce, examTitle, answeredQuestions, totalQuestions, submitError]);

  // Handle keyboard navigation within dialog
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!isOpen || !dialogRef.current) return;

    if (event.key === 'Escape' && !isSubmitting) {
      event.preventDefault();
      onClose();
    } else if (event.key === 'Tab') {
      focusManagement.trapFocus(dialogRef.current, event);
    }
  }, [isOpen, isSubmitting, onClose, focusManagement]);

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, handleKeyDown]);

  // Render content based on state
  const renderContent = () => {
    switch (dialogState) {
      case 'success':
        return (
          <>
            <DialogHeader className="text-center">
              <div className="mx-auto mb-4 p-3 bg-green-100 rounded-full w-fit">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <DialogTitle className="text-xl text-green-800">
                Exam Submitted Successfully!
              </DialogTitle>
              <p className="text-sm text-gray-600 mt-2">
                Your answers have been saved and your exam has been submitted for grading.
              </p>
            </DialogHeader>

            <div className="px-6 pb-4">
              <div className="bg-green-50 rounded-lg p-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Exam:</span>
                  <span className="font-medium text-gray-900">{examTitle}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Questions Answered:</span>
                  <span className="font-medium text-gray-900">{answeredQuestions} of {totalQuestions}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Time Spent:</span>
                  <span className="font-medium text-gray-900">{formatTime(timeSpent)}</span>
                </div>
              </div>

              <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center gap-2 text-blue-800">
                  <ArrowRight className="w-4 h-4" />
                  <span className="text-sm font-medium">What&apos;s Next?</span>
                </div>
                <p className="text-sm text-blue-700 mt-1">
                  Your exam will be graded automatically. You can view your results in the exam results section.
                </p>
              </div>
            </div>

            <DialogFooter>
              <Button
                onClick={onClose}
                variant="primary"
                className="w-full"
              >
                Continue to Dashboard
              </Button>
            </DialogFooter>
          </>
        );

      case 'error':
        return (
          <>
            <DialogHeader className="text-center">
              <div className="mx-auto mb-4 p-3 bg-red-100 rounded-full w-fit">
                <AlertTriangle className="w-8 h-8 text-red-600" />
              </div>
              <DialogTitle className="text-xl text-red-800">
                Submission Failed
              </DialogTitle>
              <p className="text-sm text-gray-600 mt-2">
                We encountered an error while submitting your exam. Your answers have been preserved.
              </p>
            </DialogHeader>

            <div className="px-6 pb-4">
              <div className="bg-red-50 rounded-lg p-4 mb-4">
                <div className="text-sm text-red-800 font-medium mb-2">Error Details:</div>
                <div className="text-sm text-red-700">{submitError}</div>
              </div>

              <div className="bg-blue-50 rounded-lg p-4">
                <div className="flex items-center gap-2 text-blue-800 mb-2">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm font-medium">Your Answers Are Safe</span>
                </div>
                <p className="text-sm text-blue-700">
                  All your answers have been automatically saved. You can try submitting again or continue working on your exam.
                </p>
              </div>
            </div>

            <DialogFooter className="flex gap-3">
              <Button
                onClick={onClose}
                variant="outline"
                className="flex-1"
              >
                Continue Exam
              </Button>
              <Button
                onClick={onRetry}
                variant="primary"
                className="flex-1"
                iconProps={{ variant: 'refresh-cw' }}
              >
                Try Again
              </Button>
            </DialogFooter>
          </>
        );

      case 'submitting':
        return (
          <>
            <DialogHeader className="text-center">
              <div className="mx-auto mb-4 p-3 bg-blue-100 rounded-full w-fit">
                <Clock className="w-8 h-8 text-blue-600 animate-pulse" />
              </div>
              <DialogTitle className="text-xl text-blue-800">
                Submitting Your Exam...
              </DialogTitle>
              <p className="text-sm text-gray-600 mt-2">
                Please wait while we process your submission. Do not close this window.
              </p>
            </DialogHeader>

            <div className="px-6 pb-4">
              {/* Progress indicator */}
              <div className="mb-4">
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Processing answers...</span>
                  <span>Please wait</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '70%' }}></div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Exam:</span>
                  <span className="font-medium text-gray-900">{examTitle}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Questions Answered:</span>
                  <span className="font-medium text-gray-900">{answeredQuestions} of {totalQuestions}</span>
                </div>
              </div>
            </div>
          </>
        );

      default: // confirm
        return (
          <>
            <DialogHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-full">
                  <Send className="w-6 h-6 text-blue-600" />
                </div>
                <div className="flex-1">
                  <DialogTitle className="text-xl">Submit Exam</DialogTitle>
                  <p className="text-sm text-gray-600 mt-1">
                    Are you ready to submit your exam for grading?
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="rounded-full p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
                  aria-label="Close"
                >
                  <X size={16} />
                </button>
              </div>
            </DialogHeader>

            <div className="px-6 pb-4">
              {/* Exam Summary */}
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-gray-600">Exam Title</div>
                    <div className="font-medium text-gray-900">{examTitle}</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Time Spent</div>
                    <div className="font-medium text-gray-900">{formatTime(timeSpent)}</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Questions Answered</div>
                    <div className="font-medium text-gray-900">{answeredQuestions} of {totalQuestions}</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Completion</div>
                    <div className="font-medium text-gray-900">{completionPercentage}%</div>
                  </div>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="mb-4">
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Progress</span>
                  <span>{completionPercentage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                    style={{ width: `${completionPercentage}%` }}
                  ></div>
                </div>
              </div>

              {/* Warnings */}
              {unansweredQuestions > 0 && (
                <div className="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="w-4 h-4 text-amber-600" />
                    <div className="text-sm">
                      <div className="font-medium text-amber-800">
                        {unansweredQuestions} question{unansweredQuestions > 1 ? 's' : ''} unanswered
                      </div>
                      <div className="text-amber-700">
                        You can still submit, but consider reviewing these questions.
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Details Toggle */}
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="text-sm text-blue-600 hover:text-blue-700 transition-colors duration-200"
              >
                {showDetails ? 'Hide' : 'Show'} submission details
              </button>

              {showDetails && (
                <div className="mt-3 p-3 bg-blue-50 rounded-lg text-sm">
                  <div className="space-y-1 text-blue-800">
                    <div>• Your answers will be automatically graded</div>
                    <div>• Results will be available immediately after submission</div>
                    <div>• You cannot change answers after submission</div>
                    <div>• Your progress and time spent will be recorded</div>
                  </div>
                </div>
              )}
            </div>

            <DialogFooter className="flex gap-3">
              <Button
                onClick={onClose}
                variant="outline"
                className="flex-1"
              >
                Review Answers
              </Button>
              <Button
                onClick={onConfirm}
                variant="primary"
                className="flex-1"
                iconProps={{ variant: 'send', className: 'w-4' }}
              >
                Submit Exam
              </Button>
            </DialogFooter>
          </>
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent 
        ref={dialogRef}
        className={cn('max-w-lg focus-trap', className)}
        aria-labelledby={`dialog-title-${dialogState}`}
        aria-describedby={`dialog-description-${dialogState}`}
      >
        {renderContent()}
      </DialogContent>
    </Dialog>
  );
};