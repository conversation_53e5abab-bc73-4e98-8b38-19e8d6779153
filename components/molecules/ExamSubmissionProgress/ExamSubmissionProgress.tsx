'use client';

import React, { useEffect, useState, useRef } from 'react';
import { CheckCircle, Clock, Send, AlertTriangle } from 'lucide-react';
import { cn } from '@/utils/cn';
import { useAccessibility, useFocusRestore } from '@/hooks/useAccessibility';

export interface ExamSubmissionProgressProps {
  isVisible: boolean;
  stage: 'preparing' | 'uploading' | 'processing' | 'complete' | 'error';
  progress?: number;
  message?: string;
  error?: string;
  className?: string;
}

export const ExamSubmissionProgress: React.FC<ExamSubmissionProgressProps> = ({
  isVisible,
  stage,
  progress = 0,
  message,
  error,
  className
}) => {
  const [displayProgress, setDisplayProgress] = useState(0);
  const dialogRef = useRef<HTMLDivElement>(null);
  
  // Accessibility hooks
  const { announce, focusManagement } = useAccessibility();
  const { saveFocus, restoreFocus } = useFocusRestore();

  // Animate progress bar
  useEffect(() => {
    if (progress > displayProgress) {
      const timer = setTimeout(() => {
        setDisplayProgress(Math.min(displayProgress + 2, progress));
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [progress, displayProgress]);

  // Reset progress when stage changes
  useEffect(() => {
    if (stage === 'preparing') {
      setDisplayProgress(0);
    }
  }, [stage]);

  const getStageConfig = () => {
    switch (stage) {
      case 'preparing':
        return {
          icon: Clock,
          title: 'Preparing Submission',
          description: 'Validating your answers...',
          color: 'blue',
          progress: 25
        };
      case 'uploading':
        return {
          icon: Send,
          title: 'Uploading Answers',
          description: 'Sending your responses to the server...',
          color: 'blue',
          progress: 60
        };
      case 'processing':
        return {
          icon: Clock,
          title: 'Processing Submission',
          description: 'Finalizing your exam submission...',
          color: 'blue',
          progress: 90
        };
      case 'complete':
        return {
          icon: CheckCircle,
          title: 'Submission Complete',
          description: 'Your exam has been successfully submitted!',
          color: 'green',
          progress: 100
        };
      case 'error':
        return {
          icon: AlertTriangle,
          title: 'Submission Failed',
          description: error || 'An error occurred during submission',
          color: 'red',
          progress: displayProgress
        };
      default:
        return {
          icon: Clock,
          title: 'Processing',
          description: 'Please wait...',
          color: 'blue',
          progress: 0
        };
    }
  };

  const config = getStageConfig();
  const IconComponent = config.icon;
  const targetProgress = Math.max(displayProgress, config.progress);

  // Focus management and announcements
  useEffect(() => {
    if (isVisible) {
      saveFocus();
      // Focus the dialog when it becomes visible
      setTimeout(() => {
        if (dialogRef.current) {
          dialogRef.current.focus();
        }
      }, 100);
      
      // Announce the current stage to screen readers
      announce(`${config.title}. ${config.description}`, 'assertive');
    } else {
      restoreFocus();
    }
  }, [isVisible, saveFocus, restoreFocus, announce, config.title, config.description]);

  // Announce progress changes
  useEffect(() => {
    if (isVisible && targetProgress > 0) {
      announce(`Progress: ${Math.round(targetProgress)}% complete`, 'polite');
    }
  }, [targetProgress, isVisible, announce]);

  // Announce stage changes
  useEffect(() => {
    if (isVisible) {
      const stageMessages = {
        preparing: 'Preparing your exam submission',
        uploading: 'Uploading your answers to the server',
        processing: 'Processing your submission',
        complete: 'Exam submitted successfully!',
        error: `Submission failed: ${error || 'An error occurred'}`
      };
      
      announce(stageMessages[stage], stage === 'error' ? 'assertive' : 'polite');
    }
  }, [stage, isVisible, announce, error]);

  if (!isVisible) return null;

  return (
    <div 
      className={cn(
        'fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4',
        className
      )}
      role="dialog"
      aria-modal="true"
      aria-labelledby="submission-title"
      aria-describedby="submission-description"
    >
      <div 
        ref={dialogRef}
        className="bg-white rounded-2xl shadow-2xl w-full max-w-md p-6 transform transition-all duration-300 scale-100 focus:outline-none"
        tabIndex={-1}
      >
        {/* Header */}
        <div className="text-center mb-6">
          <div className={cn(
            'mx-auto mb-4 p-3 rounded-full w-fit',
            {
              'bg-blue-100': config.color === 'blue',
              'bg-green-100': config.color === 'green',
              'bg-red-100': config.color === 'red'
            }
          )}>
            <IconComponent 
              className={cn(
                'w-8 h-8',
                {
                  'text-blue-600': config.color === 'blue',
                  'text-green-600': config.color === 'green',
                  'text-red-600': config.color === 'red'
                },
                stage === 'preparing' || stage === 'uploading' || stage === 'processing' ? 'animate-pulse' : ''
              )}
              aria-hidden="true"
            />
          </div>
          
          <h3 
            id="submission-title"
            className="text-xl font-semibold text-gray-900 mb-2"
          >
            {config.title}
          </h3>
          
          <p 
            id="submission-description"
            className="text-sm text-gray-600"
          >
            {message || config.description}
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Progress</span>
            <span>{Math.round(targetProgress)}%</span>
          </div>
          
          <div 
            className="w-full bg-gray-200 rounded-full h-3 overflow-hidden"
            role="progressbar"
            aria-valuenow={Math.round(targetProgress)}
            aria-valuemin={0}
            aria-valuemax={100}
            aria-label={`Submission progress: ${Math.round(targetProgress)}% complete`}
          >
            <div 
              className={cn(
                'h-3 rounded-full transition-all duration-500 ease-out',
                {
                  'bg-blue-600': config.color === 'blue',
                  'bg-green-600': config.color === 'green',
                  'bg-red-600': config.color === 'red'
                }
              )}
              style={{ width: `${targetProgress}%` }}
            />
          </div>
        </div>

        {/* Stage Indicators */}
        <div className="flex justify-between items-center text-xs text-gray-500">
          <div className={cn(
            'flex items-center gap-1',
            (stage === 'preparing' || stage === 'uploading' || stage === 'processing' || stage === 'complete') ? 'text-blue-600' : ''
          )}>
            <div className={cn(
              'w-2 h-2 rounded-full',
              (stage === 'preparing' || stage === 'uploading' || stage === 'processing' || stage === 'complete') ? 'bg-blue-600' : 'bg-gray-300'
            )} />
            <span>Prepare</span>
          </div>
          
          <div className={cn(
            'flex items-center gap-1',
            (stage === 'uploading' || stage === 'processing' || stage === 'complete') ? 'text-blue-600' : ''
          )}>
            <div className={cn(
              'w-2 h-2 rounded-full',
              (stage === 'uploading' || stage === 'processing' || stage === 'complete') ? 'bg-blue-600' : 'bg-gray-300'
            )} />
            <span>Upload</span>
          </div>
          
          <div className={cn(
            'flex items-center gap-1',
            (stage === 'processing' || stage === 'complete') ? 'text-blue-600' : ''
          )}>
            <div className={cn(
              'w-2 h-2 rounded-full',
              (stage === 'processing' || stage === 'complete') ? 'bg-blue-600' : 'bg-gray-300'
            )} />
            <span>Process</span>
          </div>
          
          <div className={cn(
            'flex items-center gap-1',
            stage === 'complete' ? 'text-green-600' : ''
          )}>
            <div className={cn(
              'w-2 h-2 rounded-full',
              stage === 'complete' ? 'bg-green-600' : 'bg-gray-300'
            )} />
            <span>Complete</span>
          </div>
        </div>

        {/* Error Details */}
        {stage === 'error' && error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="text-sm text-red-800 font-medium mb-1">Error Details:</div>
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}

        {/* Loading Animation */}
        {(stage === 'preparing' || stage === 'uploading' || stage === 'processing') && (
          <div className="mt-4 flex justify-center">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};