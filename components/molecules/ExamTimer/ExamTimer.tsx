'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Clock, AlertTriangle, Timer } from 'lucide-react';
import { cn } from '@/utils/cn';

export interface ExamTimerProps {
  /** Time limit in minutes */
  timeLimit?: number;
  /** Called when timer expires */
  onTimeExpired?: () => void;
  /** Current exam ID for persistence */
  examId?: string;
  /** Whether to show in compact mode */
  isCompact?: boolean;
  /** Custom className */
  className?: string;
  /** Whether to start the timer immediately */
  autoStart?: boolean;
}

/** Helper function to clear exam timer data from localStorage */
export const clearExamTimer = (examId: string) => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(`exam-start-${examId}`);
    console.log('Cleared exam timer for:', examId);
  }
};

export const ExamTimer: React.FC<ExamTimerProps> = ({
  timeLimit,
  onTimeExpired,
  examId,
  isCompact = false,
  className,
  autoStart = true,
}) => {
  // Validate timeLimit first
  const validTimeLimit = useMemo(() => {
    if (!timeLimit) return null;
    const parsed = typeof timeLimit === 'string' ? parseInt(timeLimit, 10) : timeLimit;
    return (isNaN(parsed) || parsed <= 0) ? null : parsed;
  }, [timeLimit]);

  // Initialize start time with proper persistence
  const [examStartTime] = useState(() => {
    if (!validTimeLimit || !examId) return Date.now();
    
    // Try to restore from localStorage if examId is provided
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem(`exam-start-${examId}`);
      if (saved) {
        try {
          const { startTime: savedStartTime, timeLimit: savedTimeLimit } = JSON.parse(saved);
          // Only use saved time if it's for the same exam with same time limit
          if (savedTimeLimit === validTimeLimit && savedStartTime) {
            console.log('Restored exam start time:', new Date(savedStartTime).toISOString());
            return savedStartTime;
          }
        } catch (e) {
          console.warn('Failed to parse saved timer data:', e);
        }
      }
      
      // Save new start time
      const newStartTime = Date.now();
      localStorage.setItem(`exam-start-${examId}`, JSON.stringify({
        startTime: newStartTime,
        timeLimit: validTimeLimit,
      }));
      console.log('Created new exam start time:', new Date(newStartTime).toISOString());
      return newStartTime;
    }
    
    return Date.now();
  });

  // Calculate real-time remaining seconds
  const [remainingSeconds, setRemainingSeconds] = useState<number>(0);
  
  // Update remaining time based on actual elapsed time
  const updateRemainingTime = useCallback(() => {
    if (!validTimeLimit) {
      setRemainingSeconds(0);
      return 0;
    }
    
    const now = Date.now();
    const elapsedSeconds = Math.floor((now - examStartTime) / 1000);
    const totalSeconds = validTimeLimit * 60;
    const remaining = Math.max(0, totalSeconds - elapsedSeconds);
    
    setRemainingSeconds(remaining);
    return remaining;
  }, [validTimeLimit, examStartTime]);

  // Initialize remaining time
  useEffect(() => {
    updateRemainingTime();
  }, [updateRemainingTime]);

  const [isActive, setIsActive] = useState(autoStart && validTimeLimit !== null);

  // Start timer immediately if autoStart is true
  useEffect(() => {
    if (autoStart && validTimeLimit !== null && !isActive) {
      setIsActive(true);
    }
  }, [autoStart, validTimeLimit, isActive]);

  // Real-time countdown effect
  useEffect(() => {
    if (!isActive || !validTimeLimit) return;

    // Update immediately
    const remaining = updateRemainingTime();
    
    // Set up interval for real-time updates
    const interval = setInterval(() => {
      const remaining = updateRemainingTime();
      
      // Handle time expiration
      if (remaining === 0) {
        setIsActive(false);
        onTimeExpired?.();
        
        // Clean up localStorage
        if (examId && typeof window !== 'undefined') {
          localStorage.removeItem(`exam-start-${examId}`);
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isActive, validTimeLimit, updateRemainingTime, onTimeExpired, examId]);

  // Format time display
  const formatTime = useMemo(() => {
    // Ensure we have a valid number
    const validSeconds = Math.max(0, Math.floor(remainingSeconds));
    const hours = Math.floor(validSeconds / 3600);
    const minutes = Math.floor((validSeconds % 3600) / 60);
    const seconds = validSeconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, [remainingSeconds]);

  // Get time status and colors
  const timeStatus = useMemo(() => {
    if (!validTimeLimit || remainingSeconds < 0) return 'normal';
    
    const totalSeconds = validTimeLimit * 60;
    const percentage = Math.max(0, Math.min(100, (remainingSeconds / totalSeconds) * 100));
    
    if (percentage <= 5) return 'critical'; // Last 5%
    if (percentage <= 15) return 'warning'; // Last 15%
    return 'normal';
  }, [remainingSeconds, validTimeLimit]);

  const getStatusColors = () => {
    switch (timeStatus) {
      case 'critical':
        return {
          bg: 'bg-red-50 border-red-200',
          text: 'text-red-700',
          icon: 'text-red-600',
          pulse: 'animate-pulse',
        };
      case 'warning':
        return {
          bg: 'bg-amber-50 border-amber-200',
          text: 'text-amber-700',
          icon: 'text-amber-600',
          pulse: '',
        };
      default:
        return {
          bg: 'bg-blue-50 border-blue-200',
          text: 'text-blue-700',
          icon: 'text-blue-600',
          pulse: '',
        };
    }
  };

  const statusColors = getStatusColors();

  // Don't render if no valid time limit
  if (!validTimeLimit) return null;

  if (isCompact) {
    return (
      <div
        className={cn(
          'flex items-center gap-2 px-3 py-2 rounded-lg border font-medium text-sm transition-all duration-300',
          statusColors.bg,
          statusColors.text,
          statusColors.pulse,
          className
        )}
      >
        <Clock className={cn('w-4 h-4', statusColors.icon)} />
        <span className="font-mono font-semibold">
          {formatTime}
        </span>
        {timeStatus !== 'normal' && (
          <AlertTriangle className="w-4 h-4" />
        )}
      </div>
    );
  }

  return (
    <div
      className={cn(
        'rounded-xl border p-4 transition-all duration-300',
        statusColors.bg,
        statusColors.pulse,
        className
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className={cn(
            'w-8 h-8 rounded-lg flex items-center justify-center',
            timeStatus === 'critical' ? 'bg-red-100' :
            timeStatus === 'warning' ? 'bg-amber-100' : 'bg-blue-100'
          )}>
            <Timer className={cn('w-4 h-4', statusColors.icon)} />
          </div>
          <div>
            <h3 className={cn('text-sm font-semibold', statusColors.text)}>
              Time Remaining
            </h3>
            <p className="text-xs text-gray-500">
              {timeStatus === 'critical' ? 'Time almost up!' :
               timeStatus === 'warning' ? 'Running low on time' :
               'Keep track of your progress'}
            </p>
          </div>
        </div>
        
        {timeStatus !== 'normal' && (
          <AlertTriangle className={cn('w-5 h-5', statusColors.icon)} />
        )}
      </div>

      {/* Time Display */}
      <div className="text-center">
        <div className={cn(
          'text-3xl font-mono font-bold tracking-wider',
          statusColors.text
        )}>
          {formatTime}
        </div>
        
        {/* Progress Bar */}
        <div className="mt-3 mb-2">
          <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
            <div
              className={cn(
                'h-full rounded-full transition-all duration-1000 ease-out',
                timeStatus === 'critical' ? 'bg-red-500' :
                timeStatus === 'warning' ? 'bg-amber-500' : 'bg-blue-500'
              )}
              style={{ 
                width: `${Math.max(0, Math.min(100, (remainingSeconds / (validTimeLimit * 60)) * 100))}%` 
              }}
            />
          </div>
        </div>
        
        {/* Status Text */}
        <div className="text-xs text-gray-500">
          {remainingSeconds === 0 ? 'Time expired' :
           timeStatus === 'critical' ? 'Final moments' :
           timeStatus === 'warning' ? 'Time running out' :
           'Plenty of time remaining'}
        </div>
      </div>
    </div>
  );
}; 