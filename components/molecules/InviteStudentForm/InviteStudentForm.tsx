'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSession } from 'next-auth/react';
import { Mail, UserPlus } from 'lucide-react';
import { z } from 'zod';
import { Button } from '@/components/atoms/Button/Button';
import { Input } from '@/components/atoms/Input/Input';
import { FormField } from '@/components/molecules/FormField/FormField';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { sendStudentInvitationAction } from '@/actions/exam.action';
import { sendInvitationWithExamAction } from '@/actions/invitation.action';
import { useToast } from '@/providers/ToastProvider';
import { requiredString } from '@/utils/zod';

// Validation schema for invitation form
const inviteStudentSchema = z.object({
  email: requiredString.email('Please enter a valid email address'),
});

type InviteStudentFormData = z.infer<typeof inviteStudentSchema>;

export interface InviteStudentFormProps {
  examId?: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export const InviteStudentForm: React.FC<InviteStudentFormProps> = ({
  examId,
  onSuccess,
  onCancel,
}) => {
  const { data: session } = useSession();
  const { showSuccess, showError } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<InviteStudentFormData>({
    resolver: zodResolver(inviteStudentSchema),
    defaultValues: {
      email: '',
    },
  });

  const handleInviteStudent = async (data: InviteStudentFormData) => {
    if (!session?.user?.schoolId) {
      setError('School information not found. Please try again.');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      const response = examId 
        ? await sendInvitationWithExamAction(
            data.email,
            session.user.schoolId,
            [examId]
          )
        : await sendStudentInvitationAction(
            data.email,
            session.user.schoolId
          );

      if (response.status === 'success') {
        const successMessage = examId 
          ? `Invitation sent successfully to ${data.email}. They will receive an email with instructions to join your school and will automatically be assigned to this exam upon registration.`
          : `Invitation sent successfully to ${data.email}. They will receive an email with instructions to join your school.`;
        setSuccess(successMessage);
        
        const toastMessage = examId 
          ? `Invitation sent and exam assigned to ${data.email}`
          : `Invitation sent to ${data.email}`;
        showSuccess('Invitation Sent', toastMessage);
        reset();

        // Auto-close after success
        setTimeout(() => {
          onSuccess();
        }, 2000);
      } else {
        // Handle specific error cases
        let errorMessage = 'Failed to send invitation';

        if (typeof response.message === 'string') {
          errorMessage = response.message;

          // Provide more user-friendly message for existing active user error
          if (response.message.includes('already exists and is active')) {
            errorMessage = examId 
              ? `A user with the email ${data.email} already has an active account. They can sign in directly or you can contact them to join this exam.`
              : `A user with the email ${data.email} already has an active account. They can sign in directly.`;
          }
        }

        setError(errorMessage);
        showError('Invitation Failed', errorMessage);
      }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred while sending the invitation';
      setError(errorMessage);
      showError('Invitation Failed', errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center gap-2 mb-4">
          <UserPlus size={20} className="text-primary-action" />
          <h3 className="text-lg font-medium text-text-primary">Invite New Student</h3>
        </div>
        <p className="text-sm text-text-secondary">
          {examId 
            ? "Send an invitation email to a new student. They will be automatically assigned to this exam once they complete their registration."
            : "Send an invitation email to a new student. They will be able to join your school once they complete their registration."
          }
        </p>
      </div>

      {/* Content */}
      <div className="flex-1 p-4">
        {/* Success/Error Messages */}
        {success && (
          <div className="mb-4">
            <AlertMessage type="success" message={success} />
          </div>
        )}
        
        {error && (
          <div className="mb-4">
            <AlertMessage type="error" message={error} />
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit(handleInviteStudent)} className="space-y-6">
          {/* Email Field */}
          <FormField
            label="Student Email Address"
            error={errors.email?.message}
            required
          >
            <div className="relative">
              <Mail size={18} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
              <Input
                type="email"
                placeholder="Enter student's email address"
                className="pl-10"
                disabled={isSubmitting}
                {...register('email')}
              />
            </div>
          </FormField>

          {/* School Information */}
          {session?.user?.school && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="text-sm">
                <p className="text-text-secondary mb-1">Inviting to school:</p>
                <p className="font-medium text-text-primary">{session.user.school.name}</p>
              </div>
            </div>
          )}
        </form>
      </div>

      {/* Footer Actions */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-end gap-3">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit(handleInviteStudent)}
            disabled={isSubmitting || !session?.user?.schoolId}
            iconProps={isSubmitting ? { variant: "refresh-ccw", className: "animate-spin mr-2 w-5", size: 16 } : { variant: "mail", className: "mr-2 w-5", size: 16 }}
          >
            {isSubmitting ? "Sending Invitation..." : examId ? "Send Invitation & Assign" : "Send Invitation"}
          </Button>
        </div>
      </div>
    </div>
  );
};
