'use client';

import React from 'react';
import { X, UserPlus } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/atoms/Dialog/Dialog';
import { Button } from '@/components/atoms/Button/Button';
import { InviteStudentForm } from '../InviteStudentForm/InviteStudentForm';

export interface InviteStudentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export const InviteStudentModal: React.FC<InviteStudentModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const handleSuccess = () => {
    onSuccess?.();
    onClose();
  };

  const handleClose = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg mx-auto h-auto max-h-[90vh] flex flex-col">
        {/* Header */}
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <UserPlus size={20} className="text-primary-action" />
              Invite Student
            </DialogTitle>
            <Button
              variant="ghost"
              onClick={handleClose}
              iconProps={{ variant: "x", className: "w-4" }}
              className="text-text-secondary hover:text-text-primary"
            />
          </div>
        </DialogHeader>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <InviteStudentForm
            onSuccess={handleSuccess}
            onCancel={handleClose}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}; 