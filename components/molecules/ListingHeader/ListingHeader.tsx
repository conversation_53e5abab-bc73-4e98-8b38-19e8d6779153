import { Button, ButtonProps } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';

type IListingButton = ButtonProps & { label: string };
export interface IListingHeaderProps {
  title: string;
  subtitle?: string;
  buttonProps?: IListingButton | IListingButton[];
  children?: React.ReactNode;
  className?: string;
}

export const ListingHeader: React.FC<IListingHeaderProps> = ({
  title,
  subtitle,
  buttonProps,
  children,
  className,
}) => {
  const renderButton = (button?: IListingButton, index?: number) => {
    if (!button) return;
    const { label, ...restButtonProps } = button;
    return (
      <Button
        key={index}
        className="shadow-sm hover:shadow-md text-white transition-shadow px-3 py-1.5 rounded-lg flex items-center gap-1.5 w-auto text-sm"
        {...restButtonProps}
      >
        {label}
      </Button>
    );
  };
  return (
    <header className={cn('bg-white p-4', className)}>
      <div className="flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
        <div className="flex-1">
          <h1 className="text-xlarge font-bold">{title}</h1>
          {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
        </div>
        <div className="flex items-center gap-2 justify-start overflow-x-auto hide-scrollbar">
          {children}
          {buttonProps && Array.isArray(buttonProps)
            ? buttonProps.map(renderButton)
            : renderButton(buttonProps)}
        </div>
      </div>
    </header>
  );
};
