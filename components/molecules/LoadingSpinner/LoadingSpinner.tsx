'use client';

import React from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/utils/cn';

export interface LoadingSpinnerProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  fullScreen?: boolean;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  message = 'Loading...',
  size = 'md',
  className = '',
  fullScreen = false
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  const containerClasses = fullScreen 
    ? 'min-h-screen flex items-center justify-center bg-gray-50'
    : 'flex items-center justify-center p-4';

  return (
    <div className={cn(containerClasses, className)}>
      <div className="text-center" role="status" aria-live="polite">
        <Loader2 
          className={cn(
            sizeClasses[size], 
            'animate-spin mx-auto mb-4 text-primary'
          )} 
          aria-hidden="true" 
        />
        <p className="text-gray-600">{message}</p>
        <span className="sr-only">{message}, please wait...</span>
      </div>
    </div>
  );
};
