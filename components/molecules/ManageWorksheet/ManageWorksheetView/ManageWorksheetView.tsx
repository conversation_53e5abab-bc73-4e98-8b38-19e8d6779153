'use client';

import { handleGenerateWorksheet } from '@/actions/worksheet.action';
import { Button } from '@/components/atoms/Button/Button';
import Steps from '@/components/atoms/Steps/Steps';
import FormItem from '@/components/molecules/FormItems/FormItem';
import { TRHFQuestionValue } from '@/components/molecules/FormItems/RHFQuestion/RHFQuestion';
import { OptionTypes } from '@/components/molecules/FormItems/FormItem.type';
import { convertObjectToFormData } from '@/helpers/convertObjectToFormData';
import { DEFAULT_QUESTION_COUNT } from '@/utils/constants';
import { parseQuestionCount } from '@/utils/questionUtils';
import { useSubscription } from '@/stores/jotai/subscription/useSubscription';
import { useToast } from '@/stores/jotai/subscription/useToast';
import { useEffect, useState, useMemo } from 'react';
import { useFormStatus } from 'react-dom';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import BottomDock from '@/components/organisms/BottomDock/BottomDock';
import { Crown } from 'lucide-react';

// Define a more specific type for formSetting
export type TFormSetting = {
  name: string;
  type?: OptionTypes; // Use OptionTypes for better type safety
  question?: string;
  options?: { // options is optional for types like 'topicSelector'
    value: string;
    label: string;
    isActive?: boolean;
  }[];
  enableCustomInput?: boolean; // For RHFQuestion custom input
  // Potentially other fields if 'any' was hiding them
};

export type TConfigItem = { // Renamed TConfig to TConfigItem for clarity if TConfig is used elsewhere
  stepLabel: string;
  formSetting: TFormSetting | any; // Retain | any for flexibility if other types exist, but prefer TFormSetting
};

export type TConfig = TConfigItem[]; // TConfig is an array of TConfigItem

type TManageWorksheetViewProps = {
  config: TConfig;
  defaultValues?: Record<string, any>;
};

// Upgrade prompt component for restricted features
const UpgradePrompt: React.FC<{ feature: string; onUpgrade: () => void }> = ({ feature, onUpgrade }) => (
  <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-4 mb-4">
    <div className="flex items-start gap-3">
      <Crown className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
      <div className="flex-1">
        <h4 className="text-sm font-semibold text-yellow-800 mb-1">
          Premium Feature: {feature}
        </h4>
        <p className="text-sm text-yellow-700 mb-3">
          This feature is available to premium subscribers. Upgrade your plan to unlock unlimited questions and advanced features.
        </p>
        <Button
          onClick={onUpgrade}
          className="bg-yellow-600 hover:bg-yellow-700 text-white text-sm px-3 py-1.5 [&>span]:inline-flex [&>span]:items-center [&>span]:justify-center"
        >
          <Crown className="w-4 h-4 mr-1.5" />
          <span>Upgrade Now</span>
        </Button>
      </div>
    </div>
  </div>
);

export const ManageWorksheetView: React.FC<TManageWorksheetViewProps> = ({
  config,
  defaultValues,
}) => {
  const [currenStep, setCurrentStep] = useState(0);
  const { pending } = useFormStatus();
  const [currentActualTotalQuestions, setCurrentActualTotalQuestions] = useState(DEFAULT_QUESTION_COUNT); // Use constant
  
  // Subscription state
  const { 
    hasCustomQuestionLimit, 
    maxAllowedQuestions,
    isActiveSubscription,
    loading: subscriptionLoading 
  } = useSubscription();

  // Toast notifications
  const { showWarning } = useToast();

  // Filter question_count options based on subscription status
  const filteredConfig = useMemo(() => {
    const configCopy = [...config];
    
    // Find the question_count step
    const questionCountStepIndex = configCopy.findIndex(
      (item) => item.formSetting.name === 'question_count'
    );
    
    if (questionCountStepIndex !== -1 && configCopy[questionCountStepIndex].formSetting.options) {
      const questionCountStep = configCopy[questionCountStepIndex];
      
      // console.log('Original question_count options:', questionCountStep.formSetting.options);
      // console.log('User has active subscription:', isActiveSubscription);
      
      // If user doesn't have active subscription, filter to only show 10 and 20
      if (!isActiveSubscription) {
        const filteredOptions = questionCountStep.formSetting.options?.filter((option: { value: string; label: string; isActive?: boolean }) => {
          // Values are strings like '10', '20', '50', '100'
          // Only allow '10' and '20' for free users
          return option.label === '10' || option.label === '20';
        });
        
        // console.log('Filtered question_count options for free user:', filteredOptions);
        
        // Create a new step with filtered options
        configCopy[questionCountStepIndex] = {
          ...questionCountStep,
          formSetting: {
            ...questionCountStep.formSetting,
            options: filteredOptions
          }
        };
      }
    }
    
    return configCopy;
  }, [config, isActiveSubscription]);

  const isLastStep = currenStep === filteredConfig.length - 1;

  const methods = useForm<Record<string, any>>({
    defaultValues,
  });
  
  // Define maxStep before onNextStep uses it
  const maxStep = filteredConfig.length - 1;

  const onNextStep = () => {
    const newStep = currenStep + 1;
    if (newStep <= maxStep) { 
      setCurrentStep(newStep);
    }
  };

  const onPreStep = () => {
    const newStep = currenStep - 1;
    if (newStep < 0) return;
    setCurrentStep((preStep) => preStep - 1);
  };

  // Watch for the question_count value to pass to the question types step
  const questionCountValue = useWatch({
    control: methods.control,
    name: 'question_count',
    // defaultValue: { value: '20', label: '20 Questions', isCustom: false } // More appropriate default
  }) as TRHFQuestionValue | string | undefined;

  useEffect(() => {
    const newTotal = parseQuestionCount(questionCountValue, DEFAULT_QUESTION_COUNT);
    setCurrentActualTotalQuestions(newTotal);
  }, [questionCountValue]);

  // Handle upgrade navigation
  const handleUpgrade = () => {
    // Navigate to pricing page
    window.location.href = '/pricing';
  };

  // Check if current step uses restricted features
  const getCurrentStepRestrictions = () => {
    if (currenStep >= filteredConfig.length) return null;
    
    // Check if user is trying to exceed question limit
    if (hasCustomQuestionLimit && currentActualTotalQuestions > maxAllowedQuestions) {
      return {
        type: 'questionLimit',
        feature: 'Unlimited Questions',
        message: `Free users are limited to ${maxAllowedQuestions} questions. You currently have ${currentActualTotalQuestions} questions selected.`
      };
    }
    
    return null;
  };

  const currentRestriction = getCurrentStepRestrictions();

  const onSubmit = async (values: any) => {
    // Process question count
    if (values.question_count !== undefined) {
      const originalQcValue = values.question_count as TRHFQuestionValue | string | undefined;
      const questionCount = parseQuestionCount(originalQcValue, DEFAULT_QUESTION_COUNT);
      
      // Check if user is trying to exceed question limit
      if (hasCustomQuestionLimit && questionCount > maxAllowedQuestions) {
        showWarning(`Free users are limited to ${maxAllowedQuestions} questions. Please upgrade your subscription to use unlimited questions.`, 6000);
        return;
      }
      
      values.question_count = questionCount;

      // Determine isCustomQuestionCount based on the original structure
      if (typeof originalQcValue === 'object' && originalQcValue !== null && 'isCustom' in originalQcValue) {
        values.isCustomQuestionCount = !!originalQcValue.isCustom; // Ensure boolean
      } else {
        // If it's a direct string or number, or undefined, assume not custom.
        // The utility function handles undefined by returning default, which isn't "custom" in this context.
        values.isCustomQuestionCount = false;
      }
    } else {
      // If question_count was not in form values at all, set a default and mark not custom
      values.question_count = DEFAULT_QUESTION_COUNT;
      values.isCustomQuestionCount = false;
    }

    if (isLastStep) {
      // Filter question_type to remove items with count 0
      if (values.question_type && Array.isArray(values.question_type)) {
        values.question_type = values.question_type.filter(
          (qt: { count: number }) => qt.count !== 0
        );
      }

      // Create a new object for submission, explicitly excluding totalQuestions
      const { totalQuestions, ...submissionValues } = values;

      const formData: any = convertObjectToFormData(submissionValues);
      await handleGenerateWorksheet(formData);
    } else {
      onNextStep(); // Move to next step if not the last one
    }
  };
  // const maxStep = config.length - 1; // Moved this definition up

  if (pending) return <div>pending submit</div>;

  return (
    <div className="flex w-full flex-col pb-24">
      <div className="flex flex-col gap-3 sm:gap-6 w-full pt-3 sm:pt-8 sm:px-6 lg:px-8">
        <div className="text-black-100 font-semibold text-lg sm:text-xl">
          Create Worksheet
        </div>
        
        {/* Show upgrade prompt if current step has restrictions */}
        {currentRestriction && (
          <UpgradePrompt 
            feature={currentRestriction.feature}
            onUpgrade={handleUpgrade}
          />
        )}
        
        <div>
          <Steps
            key="worksheet-steps"
            currentStep={currenStep}
            className="w-full"
            steps={filteredConfig?.map((c) => ({ label: c.stepLabel }))}
          />
        </div>
        <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
        <FormProvider {...methods}>
          <div>
            {filteredConfig?.map((c, index) => {
              const isHidden = currenStep !== filteredConfig.indexOf(c);
              const formSetting = c.formSetting as TFormSetting; // Use the more specific type

              const isQuestionTypesStep = formSetting?.type === 'questionTypes';

              const attributes: any = { // Use any for attributes for now, can be typed better
                name: formSetting.name,
                options: isQuestionTypesStep
                  ? {
                      // ...formSetting.options, // This was spreading array into object
                      type: {
                        questionTypes: formSetting.options, // This should be the options array
                        totalQuestions: currentActualTotalQuestions, // Use the state variable
                      }
                    }
                  : formSetting.options,
                hidden: isHidden,
                question: formSetting?.question,
              };

              // Pass enableCustomInput if it exists in formSetting
              if (formSetting.enableCustomInput !== undefined) {
                attributes.enableCustomInput = formSetting.enableCustomInput;
              }

              // Fix auto-scroll issue by using stable keys
              const itemKey = `form-item-${index}-${c.stepLabel}-${formSetting.name}`;

              return (
                <FormItem
                  key={itemKey}
                  type={formSetting?.type || 'question'}
                  attributes={attributes}
                />
              );
            })}
          </div>

          {/* Mobile: Use BottomDock, Desktop: Normal flow */}
          <BottomDock className="lg:hidden">
            <div className="flex flex-row gap-2 w-full">
              <Button
                variant="outline"
                onClick={onPreStep}
                disabled={currenStep === 0}
                type="button"
                className="flex-1 h-10 text-sm"
              >
                Prev
              </Button>
              <Button type="submit" className="flex-1 h-10 text-sm">
                {isLastStep ? 'Generate' : 'Next'}
              </Button>
            </div>
          </BottomDock>

          {/* Desktop: Static positioning */}
          <div className="hidden lg:flex flex-row gap-6 justify-end">
            <Button
              variant="outline"
              onClick={onPreStep}
              disabled={currenStep === 0}
              type="button"
              className="w-[7.5rem] h-auto text-base"
            >
              Prev
            </Button>
            <Button type="submit" className="w-[7.5rem] h-auto text-base">
              {isLastStep ? 'Generate' : 'Next'}
            </Button>
          </div>
        </FormProvider>
      </form>
      </div>
    </div>
  );
};
