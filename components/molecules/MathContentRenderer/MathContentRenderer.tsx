'use client';

import React from 'react';
import { MathMLRenderer } from '@/components/atoms/MathMLRenderer';
import { parseContentWithMath, containsMathML } from '@/utils/mathUtils';
import { cn } from '@/utils/cn';

export interface MathContentRendererProps {
  content: string;
  className?: string;
  fallbackToText?: boolean;
  onRenderError?: (error: Error) => void;
}

/**
 * MathContentRenderer component that intelligently renders content containing
 * both regular HTML and MathML expressions.
 */
export const MathContentRenderer: React.FC<MathContentRendererProps> = ({
  content,
  className,
  fallbackToText = true,
  onRenderError,
}) => {
  // If no MathML content is detected, render as regular HTML
  if (!containsMathML(content)) {
    return (
      <div
        className={cn('prose prose-sm max-w-none', className)}
        dangerouslySetInnerHTML={{ __html: content }}
      />
    );
  }

  // Parse content into text and MathML parts
  const contentParts = parseContentWithMath(content);

  return (
    <div className={cn('math-content-container', className)}>
      {contentParts.map((part) => {
        if (part.type === 'mathml') {
          return (
            <MathMLRenderer
              key={part.index}
              content={part.content}
              fallbackToText={fallbackToText}
              onRenderError={onRenderError}
              className="inline-block align-middle mx-1"
            />
          );
        } else {
          return (
            <span
              key={part.index}
              className="inline"
              dangerouslySetInnerHTML={{ __html: part.content }}
            />
          );
        }
      })}
    </div>
  );
};