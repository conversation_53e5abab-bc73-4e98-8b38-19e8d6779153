'use client';

import React from 'react';
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import { ExamHeader, PrintOptions } from '@/components/molecules/PrintModal/types';
import { sanitizeHtml } from '@/utils/sanitizeHtml';
import { cn } from '@/utils/cn';
import Image from 'next/image';

interface ExamPrintPreviewProps {
  questions: Question[];
  examHeader: ExamHeader;
  printOptions: PrintOptions;
  isHtmlContent?: boolean;
}

// Helper function to strip HTML tags (when isHtmlContent is false)
const stripHtml = (html: string) => {
  return html.replace(/<[^>]*>?/gm, '');
};

// Helper function to identify and process fill-in-the-blank content
const processFillBlankContent = (content: string, answers: string[] = [], isHtmlContent = false) => {
  try {
    // Validate inputs
    if (typeof content !== 'string') {
      console.warn('processFillBlankContent: content is not a string', content);
      return 'Invalid content';
    }

    if (!Array.isArray(answers)) {
      console.warn('processFillBlankContent: answers is not an array', answers);
      answers = [];
    }

    // Only strip HTML if isHtmlContent is false
    const plainContent = isHtmlContent ? content : content.replace(/<[^>]*>?/gm, '');

    // Enhanced regex to match various blank patterns
    const blankPatterns = /___|___\*|___\.|___,|___!|___\?|\[blank\]|\{blank\}|\(___\)/g;

    // Find all matches with their positions
    const matches = [];
    let match;
    const contentCopy = String(plainContent);

    while ((match = blankPatterns.exec(contentCopy)) !== null) {
      matches.push({
        pattern: match[0],
        index: match.index,
        length: match[0].length
      });
    }

    // If no blanks found or no answers provided, just replace with standard underscores
    if (matches.length === 0 || !answers || answers.length === 0) {
      return plainContent.replace(blankPatterns, '________');
    }

    // Process the content with blanks
    const processedContent = plainContent;
    let lastIndex = 0;
    let result = '';
    let answerIndex = 0;

    // Replace each blank with appropriate number of underscores based on answer length
    for (const match of matches) {
      // Add text before this blank
      if (match.index > lastIndex) {
        result += processedContent.substring(lastIndex, match.index);
      }

      // Get the answer for this blank
      const answer = answers[answerIndex] || '';

      // Calculate number of underscores based on answer length (minimum 8)
      const underscoreCount = Math.max(8, answer.length * 1.5);
      const underscores = '_'.repeat(underscoreCount);

      // Add the underscores
      result += underscores;

      // Update indices
      lastIndex = match.index + match.length;
      answerIndex++;

      // Break if we've used all answers
      if (answerIndex >= answers.length) break;
    }

    // Add any remaining text
    if (lastIndex < processedContent.length) {
      result += processedContent.substring(lastIndex);
    }

    return result;
  } catch (error) {
    console.error('Error in processFillBlankContent:', error);
    return content || 'Error processing content';
  }
};

export const ExamPrintPreview: React.FC<ExamPrintPreviewProps> = React.memo(({ 
  questions = [], 
  examHeader, 
  printOptions,
  isHtmlContent = false 
}) => {

  // Validate and sanitize questions data
  const validateQuestion = (question: any, index: number): boolean => {
    if (!question || typeof question !== 'object') {
      console.warn(`Question at index ${index} is not a valid object:`, question);
      return false;
    }

    // Check required properties
    if (typeof question.content !== 'string') {
      console.warn(`Question at index ${index} has invalid content:`, question.content);
      return false;
    }

    if (!Array.isArray(question.options)) {
      console.warn(`Question at index ${index} has invalid options:`, question.options);
      return false;
    }

    if (!Array.isArray(question.answer)) {
      console.warn(`Question at index ${index} has invalid answer:`, question.answer);
      return false;
    }

    return true;
  };

  // Ensure questions is an array and filter out invalid questions
  const safeQuestions = Array.isArray(questions)
    ? questions.filter((question, index) => validateQuestion(question, index))
    : [];

  try {
    return (
      <div className="max-w-4xl mx-auto bg-white p-8 print:p-6 print:max-w-none">
        {/* Header Section */}
        <div className="text-center mb-6 border-b border-gray-200 pb-6">
          {examHeader.logoUrl && (
            <div className="mb-4">
              <Image
                src={examHeader.logoUrl}
                alt="School Logo"
                width={120}
                height={64}
                className="mx-auto h-16 w-auto object-contain"
                style={{ objectFit: 'contain' }}
                priority
              />
            </div>
          )}
          <h1 className="text-2xl font-bold text-gray-900 mb-2">{examHeader.schoolName}</h1>
          <h2 className="text-xl font-bold text-gray-800 mb-2 uppercase">{examHeader.examTitle}</h2>
          <h3 className="text-lg text-gray-700 mb-4">{examHeader.subject}</h3>

          <div className="flex justify-between items-center text-sm text-gray-600 max-w-2xl mx-auto">
            <span>Date: {examHeader.examDate}</span>
            {examHeader.duration && <span>Duration: {examHeader.duration}</span>}
            {examHeader.gradeLevel && <span>Grade: {examHeader.gradeLevel}</span>}
          </div>
        </div>

        {/* Student Information */}
        <div className="mb-6 space-y-4">
          <div className="flex items-center">
            <span className="font-semibold text-gray-700 w-20">Name:</span>
            <div className="flex-1 border-b border-gray-400 h-6 ml-4">
              {examHeader.studentName && (
                <span className="text-gray-800">{examHeader.studentName}</span>
              )}
            </div>
          </div>
          <div className="flex items-center">
            <span className="font-semibold text-gray-700 w-20">Class:</span>
            <div className="flex-1 border-b border-gray-400 h-6 ml-4">
              {examHeader.className && (
                <span className="text-gray-800">{examHeader.className}</span>
              )}
            </div>
          </div>
        </div>

        {/* Instructions */}
        {printOptions.includeInstructions && (
          <div className="mb-8 border border-gray-400 p-4">
            <h4 className="text-sm font-bold mb-3 uppercase text-gray-800">Instructions to Candidates</h4>
            <div className="space-y-1 text-xs text-gray-700">
              <p>1. This question paper consists of multiple pages.</p>
              <p>2. Answer all questions in the spaces provided.</p>
              <p>3. Follow all instructions carefully.</p>
              {printOptions.includeMarkAllocation && (
                <p>4. The marks for each question are shown in brackets [ ].</p>
              )}
            </div>
          </div>
        )}

        {/* Questions */}
        <div className="space-y-6">
          {safeQuestions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-lg text-gray-500">
                No questions available. Please add questions to your worksheet.
              </p>
            </div>
          ) : (
            safeQuestions.map((question, index) => {
              try {
                return (
                  <div key={index} className="mb-6 print:break-inside-avoid">
                    <div className="flex justify-between items-center mb-3">
                      <h5 className="text-sm font-semibold text-gray-800">Question {index + 1}</h5>
                      {printOptions.includeMarkAllocation && (
                        <span className="text-sm italic text-gray-600">
                          [{question.type === 'multiple_choice' || question.type === 'single_choice' ? 1 : 2} marks]
                        </span>
                      )}
                    </div>

                    {/* Question Content - Handle fill_blank differently */}
                    {question.type === 'fill_blank' ? (
                      <div className="mb-4 leading-6">
                        {isHtmlContent ? (
                          <div
                            className="text-sm text-gray-800 leading-relaxed"
                            dangerouslySetInnerHTML={{
                              __html: sanitizeHtml(processFillBlankContent(question.content || '', question.answer || [], true))
                            }}
                          />
                        ) : (
                          <p className="text-sm text-gray-800 leading-relaxed">
                            {processFillBlankContent(question.content || '', question.answer || [], false)}
                          </p>
                        )}
                      </div>
                    ) : (
                      <div className="mb-4">
                        {isHtmlContent ? (
                          <div
                            className="text-sm text-gray-800 leading-relaxed"
                            dangerouslySetInnerHTML={{
                              __html: sanitizeHtml(question.content || '')
                            }}
                          />
                        ) : (
                          <p className="text-sm text-gray-800 leading-relaxed">
                            {stripHtml(question.content || '')}
                          </p>
                        )}
                      </div>
                    )}

                    {/* Question Type Specific Rendering */}
                    {(question.type === 'multiple_choice' || question.type === 'single_choice') && 
                     question.options && Array.isArray(question.options) && (
                      <div className="ml-5 space-y-2">
                        {question.options.map((option, optionIndex) => (
                          <div key={optionIndex} className="flex items-start space-x-3">
                            <span className="text-sm text-gray-800">(__)</span>
                            {isHtmlContent ? (
                              <div
                                className="flex-1 text-sm text-gray-800"
                                dangerouslySetInnerHTML={{
                                  __html: sanitizeHtml(option || '')
                                }}
                              />
                            ) : (
                              <span className="flex-1 text-sm text-gray-800">
                                {stripHtml(option || '')}
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    )}

                    {question.type === 'creative_writing' && (
                      <div className="border border-gray-300 h-32 mt-4 bg-gray-50"></div>
                    )}
                  </div>
                );
              } catch (error) {
                console.error(`Error rendering question ${index + 1}:`, error);
                return (
                  <div key={index} className="mb-6">
                    <p className="text-sm text-gray-500">
                      Error rendering question {index + 1}
                    </p>
                  </div>
                );
              }
            })
          )}
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error rendering exam preview:', error);
    return (
      <div className="max-w-4xl mx-auto bg-white p-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Error Generating Exam Preview</h1>
          <p className="text-gray-600">Please try again or contact support</p>
        </div>
      </div>
    );
  }
});

// Add display name for debugging
ExamPrintPreview.displayName = 'ExamPrintPreview';

export default ExamPrintPreview; 