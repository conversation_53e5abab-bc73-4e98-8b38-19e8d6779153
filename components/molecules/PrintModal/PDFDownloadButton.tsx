'use client';

import React, { useState, useRef, useCallback } from 'react';
import Image from 'next/image';
import { Download, Printer } from 'lucide-react';
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import { ExamHeader, PrintOptions } from './types';
import { sanitizeHtml } from '@/utils/sanitizeHtml';
import { cn } from '@/utils/cn';

interface PDFDownloadButtonProps {
  questions: Question[];
  examHeader: ExamHeader;
  printOptions: PrintOptions;
  fileName: string;
  isLoading: boolean;
  isFormValid?: boolean;
  className?: string;
  onValidationError?: () => void;
}

// Print-optimized component with CSS @media print styles
const PrintableExamComponent: React.FC<{
  questions: Question[];
  examHeader: ExamHeader;
  printOptions: PrintOptions;
}> = ({ questions, examHeader, printOptions }) => {
  
  // Helper function to process fill-blank content
  const processFillBlankContent = (content: string, answers: string[] = []) => {
    try {
      if (typeof content !== 'string') {
        return 'Invalid content';
      }
      
      const plainContent = content;
      const blankPatterns = /___|___\*|___\.|___,|___!|___\?|\[blank\]|\{blank\}|\(___\)/g;
      
      const matches = [];
      let match;
      const contentCopy = String(plainContent);

      while ((match = blankPatterns.exec(contentCopy)) !== null) {
        matches.push({
          pattern: match[0],
          index: match.index,
          length: match[0].length
        });
      }

      if (matches.length === 0 || !answers || answers.length === 0) {
        return plainContent.replace(blankPatterns, '________');
      }

      let result = '';
      let lastIndex = 0;
      let answerIndex = 0;

      for (const match of matches) {
        if (match.index > lastIndex) {
          result += contentCopy.substring(lastIndex, match.index);
        }

        const answer = answers[answerIndex] || '';
        const underscoreCount = Math.max(8, answer.length * 1.5);
        const underscores = '_'.repeat(underscoreCount);
        result += underscores;

        lastIndex = match.index + match.length;
        answerIndex++;

        if (answerIndex >= answers.length) break;
      }

      if (lastIndex < contentCopy.length) {
        result += contentCopy.substring(lastIndex);
      }

      return result;
    } catch (error) {
      console.error('Error in processFillBlankContent:', error);
      return content || 'Error processing content';
    }
  };

  return (
    <>
      {/* Print-specific CSS */}
      <style jsx>{`
        @media print {
          * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
          
          .print-container {
            width: 100% !important;
            max-width: none !important;
            margin: 0 !important;
            padding: 20px !important;
            font-family: 'Times New Roman', serif !important;
            font-size: 12pt !important;
            line-height: 1.4 !important;
            color: black !important;
            background: white !important;
          }
          
          .print-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid black;
            padding-bottom: 15px;
            page-break-inside: avoid;
          }
          
          .print-title {
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 8px;
          }
          
          .print-subtitle {
            font-size: 16pt;
            font-weight: bold;
            margin-bottom: 8px;
            text-transform: uppercase;
          }
          
          .print-subject {
            font-size: 14pt;
            margin-bottom: 12px;
          }
          
          .print-info {
            display: flex;
            justify-content: space-between;
            font-size: 10pt;
            max-width: 400px;
            margin: 0 auto;
          }
          
          .print-student-info {
            margin: 20px 0;
          }
          
          .print-student-row {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
          }
          
          .print-student-label {
            font-weight: bold;
            width: 60px;
          }
          
          .print-student-line {
            flex: 1;
            border-bottom: 1px solid black;
            height: 20px;
            margin-left: 12px;
          }
          
          .print-instructions {
            margin: 20px 0;
            border: 1px solid black;
            padding: 12px;
            page-break-inside: avoid;
          }
          
          .print-instructions-title {
            font-size: 10pt;
            font-weight: bold;
            margin-bottom: 8px;
            text-transform: uppercase;
          }
          
          .print-instructions-list {
            font-size: 9pt;
            line-height: 1.3;
          }
          
          .print-question {
            margin-bottom: 20px;
            page-break-inside: avoid;
          }
          
          .print-question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
          }
          
          .print-question-title {
            font-size: 11pt;
            font-weight: bold;
          }
          
          .print-question-marks {
            font-size: 10pt;
            font-style: italic;
          }
          
          .print-question-content {
            margin-bottom: 12px;
            font-size: 11pt;
            line-height: 1.5;
          }
          
          .print-options {
            margin-left: 15px;
          }
          
          .print-option {
            display: flex;
            align-items: flex-start;
            margin-bottom: 6px;
          }
          
          .print-option-bullet {
            margin-right: 8px;
            font-size: 10pt;
          }
          
          .print-option-content {
            flex: 1;
            font-size: 10pt;
          }
          
          .print-writing-box {
            border: 1px solid black;
            height: 80px;
            margin-top: 12px;
            background: #f9f9f9;
          }
          
          .print-logo {
            max-height: 50px;
            width: auto;
            margin-bottom: 10px;
          }
        }
        
        @media screen {
          .print-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1.4;
            color: black;
          }
        }
      `}</style>

      <div className="print-container">
        {/* Header Section */}
        <div className="print-header">
          {examHeader.logoUrl && (
            <Image
              src={examHeader.logoUrl}
              alt="School Logo"
              width={100}
              height={100}
              className="print-logo"
              style={{ display: 'block', margin: '0 auto 10px auto' }}
            />
          )}
          <h1 className="print-title">{examHeader.schoolName}</h1>
          <h2 className="print-subtitle">{examHeader.examTitle}</h2>
          <h3 className="print-subject">{examHeader.subject}</h3>

          <div className="print-info">
            <span>Date: {examHeader.examDate}</span>
            {examHeader.duration && <span>Duration: {examHeader.duration}</span>}
            {examHeader.gradeLevel && <span>Grade: {examHeader.gradeLevel}</span>}
          </div>
        </div>

        {/* Student Information */}
        <div className="print-student-info">
          <div className="print-student-row">
            <span className="print-student-label">Name:</span>
            <div className="print-student-line">
              {examHeader.studentName && (
                <span>{examHeader.studentName}</span>
              )}
            </div>
          </div>
          <div className="print-student-row">
            <span className="print-student-label">Class:</span>
            <div className="print-student-line">
              {examHeader.className && (
                <span>{examHeader.className}</span>
              )}
            </div>
          </div>
        </div>

        {/* Instructions */}
        {printOptions.includeInstructions && (
          <div className="print-instructions">
            <h4 className="print-instructions-title">Instructions to Candidates</h4>
            <div className="print-instructions-list">
              <p>1. This question paper consists of multiple pages.</p>
              <p>2. Answer all questions in the spaces provided.</p>
              <p>3. Follow all instructions carefully.</p>
              {printOptions.includeMarkAllocation && (
                <p>4. The marks for each question are shown in brackets [ ].</p>
              )}
            </div>
          </div>
        )}

        {/* Questions */}
        <div>
          {questions.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <p style={{ fontSize: '14pt', color: '#666' }}>
                No questions available. Please add questions to your worksheet.
              </p>
            </div>
          ) : (
            questions.map((question, index) => {
              try {
                return (
                  <div key={index} className="print-question">
                    <div className="print-question-header">
                      <h5 className="print-question-title">Question {index + 1}</h5>
                      {printOptions.includeMarkAllocation && (
                        <span className="print-question-marks">
                          [{question.type === 'multiple_choice' || question.type === 'single_choice' ? 1 : 2} marks]
                        </span>
                      )}
                    </div>

                    {/* Question Content */}
                    {question.type === 'fill_blank' ? (
                      <div className="print-question-content">
                        <div
                          dangerouslySetInnerHTML={{
                            __html: sanitizeHtml(processFillBlankContent(question.content || '', question.answer || []))
                          }}
                        />
                      </div>
                    ) : (
                      <div className="print-question-content">
                        <div
                          dangerouslySetInnerHTML={{
                            __html: sanitizeHtml(question.content || '')
                          }}
                        />
                      </div>
                    )}

                    {/* Question Options */}
                    {(question.type === 'multiple_choice' || question.type === 'single_choice') && 
                     question.options && Array.isArray(question.options) && (
                      <div className="print-options">
                        {question.options.map((option, optionIndex) => (
                          <div key={optionIndex} className="print-option">
                            <span className="print-option-bullet">(__)</span>
                            <div 
                              className="print-option-content"
                              dangerouslySetInnerHTML={{
                                __html: sanitizeHtml(option || '')
                              }}
                            />
                          </div>
                        ))}
                      </div>
                    )}

                    {question.type === 'creative_writing' && (
                      <div className="print-writing-box"></div>
                    )}
                  </div>
                );
              } catch (error) {
                console.error(`Error rendering question ${index + 1}:`, error);
                return (
                  <div key={index} className="print-question">
                    <p style={{ fontSize: '10pt', color: '#666' }}>
                      Error rendering question {index + 1}
                    </p>
                  </div>
                );
              }
            })
          )}
        </div>
      </div>
    </>
  );
};

const PDFDownloadButton: React.FC<PDFDownloadButtonProps> = ({
  questions,
  examHeader,
  printOptions,
  fileName,
  isLoading,
  isFormValid = true,
  className,
  onValidationError
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationError, setGenerationError] = useState<string | null>(null);

  const generateBrowserPDF = useCallback(() => {
    if (!isFormValid) {
      if (onValidationError) {
        onValidationError();
      }
      return;
    }

    try {
      setIsGenerating(true);
      setGenerationError(null);

      // Validate questions
      if (!questions || !Array.isArray(questions) || questions.length === 0) {
        throw new Error('No valid questions to export');
      }

      const validQuestions = questions.filter((question, index) => {
        if (!question || typeof question !== 'object') {
          console.warn(`Invalid question at index ${index}:`, question);
          return false;
        }
        return true;
      });

      if (validQuestions.length === 0) {
        throw new Error('No valid questions found after filtering');
      }

      // Create a new window with the printable content
      const printWindow = window.open('', '_blank', 'width=800,height=600');
      
      if (!printWindow) {
        throw new Error('Popup blocked. Please allow popups for this site.');
      }

      // Create the HTML document
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>${fileName}</title>
          <style>
            @media print {
              * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
              }
              
              body {
                margin: 0;
                padding: 20px;
                font-family: 'Times New Roman', serif;
                font-size: 12pt;
                line-height: 1.4;
                color: black;
                background: white;
              }
              
              .container {
                width: 100%;
                max-width: none;
              }
              
              .header {
                text-align: center;
                margin-bottom: 20px;
                border-bottom: 2px solid black;
                padding-bottom: 15px;
                page-break-inside: avoid;
              }
              
              .title { font-size: 18pt; font-weight: bold; margin-bottom: 8px; }
              .subtitle { font-size: 16pt; font-weight: bold; margin-bottom: 8px; text-transform: uppercase; }
              .subject { font-size: 14pt; margin-bottom: 12px; }
              .info { display: flex; justify-content: space-between; font-size: 10pt; max-width: 400px; margin: 0 auto; }
              
              .student-info { margin: 20px 0; }
              .student-row { display: flex; align-items: center; margin-bottom: 12px; }
              .student-label { font-weight: bold; width: 60px; }
              .student-line { flex: 1; border-bottom: 1px solid black; height: 20px; margin-left: 12px; }
              
              .instructions { margin: 20px 0; border: 1px solid black; padding: 12px; page-break-inside: avoid; }
              .instructions-title { font-size: 10pt; font-weight: bold; margin-bottom: 8px; text-transform: uppercase; }
              .instructions-list { font-size: 9pt; line-height: 1.3; }
              
              .question { margin-bottom: 20px; page-break-inside: avoid; }
              .question-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }
              .question-title { font-size: 11pt; font-weight: bold; }
              .question-marks { font-size: 10pt; font-style: italic; }
              .question-content { margin-bottom: 12px; font-size: 11pt; line-height: 1.5; }
              
              .options { margin-left: 15px; }
              .option { display: flex; align-items: flex-start; margin-bottom: 6px; }
              .option-bullet { margin-right: 8px; font-size: 10pt; }
              .option-content { flex: 1; font-size: 10pt; }
              
              .writing-box { border: 1px solid black; height: 80px; margin-top: 12px; background: #f9f9f9; }
              .logo { max-height: 50px; width: auto; margin-bottom: 10px; }
            }
            
            @media screen {
              body {
                margin: 20px;
                padding: 20px;
                font-family: 'Times New Roman', serif;
                font-size: 12pt;
                line-height: 1.4;
                color: black;
                background: #f5f5f5;
              }
              
              .container {
                max-width: 210mm;
                margin: 0 auto;
                padding: 20mm;
                background: white;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
              }
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              ${examHeader.logoUrl ? `<img src="${examHeader.logoUrl}" alt="School Logo" class="logo" style="display: block; margin: 0 auto 10px auto;">` : ''}
              <h1 class="title">${examHeader.schoolName}</h1>
              <h2 class="subtitle">${examHeader.examTitle}</h2>
              <h3 class="subject">${examHeader.subject}</h3>
              <div class="info">
                <span>Date: ${examHeader.examDate}</span>
                ${examHeader.duration ? `<span>Duration: ${examHeader.duration}</span>` : ''}
                ${examHeader.gradeLevel ? `<span>Grade: ${examHeader.gradeLevel}</span>` : ''}
              </div>
            </div>
            
            <div class="student-info">
              <div class="student-row">
                <span class="student-label">Name:</span>
                <div class="student-line">${examHeader.studentName || ''}</div>
              </div>
              <div class="student-row">
                <span class="student-label">Class:</span>
                <div class="student-line">${examHeader.className || ''}</div>
              </div>
            </div>
            
            ${printOptions.includeInstructions ? `
              <div class="instructions">
                <h4 class="instructions-title">Instructions to Candidates</h4>
                <div class="instructions-list">
                  <p>1. This question paper consists of multiple pages.</p>
                  <p>2. Answer all questions in the spaces provided.</p>
                  <p>3. Follow all instructions carefully.</p>
                  ${printOptions.includeMarkAllocation ? '<p>4. The marks for each question are shown in brackets [ ].</p>' : ''}
                </div>
              </div>
            ` : ''}
            
            <div>
              ${validQuestions.map((question, index) => {
                // Explicitly type content as string and answers as any[]
                const processFillBlankContent = (content: string, answers: any[] = []) => {
                  try {
                    if (typeof content !== 'string') return 'Invalid content';
                    const blankPatterns = /___|___\*|___\.|___,|___!|___\?|\\[blank\\]|\\{blank\\}|\\(___\\)/g;
                    if (!answers || answers.length === 0) {
                      return content.replace(blankPatterns, '________');
                    }
                    return content.replace(blankPatterns, () => '________');
                  } catch (error) {
                    return content || 'Error processing content';
                  }
                };

                return `
                  <div class="question">
                    <div class="question-header">
                      <h5 class="question-title">Question ${index + 1}</h5>
                      ${printOptions.includeMarkAllocation ? `
                        <span class="question-marks">
                          [${question.type === 'multiple_choice' || question.type === 'single_choice' ? 1 : 2} marks]
                        </span>
                      ` : ''}
                    </div>
                    
                    <div class="question-content">
                      ${question.type === 'fill_blank' 
                        ? processFillBlankContent(question.content || '', question.answer || [])
                        : (question.content || '')
                      }
                    </div>
                    
                    ${(question.type === 'multiple_choice' || question.type === 'single_choice') && question.options ? `
                      <div class="options">
                        ${question.options.map((option, optionIndex) => `
                          <div class="option">
                            <span class="option-bullet">(__)</span>
                            <div class="option-content">${option || ''}</div>
                          </div>
                        `).join('')}
                      </div>
                    ` : ''}
                    
                    ${question.type === 'creative_writing' ? '<div class="writing-box"></div>' : ''}
                  </div>
                `;
              }).join('')}
            </div>
          </div>
          
          <script>
            // Auto-print when page loads
            window.onload = function() {
              window.print();
              // Close window after printing (optional)
              window.onafterprint = function() {
                window.close();
              };
            };
          </script>
        </body>
        </html>
      `;

      // Write content to new window
      printWindow.document.write(htmlContent);
      printWindow.document.close();

    } catch (error) {
      console.error('PDF generation error:', error);
      setGenerationError(error instanceof Error ? error.message : 'PDF generation failed');
    } finally {
      setIsGenerating(false);
    }
  }, [questions, examHeader, printOptions, fileName, isFormValid, onValidationError]);

  // Error handling for validation errors
  if (generationError) {
    return (
      <div className={cn(
        "min-w-24 h-12 sm:h-10 bg-red-500 text-white rounded-md text-sm sm:text-base flex items-center justify-center gap-2 px-4 touch-manipulation",
        className
      )}>
        <span className="hidden sm:inline">Error: {generationError}</span>
        <span className="sm:hidden">Error</span>
      </div>
    );
  }

  // Check if questions array is empty or invalid
  if (!questions || !Array.isArray(questions) || questions.length === 0) {
    return (
      <div className={cn(
        "min-w-24 h-12 sm:h-10 bg-red-500 text-white rounded-md text-sm sm:text-base flex items-center justify-center gap-2 px-4 touch-manipulation",
        className
      )}>
        <span className="hidden sm:inline">No questions to export</span>
        <span className="sm:hidden">No questions</span>
      </div>
    );
  }

  // Validate questions structure
  const validQuestions = questions.filter((question, index) => {
    if (!question || typeof question !== 'object') {
      console.warn(`Invalid question at index ${index}:`, question);
      return false;
    }
    return true;
  });

  if (validQuestions.length === 0) {
    return (
      <div className={cn(
        "min-w-24 h-12 sm:h-10 bg-red-500 text-white rounded-md text-sm sm:text-base flex items-center justify-center gap-2 px-4 touch-manipulation",
        className
      )}>
        <span className="hidden sm:inline">Invalid questions data</span>
        <span className="sm:hidden">Invalid data</span>
      </div>
    );
  }

  return (
    <button
      onClick={generateBrowserPDF}
      disabled={isLoading || !isFormValid || isGenerating}
      className={cn(
        "min-w-28 sm:min-w-32 h-11 sm:h-12 bg-blue-600 hover:bg-blue-700 active:bg-blue-800 text-white transition-colors rounded-lg text-sm sm:text-base flex items-center justify-center gap-2 sm:gap-3 px-4 sm:px-6 touch-manipulation font-medium shadow-sm",
        (isLoading || !isFormValid || isGenerating) && "opacity-50 cursor-not-allowed",
        className
      )}
      aria-label="Generate PDF"
    >
      <Printer size={16} className="sm:w-[18px] sm:h-[18px]" />
      <span className="hidden sm:inline">
        {isGenerating ? "Opening Print..." : "Print to PDF"}
      </span>
      <span className="sm:hidden">
        {isGenerating ? "Opening..." : "Print PDF"}
      </span>
    </button>
  );
};

export default PDFDownloadButton;
