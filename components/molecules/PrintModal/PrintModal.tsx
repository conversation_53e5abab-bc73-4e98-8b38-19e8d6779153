'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Button } from '@/components/atoms/Button/Button';
import { Input } from '@/components/atoms/Input/Input';
import { Label } from '@/components/atoms/Label/Label';
import Icon from '@/components/atoms/Icon';
import { PrintFormData } from './types';
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import { cn } from '@/utils/cn';
import {
  X, Info, FileText, Calendar, Clock, Download,
  AlertTriangle, CheckCircle
} from 'lucide-react';

import PDFDownloadButton from './PDFDownloadButton';

interface PrintModalProps {
  isOpen: boolean;
  onClose: () => void;
  questions: Question[];
  worksheetInfo?: {
    topic?: string;
    grade?: string;
    language?: string;
    level?: string;
    totalQuestions?: number;
  };
  schoolInfo?: {
    name: string;
    address?: string;
    phoneNumber?: string;
    registeredNumber?: string;
    email?: string;
    logoUrl?: string;
  };
}

export const PrintModal: React.FC<PrintModalProps> = ({
  isOpen,
  onClose,
  questions,
  worksheetInfo,
  schoolInfo,
}) => {
  const [isLoading] = useState(false); // Keep isLoading state for PDFDownloadButton
  const [isClient, setIsClient] = useState(false);
  const [notification, setNotification] = useState<{
    message: string;
    type: 'error' | 'warning' | 'success' | 'info';
  } | null>(null);


  useEffect(() => {
    setIsClient(true);
  }, []);

  const [formData, setFormData] = useState<PrintFormData>({
    // Exam Details
    examTitle: '',
    subject: worksheetInfo?.topic || '',
    examDate: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
    duration: '',

    // Hidden fields (still used in print configuration)
    gradeLevel: worksheetInfo?.grade || '',
    studentName: '',
    className: '',
    includeMarkAllocation: true,
    includeInstructions: true,
    paperSize: 'A4',
    orientation: 'portrait',
  });

  const modalRef = useRef<HTMLDialogElement>(null);

  // Open or close the modal based on the isOpen prop
  React.useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.showModal();
      // Only prevent body scroll on mobile screens to avoid drawer conflicts
      const isMobile = window.innerWidth < 1024; // lg breakpoint
      if (isMobile) {
        document.body.style.overflow = 'hidden';
        // Add mobile-specific viewport handling
        const viewport = document.querySelector('meta[name=viewport]');
        if (viewport) {
          viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
        }
      }
    } else if (!isOpen && modalRef.current) {
      modalRef.current.close();
      // Restore body scroll
      document.body.style.overflow = '';
      // Restore viewport
      const viewport = document.querySelector('meta[name=viewport]');
      if (viewport) {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0');
      }
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = '';
      const viewport = document.querySelector('meta[name=viewport]');
      if (viewport) {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0');
      }
    };
  }, [isOpen]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData((prev) => ({ ...prev, [name]: checked }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  // Logo handling has been removed as school information now comes from session

  // Create exam header from form data and school info - memoized to prevent re-renders
  const examHeader = React.useMemo(() => {
    const header = {
      schoolName: schoolInfo?.name || '',
      gradeLevel: formData.gradeLevel,
      logoUrl: schoolInfo?.logoUrl || null, // Use original logo URL directly
      examTitle: formData.examTitle,
      subject: formData.subject,
      examDate: formData.examDate,
      duration: formData.duration,
      studentName: formData.studentName,
      className: formData.className,
    };

    return header;
  }, [formData, schoolInfo]);

  // Create print options from form data - memoized to prevent re-renders
  const printOptions = React.useMemo(() => {
    return {
      includeMarkAllocation: formData.includeMarkAllocation,
      includeInstructions: formData.includeInstructions,
      paperSize: formData.paperSize,
      orientation: formData.orientation,
    };
  }, [formData]);

  // Check if form is valid
  const isFormValid: boolean = !!(formData.examTitle && formData.subject);

  // Tooltip component - Enhanced for mobile
  const Tooltip = ({ text, children }: { text: string; children: React.ReactNode }) => {
    const [isVisible, setIsVisible] = useState(false);

    return (
      <div className="relative">
        <div
          className="cursor-help"
          onMouseEnter={() => setIsVisible(true)}
          onMouseLeave={() => setIsVisible(false)}
          onClick={() => setIsVisible(!isVisible)}
          onTouchStart={() => setIsVisible(!isVisible)}
        >
          {children}
        </div>
        {isVisible && (
          <div className="absolute left-0 bottom-full mb-3 z-20 pointer-events-none">
            <div className="bg-gray-900 text-white text-sm rounded-lg py-2.5 px-4 whitespace-nowrap shadow-lg max-w-xs">
              {text}
              <div className="absolute top-full left-4 w-2 h-2 bg-gray-900 transform rotate-45"></div>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Notification component
  const Notification = ({
    message,
    type = 'error',
    onClose
  }: {
    message: string;
    type?: 'error' | 'warning' | 'success' | 'info';
    onClose?: () => void;
  }) => {
    const [isVisible, setIsVisible] = useState(true);

    useEffect(() => {
      const timer = setTimeout(() => {
        setIsVisible(false);
        onClose?.();
      }, 5000);

      return () => clearTimeout(timer);
    }, [onClose]);

    if (!isVisible) return null;

    const iconMap = {
      error: <AlertTriangle className="h-5 w-5" />,
      warning: <AlertTriangle className="h-5 w-5" />,
      success: <CheckCircle className="h-5 w-5" />,
      info: <Info className="h-5 w-5" />
    };

    const colorMap = {
      error: 'bg-red-50 text-red-600 border-red-500',
      warning: 'bg-amber-50 text-amber-600 border-amber-500',
      success: 'bg-green-50 text-green-600 border-green-500',
      info: 'bg-section-bg-accent text-link-default border-blue-500'
    };

    return (
      <div className={`fixed top-4 left-4 right-4 sm:bottom-4 sm:right-4 sm:left-auto sm:top-auto z-50 max-w-md sm:max-w-sm mx-auto sm:mx-0`}
        style={{
          animation: 'slideInDown 0.3s ease-out forwards'
        }}
      >
        <style jsx>{`
          @keyframes slideInDown {
            from {
              transform: translateY(-20px);
              opacity: 0;
            }
            to {
              transform: translateY(0);
              opacity: 1;
            }
          }
          @media (min-width: 640px) {
            @keyframes slideInDown {
              from {
                transform: translateY(20px);
                opacity: 0;
              }
              to {
                transform: translateY(0);
                opacity: 1;
              }
            }
          }
        `}</style>
        <div className={`flex items-center p-4 sm:p-3 rounded-xl shadow-xl border-l-4 ${colorMap[type]} bg-white backdrop-blur-sm`}>
          <div className="mr-4 flex-shrink-0">
            {iconMap[type]}
          </div>
          <div className="flex-1 mr-3">
            <p className="text-base sm:text-sm font-medium leading-relaxed">{message}</p>
          </div>
          <button
            onClick={() => {
              setIsVisible(false);
              onClose?.();
            }}
            className="flex-shrink-0 ml-auto rounded-lg p-2.5 sm:p-1.5 inline-flex items-center justify-center h-11 w-11 sm:h-8 sm:w-8 hover:bg-gray-200 hover:bg-opacity-20 active:bg-gray-300 active:bg-opacity-30 transition-colors touch-manipulation"
          >
            <X className="h-5 w-5 sm:h-4 sm:w-4" />
          </button>
        </div>
      </div>
    );
  };

  return (
    <>
      {notification && (
        <Notification
          message={notification.message}
          type={notification.type}
          onClose={() => setNotification(null)}
        />
      )}
      <dialog
        id="print_modal"
        className="modal modal-bottom sm:modal-middle p-0! lg:left-[320px] lg:w-[calc(100vw-320px)]"
        ref={modalRef}
        aria-labelledby="print-modal-title"
      >
        <div className="modal-box w-screen! max-w-none! mx-0! left-0! right-0! sm:w-11/12! sm:max-w-lg! md:max-w-2xl! lg:max-w-4xl! sm:mx-auto! sm:left-auto! sm:right-auto! bg-white p-0 rounded-t-2xl sm:rounded-xl shadow-2xl overflow-hidden relative max-h-[90vh] sm:max-h-[85vh] flex flex-col">
          {/* Header with colored background - Fixed height for mobile */}
          <div className="bg-gray-800 text-white p-3 sm:p-6 flex justify-between items-center flex-shrink-0 min-h-[56px] sm:min-h-auto">
            <h2 id="print-modal-title" className="text-lg sm:text-xl font-bold flex items-center gap-2 sm:gap-3">
              <FileText size={18} className="sm:w-5 sm:h-5 flex-shrink-0" />
              <span className="truncate">Print Worksheet</span>
            </h2>
            <button
              onClick={onClose}
              className="rounded-full p-3 sm:p-1.5 bg-white/20 hover:bg-white/30 active:bg-white/40 transition-colors duration-200 touch-manipulation flex-shrink-0 min-w-[44px] min-h-[44px] sm:min-w-auto sm:min-h-auto flex items-center justify-center"
              aria-label="Close"
            >
              <X size={18} className="text-white sm:w-4 sm:h-4" />
            </button>
          </div>

          {/* Scrollable content area */}
          <div className="flex-1 overflow-y-auto p-3 sm:p-6">
            <p className="text-gray-600 mb-4 sm:mb-6 text-sm sm:text-base leading-relaxed">Configure your worksheet before printing</p>

            <div className="space-y-4 sm:space-y-6">
              {/* School information is now automatically retrieved from the user's session */}

              {/* Exam Details Section */}
              <div className="bg-gray-50 p-4 sm:p-6 rounded-xl border border-gray-100">
                <h4 className="font-semibold text-base sm:text-lg mb-4 sm:mb-4 text-gray-800 flex items-center">
                  <span className="bg-primary/10 text-primary p-2 sm:p-2.5 rounded-lg mr-3 sm:mr-4 flex items-center justify-center">
                    <Icon variant="file-text" size={5} className="w-4 h-4 sm:w-5 sm:h-5" />
                  </span>
                  <span className="text-base sm:text-lg font-semibold">Exam Details</span>
                </h4>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                  <div className="form-control w-full space-y-2 sm:space-y-3">
                    <Label className="text-gray-800 flex items-center text-sm sm:text-base font-medium">
                      <span className="font-medium">Exam Title</span> <span className="text-error font-semibold ml-1 sm:ml-2">*</span>
                      <Tooltip text="Enter the title of this assessment">
                        <Info size={16} className="ml-1 sm:ml-2 text-gray-400" />
                      </Tooltip>
                    </Label>
                    <Input
                      name="examTitle"
                      value={formData.examTitle}
                      onChange={handleInputChange}
                      placeholder="e.g., Mid-Term Assessment"
                      required
                      className="focus:outline-none focus:ring-2 focus:ring-blue-500 border border-gray-200 bg-white transition-all h-12 sm:h-12 px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base rounded-lg touch-manipulation"
                      aria-required="true"
                    />
                    {!formData.examTitle && (
                      <p className="text-xs sm:text-sm text-error mt-1 sm:mt-2 font-medium">This field is required</p>
                    )}
                  </div>

                  <div className="form-control w-full space-y-2 sm:space-y-3">
                    <Label className="text-gray-800 flex items-center text-sm sm:text-base font-medium">
                      <span className="font-medium">Subject</span> <span className="text-error font-semibold ml-1 sm:ml-2">*</span>
                      <Tooltip text="Enter the subject for this worksheet">
                        <Info size={16} className="ml-1 sm:ml-2 text-gray-400" />
                      </Tooltip>
                    </Label>
                    <Input
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      placeholder="e.g., Mathematics"
                      required
                      className="focus:outline-none focus:ring-2 focus:ring-blue-500 border border-gray-200 bg-white transition-all h-12 sm:h-12 px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base rounded-lg touch-manipulation"
                      aria-required="true"
                    />
                    {!formData.subject && (
                      <p className="text-xs sm:text-sm text-error mt-1 sm:mt-2 font-medium">This field is required</p>
                    )}
                  </div>

                  <div className="form-control w-full space-y-2 sm:space-y-3">
                    <Label className="text-gray-800 flex items-center text-sm sm:text-base font-medium">
                      <span className="font-medium">Exam Date</span>
                      <Tooltip text="Select the date when this exam will be administered">
                        <Info size={16} className="ml-1 sm:ml-2 text-gray-400" />
                      </Tooltip>
                    </Label>
                    <div className="relative">
                      <Input
                        type="date"
                        name="examDate"
                        value={formData.examDate}
                        onChange={handleInputChange}
                        className="focus:outline-none focus:ring-2 focus:ring-blue-500 border border-gray-200 bg-white pr-10 sm:pr-12 h-12 sm:h-12 px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base rounded-lg touch-manipulation"
                      />
                      <Calendar size={18} className="absolute right-3 sm:right-4 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none" />
                    </div>
                  </div>

                  <div className="form-control w-full space-y-2 sm:space-y-3">
                    <Label className="text-gray-800 flex items-center text-sm sm:text-base font-medium">
                      <span className="font-medium">Duration</span>
                      <Tooltip text="Specify how long students have to complete this assessment">
                        <Info size={16} className="ml-1 sm:ml-2 text-gray-400" />
                      </Tooltip>
                    </Label>
                    <div className="relative">
                      <Input
                        name="duration"
                        value={formData.duration}
                        onChange={handleInputChange}
                        placeholder="e.g., 1 h 45 min"
                        className="focus:outline-none focus:ring-2 focus:ring-blue-500 border border-gray-200 bg-white pr-10 sm:pr-12 h-12 sm:h-12 px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base rounded-lg touch-manipulation"
                      />
                      <Clock size={18} className="absolute right-3 sm:right-4 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>

          {/* Fixed bottom action area */}
          <div className="flex-shrink-0 border-t border-gray-200 bg-white p-3 sm:p-6">
            <div className="flex flex-col sm:flex-row justify-end gap-4 sm:gap-4">
              {isClient ? (
                <>
                  {questions && questions.length > 0 ? (
                    <PDFDownloadButton
                      questions={questions}
                      examHeader={examHeader}
                      printOptions={printOptions}
                      fileName={`${formData.subject.replace(/\s+/g, '-').toLowerCase()}-worksheet.pdf`}
                      isLoading={isLoading}
                      isFormValid={isFormValid}
                      onValidationError={() => {
                        setNotification({
                          message: 'Please fill in all required fields before downloading the PDF.',
                          type: 'error'
                        });
                      }}
                    />
                  ) : (
                    <div className="min-w-28 sm:min-w-32 h-11 sm:h-12 bg-gray-400 text-white rounded-lg text-sm sm:text-base flex items-center justify-center gap-2 sm:gap-3 px-4 sm:px-6 opacity-50 cursor-not-allowed touch-manipulation font-medium">
                      <Download size={16} className="sm:w-[18px] sm:h-[18px]" />
                      <span className="hidden sm:inline">No questions to export</span>
                      <span className="sm:hidden">No questions</span>
                    </div>
                  )}
                </>
              ) : null}
            </div>
          </div>
        </div>

        <form method="dialog" className="modal-backdrop">
          <button onClick={onClose} aria-label="Close modal background">close</button>
        </form>
      </dialog>
    </>
  );
};
