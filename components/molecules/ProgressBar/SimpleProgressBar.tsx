import React from 'react';
import { cn } from '@/utils/cn';

export interface SimpleProgressBarProps {
  value: number; // 0-100
  label?: string;
  className?: string;
}

const SimpleProgressBar: React.FC<SimpleProgressBarProps> = ({ value, label, className }) => {
  const safeValue = Math.max(0, Math.min(100, value));
  return (
    <div className={cn('w-full', className)}>
      {label && (
        <div className="mb-1 text-xs font-medium text-gray-700 flex justify-between">
          <span>{label}</span>
          <span>{safeValue}%</span>
        </div>
      )}
      <div
        className="relative w-full h-2 bg-gray-200 rounded-full overflow-hidden"
        role="progressbar"
        aria-valuenow={safeValue}
        aria-valuemin={0}
        aria-valuemax={100}
      >
        <div
          className="absolute top-0 left-0 h-full bg-primary transition-all duration-500 rounded-full"
          style={{ width: `${safeValue}%` }}
        ></div>
      </div>
    </div>
  );
};

export default SimpleProgressBar; 