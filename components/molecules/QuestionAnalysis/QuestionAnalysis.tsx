import React, { useState } from 'react';
import { 
  CheckCircle2, 
  XCircle, 
  ChevronDown, 
  ChevronRight, 
  ChevronUp,
  Info 
} from 'lucide-react';
import { MathContentRenderer } from '@/components/molecules/MathContentRenderer/MathContentRenderer';
import { cn } from '@/utils/cn';
import { IQuestionDetail } from '../DetailedStudentResult/types';

interface QuestionAnalysisProps {
  questions: IQuestionDetail[];
  className?: string;
}

export const QuestionAnalysis: React.FC<QuestionAnalysisProps> = ({ questions, className }) => {
  const [expandedQuestions, setExpandedQuestions] = useState<Set<number>>(new Set());
  const [expandedExplanations, setExpandedExplanations] = useState<Set<number>>(new Set());

  const toggleQuestionExpansion = (questionIndex: number) => {
    const newExpanded = new Set(expandedQuestions);
    if (newExpanded.has(questionIndex)) {
      newExpanded.delete(questionIndex);
    } else {
      newExpanded.add(questionIndex);
    }
    setExpandedQuestions(newExpanded);
  };

  const toggleExplanationExpansion = (questionIndex: number) => {
    const newExpanded = new Set(expandedExplanations);
    if (newExpanded.has(questionIndex)) {
      newExpanded.delete(questionIndex);
    } else {
      newExpanded.add(questionIndex);
    }
    setExpandedExplanations(newExpanded);
  };

  const isExplanationLong = (explanation: string) => explanation.length > 200;

  const getQuestionTypeLabel = (type: string) => {
    switch (type) {
      case 'single_choice':
        return 'Single Choice';
      case 'multiple_choice':
        return 'Multiple Choice';
      case 'fill_blank':
        return 'Fill in the Blank';
      case 'creative_writing':
        return 'Creative Writing';
      default:
        return type;
    }
  };

  const renderAnswer = (answer: string[]) => {
    if (!answer || answer.length === 0) {
      return <span className="text-gray-400 italic">No answer provided</span>;
    }
    const answerText = answer.join(', ');
    return (
      <MathContentRenderer
        content={answerText}
        className="prose prose-sm max-w-none"
        fallbackToText={true}
      />
    );
  };

  return (
    <div className={cn("space-y-3 md:space-y-4", className)}>
      {questions.map((question, index) => {
        const isExpanded = expandedQuestions.has(index);

        return (
          <div
            key={index}
            className={cn(
              'border rounded-lg transition-all duration-200',
              question.isCorrect
                ? 'border-green-200 bg-green-50/50'
                : 'border-red-200 bg-red-50/50'
            )}
          >
            {/* Question Header */}
            <div 
              className="p-3 md:p-4 cursor-pointer hover:bg-background-subtle/50 transition-all duration-200"
              onClick={() => toggleQuestionExpansion(index)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 md:gap-3 flex-1 min-w-0">
                  {/* Status Icon */}
                  <div className={cn(
                    'flex items-center justify-center rounded-full flex-shrink-0',
                    'w-6 h-6 md:w-8 md:h-8',
                    question.isCorrect
                      ? 'bg-green-100 text-green-600'
                      : 'bg-red-100 text-red-600'
                  )}>
                    {question.isCorrect ? (
                      <CheckCircle2 className="w-3 md:w-5 h-3 md:h-5" />
                    ) : (
                      <XCircle className="w-3 md:w-5 h-3 md:h-5" />
                    )}
                  </div>

                  {/* Question Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-1 md:gap-2 mb-1 flex-wrap">
                      <h3 className="font-semibold text-text-primary text-sm md:text-base">
                        Question {index + 1}
                      </h3>
                      <span className={cn(
                        'px-2 py-0.5 md:py-1 rounded-full text-xs font-medium flex-shrink-0',
                        question.isCorrect
                          ? 'bg-green-100 text-green-700'
                          : 'bg-red-100 text-red-700'
                      )}>
                        {question.isCorrect ? 'Correct' : 'Incorrect'}
                      </span>
                      <span className="text-xs px-2 py-0.5 md:py-1 bg-section-bg-neutral-alt text-text-secondary rounded-full font-medium flex-shrink-0">
                        {getQuestionTypeLabel(question.type)}
                      </span>
                    </div>
                    <div className="text-xs md:text-sm text-text-secondary line-clamp-2">
                      <MathContentRenderer
                        content={question.content}
                        className="prose prose-sm max-w-none"
                        fallbackToText={true}
                      />
                    </div>
                  </div>
                </div>

                {/* Expand/Collapse Icon */}
                <div className="ml-2 md:ml-4 flex-shrink-0">
                  {isExpanded ? (
                    <ChevronDown className="w-4 md:w-5 h-4 md:h-5 text-text-secondary" />
                  ) : (
                    <ChevronRight className="w-4 md:w-5 h-4 md:h-5 text-text-secondary" />
                  )}
                </div>
              </div>
            </div>

            {/* Expanded Question Details */}
            {isExpanded && (
              <div className="px-3 md:px-4 pb-3 md:pb-4 border-t border-gray-200 bg-background-default">
                <div className="pt-3 md:pt-4 space-y-3 md:space-y-4">
                  {/* Question Content */}
                  <div>
                    <h4 className="font-medium text-text-primary mb-2 text-sm md:text-base">Question:</h4>
                    <div className="p-2 md:p-3 bg-section-bg-neutral-alt rounded-lg text-text-primary">
                      <MathContentRenderer
                        content={question.content}
                        className="prose prose-sm max-w-none"
                        fallbackToText={true}
                      />
                    </div>
                  </div>

                  {/* Student's Answer */}
                  <div>
                    <h4 className="font-medium text-text-primary mb-2 text-sm md:text-base">Student&apos;s Answer:</h4>
                    <div className={cn(
                      'p-2 md:p-3 rounded-lg border',
                      question.isCorrect
                        ? 'bg-green-50 border-green-200 text-green-800'
                        : 'bg-red-50 border-red-200 text-red-800'
                    )}>
                      {renderAnswer(question.studentAnswer)}
                    </div>
                  </div>

                  {/* Correct Answer */}
                  <div>
                    <h4 className="font-medium text-text-primary mb-2 text-sm md:text-base">Correct Answer:</h4>
                    <div className="p-2 md:p-3 rounded-lg border bg-green-50 border-green-200 text-green-800">
                      {renderAnswer(question.correctAnswer)}
                    </div>
                  </div>

                  {/* Explanation (if available) */}
                  {question.explanation && (
                    <div>
                      <div className="flex items-center justify-between mb-2 md:mb-3">
                        <div className="flex items-center gap-2">
                          <div className="flex items-center justify-center w-5 md:w-6 h-5 md:h-6 rounded-full bg-blue-100 text-blue-600 flex-shrink-0">
                            <Info className="w-3 md:w-4 h-3 md:h-4" />
                          </div>
                          <h4 className="font-semibold text-text-primary text-xs md:text-sm">Explanation</h4>
                        </div>
                        {isExplanationLong(question.explanation) && (
                          <button
                            onClick={() => toggleExplanationExpansion(index)}
                            className="flex items-center gap-1 px-2 py-1 text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors"
                          >
                            {expandedExplanations.has(index) ? (
                              <>
                                <span>Show less</span>
                                <ChevronUp className="w-3 h-3" />
                              </>
                            ) : (
                              <>
                                <span>Show more</span>
                                <ChevronDown className="w-3 h-3" />
                              </>
                            )}
                          </button>
                        )}
                      </div>
                      <div className="relative">
                        <div className="absolute left-0 top-0 bottom-0 w-0.5 md:w-1 bg-gradient-to-b from-blue-400 to-blue-600 rounded-full"></div>
                        <div className="pl-3 md:pl-6 pr-2 md:pr-4 py-3 md:py-4 bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200/60 rounded-xl shadow-sm">
                          <div className={cn(
                            "prose prose-sm max-w-none text-blue-900/90 leading-relaxed transition-all duration-300",
                            isExplanationLong(question.explanation) && !expandedExplanations.has(index) && "max-h-20 md:max-h-24 overflow-hidden relative"
                          )}>
                            <MathContentRenderer
                              content={question.explanation}
                              className="prose prose-sm max-w-none [&_p]:mb-2 [&_p:last-child]:mb-0"
                              fallbackToText={true}
                            />
                            {isExplanationLong(question.explanation) && !expandedExplanations.has(index) && (
                              <div className="absolute bottom-0 left-0 right-0 h-6 md:h-8 bg-gradient-to-t from-blue-50 to-transparent pointer-events-none"></div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}; 