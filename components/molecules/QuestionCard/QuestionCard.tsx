'use client';

import React from 'react';
import { UseFormRegister, Control, UseFormWatch, UseFormSetValue, FieldError } from 'react-hook-form';
import { CheckCircle, Circle, Clock, Award } from 'lucide-react';
import { cn } from '@/utils/cn';

import { ExamQuestionRenderer } from '@/components/molecules/ExamQuestionInputs';
import { MathContentRenderer } from '@/components/molecules/MathContentRenderer';
import { IExamQuestion } from '@/types/exam.types';

// Define the form data structure for exam answers
export interface ExamFormData {
  answers: Array<{
    questionIndex: number;
    userAnswer: string[];
  }>;
}

export interface QuestionCardProps {
  question: IExamQuestion;
  questionIndex: number;
  totalQuestions: number;
  isActive?: boolean;
  currentAnswer?: string[];
  register: UseFormRegister<ExamFormData>;
  control: Control<ExamFormData>;
  watch: UseFormWatch<ExamFormData>;
  setValue: UseFormSetValue<ExamFormData>;
  error?: FieldError;
  onNext?: () => void;
  onPrevious?: () => void;
  showNavigation?: boolean;
  timeSpent?: number;
  points?: number;
  className?: string;
}

const getQuestionTypeLabel = (type: string): string => {
  switch (type) {
    case 'single_choice':
      return 'Single Choice';
    case 'multiple_choice':
      return 'Multiple Choice';
    case 'fill_blank':
      return 'Fill in the Blank';
    case 'creative_writing':
      return 'Creative Writing';
    default:
      return 'Question';
  }
};

const getQuestionTypeColor = (type: string): string => {
  switch (type) {
    case 'single_choice':
      return 'bg-blue-100 text-blue-800';
    case 'multiple_choice':
      return 'bg-green-100 text-green-800';
    case 'fill_blank':
      return 'bg-purple-100 text-purple-800';
    case 'creative_writing':
      return 'bg-orange-100 text-orange-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const QuestionCard: React.FC<QuestionCardProps> = ({
  question,
  questionIndex,
  totalQuestions,
  isActive = true,
  currentAnswer = [],
  register,
  control,
  watch,
  setValue,
  error,
  onNext,
  onPrevious,
  showNavigation = true,
  timeSpent,
  points,
  className,
}) => {
  // Check if question is answered
  const isAnswered = currentAnswer && currentAnswer.length > 0 && currentAnswer.some(ans => ans.trim() !== '');

  return (
    <div
      className={cn(
        'bg-background-default rounded-xl shadow-lg border border-gray-200 overflow-hidden',
        'transition-all duration-300 ease-out exam-card-optimized',
        'animate-[questionCardEntrance_0.5s_ease-out]',
        isActive && 'ring-2 ring-primary-action ring-opacity-20',
        'hover:shadow-xl hover:transform hover:scale-[1.01] hover:-translate-y-1',
        className
      )}
    >
      {/* Card Header */}
      <div className="bg-section-bg-accent border-b border-blue-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Question Number */}
            <div className="flex items-center gap-2">
              <div className="flex items-center justify-center w-8 h-8 bg-primary-action text-background-default rounded-full font-semibold text-sm">
                {questionIndex + 1}
              </div>
              <span className="text-text-secondary text-sm font-medium">
                of {totalQuestions}
              </span>
            </div>

            {/* Question Type Badge */}
            <span
              className={cn(
                'px-3 py-1 rounded-full text-xs font-medium',
                getQuestionTypeColor(question.type)
              )}
            >
              {getQuestionTypeLabel(question.type)}
            </span>

            {/* Answer Status */}
            <div className="flex items-center gap-1">
              {isAnswered ? (
                <CheckCircle className="w-4 h-4 text-green-500" />
              ) : (
                <Circle className="w-4 h-4 text-gray-400" />
              )}
              <span
                className={cn(
                  'text-xs font-medium',
                  isAnswered ? 'text-green-600' : 'text-gray-500'
                )}
              >
                {isAnswered ? 'Answered' : 'Not Answered'}
              </span>
            </div>
          </div>

          {/* Metadata */}
          <div className="flex items-center gap-4 text-sm text-text-secondary">
            {/* Points Display */}
            {points && (
              <div className="flex items-center gap-1">
                <Award className="w-4 h-4" />
                <span>{points} pts</span>
              </div>
            )}

            {/* Time Spent */}
            {timeSpent && (
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>{Math.floor(timeSpent / 60)}:{(timeSpent % 60).toString().padStart(2, '0')}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Card Content */}
      <div className="p-6">
        {/* Question Content */}
        <div className="mb-6">
          <h3 className="text-xl font-semibold text-text-primary mb-4 leading-relaxed">
            Question {questionIndex + 1}
          </h3>
          
          {/* Enhanced Question Content Display with MathML Support */}
          <MathContentRenderer
            content={question.content}
            className="text-text-primary text-base leading-relaxed mb-6"
            fallbackToText={true}
            onRenderError={(error) => {
              console.warn('Math rendering error in question:', questionIndex + 1, error);
            }}
          />
        </div>

        {/* Question Input Component */}
        <div className="mb-6">
          <ExamQuestionRenderer
            question={question}
            questionIndex={questionIndex}
            register={register}
            control={control}
            watch={watch}
            setValue={setValue}
            error={error}
            className="border-0 bg-transparent p-0 shadow-none"
          />
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600 font-medium">
              {error.message || 'Please provide an answer for this question'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};