'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { Search, Users, Loader2, AlertCircle, RefreshCw, Wifi, WifiOff } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { Input } from '@/components/atoms/Input/Input';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { TableSkeleton } from '@/components/molecules/CustomTable/TableSkeleton';
import { assignExamAction, getUnassignedStudentsAction } from '@/actions/exam.action';
import { UnassignedStudent } from '@/types/student';
import { useToast } from '@/providers/ToastProvider';
import { cn } from '@/utils/cn';

export interface StudentSelectionTableProps {
  examId: string;
  onSuccess: () => void;
  onCancel: () => void;
}

const StudentSelectionTableComponent: React.FC<StudentSelectionTableProps> = ({
  examId,
  onSuccess,
  onCancel,
}) => {
  const { data: session } = useSession();
  const { showSuccess, showError } = useToast();
  const [students, setStudents] = useState<UnassignedStudent[]>([]);
  const [selectedStudentIds, setSelectedStudentIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isAssigning, setIsAssigning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const [networkError, setNetworkError] = useState(false);
  const studentsPerPage = 10;
  const maxRetries = 3;

  // Enhanced fetch function with retry logic and better error handling
  const fetchStudents = useCallback(async (isRetry = false) => {
    if (!examId) {
      setError('Exam ID is required to fetch unassigned students.');
      setIsLoading(false);
      return;
    }

    try {
      if (isRetry) {
        setIsRetrying(true);
      } else {
        setIsLoading(true);
      }
      setError(null);
      setNetworkError(false);

      // Get unassigned students for this specific exam
      // The server action will handle school filtering based on user role
      const response = await getUnassignedStudentsAction(examId, session?.user?.schoolId || undefined);

      if (response.status === 'success') {
        setStudents(response.data || []);
        setRetryCount(0); // Reset retry count on success
      } else {
        const errorMessage = typeof response.message === 'string' ? response.message : 'Failed to fetch unassigned students';

        // Check if it's a network/server error that might benefit from retry
        if (response.message && typeof response.message === 'string' &&
            (response.message.includes('network') || response.message.includes('timeout') || response.message.includes('500'))) {
          setNetworkError(true);
        }

        setError(errorMessage);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred while fetching unassigned students';

      // Detect network errors
      if (err.name === 'NetworkError' || err.message?.includes('fetch') || err.message?.includes('network')) {
        setNetworkError(true);
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
      setIsRetrying(false);
    }
  }, [examId, session?.user?.schoolId]);

  // Auto-retry logic
  const handleRetry = useCallback(async () => {
    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1);
      await fetchStudents(true);
    }
  }, [fetchStudents, retryCount, maxRetries]);

  // Fetch unassigned students for the exam on component mount
  useEffect(() => {
    fetchStudents();
  }, [fetchStudents]);

  // Debounce search term for better performance
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Filter students based on debounced search term for better performance
  const filteredStudents = useMemo(() => {
    if (!debouncedSearchTerm) return students;

    const searchLower = debouncedSearchTerm.toLowerCase();
    return students.filter(student =>
      student.name.toLowerCase().includes(searchLower) ||
      student.email.toLowerCase().includes(searchLower)
    );
  }, [students, debouncedSearchTerm]);

  // Pagination with memoization
  const paginationData = useMemo(() => {
    const totalPages = Math.ceil(filteredStudents.length / studentsPerPage);
    const startIndex = (currentPage - 1) * studentsPerPage;
    const paginatedStudents = filteredStudents.slice(startIndex, startIndex + studentsPerPage);

    return {
      totalPages,
      startIndex,
      paginatedStudents
    };
  }, [filteredStudents, currentPage, studentsPerPage]);

  const { totalPages, paginatedStudents } = paginationData;

  // Handle student selection with useCallback for performance
  const handleStudentToggle = useCallback((studentId: string) => {
    setSelectedStudentIds(prev =>
      prev.includes(studentId)
        ? prev.filter(id => id !== studentId)
        : [...prev, studentId]
    );
  }, []);

  const handleSelectAll = useCallback(() => {
    const currentPageIds = paginatedStudents.map(student => student.id);
    const allCurrentSelected = currentPageIds.every(id => selectedStudentIds.includes(id));

    if (allCurrentSelected) {
      // Deselect all on current page
      setSelectedStudentIds(prev => prev.filter(id => !currentPageIds.includes(id)));
    } else {
      // Select all on current page
      setSelectedStudentIds(prev => {
        const newIds = currentPageIds.filter(id => !prev.includes(id));
        return [...prev, ...newIds];
      });
    }
  }, [paginatedStudents, selectedStudentIds]);

  // Handle assignment with enhanced error handling
  const handleAssignExam = async () => {
    if (selectedStudentIds.length === 0) {
      setError('Please select at least one student to assign the exam.');
      return;
    }

    try {
      setIsAssigning(true);
      setError(null);

      const response = await assignExamAction(examId, selectedStudentIds);

      if (response.status === 'success') {
        showSuccess(
          'Exam Assigned Successfully',
          `The exam has been assigned to ${selectedStudentIds.length} student${selectedStudentIds.length === 1 ? '' : 's'}.`
        );

        // Clear selections and refresh the list
        setSelectedStudentIds([]);
        await fetchStudents(); // Refresh to show updated unassigned students

        onSuccess();
      } else {
        let errorMessage = 'Failed to assign exam';

        if (typeof response.message === 'string') {
          errorMessage = response.message;
        } else if (Array.isArray(response.message)) {
          errorMessage = response.message.join(', ');
        }

        // Check for specific error types
        if (errorMessage.includes('already assigned')) {
          errorMessage = 'Some students are already assigned to this exam. Please refresh and try again.';
          await fetchStudents(); // Refresh to show current state
        } else if (errorMessage.includes('permission') || errorMessage.includes('unauthorized')) {
          errorMessage = 'You do not have permission to assign this exam. Please contact your administrator.';
        }

        setError(errorMessage);
        showError('Assignment Failed', errorMessage);
      }
    } catch (err: any) {
      let errorMessage = 'An unexpected error occurred while assigning the exam';

      // Handle network errors
      if (err.name === 'NetworkError' || err.message?.includes('fetch') || err.message?.includes('network')) {
        errorMessage = 'Network error occurred. Please check your connection and try again.';
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      showError('Assignment Failed', errorMessage);
    } finally {
      setIsAssigning(false);
    }
  };

  // Reset page when debounced search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchTerm]);

  // Memoize selection state calculations for performance
  const selectionState = useMemo(() => {
    const isAllCurrentPageSelected = paginatedStudents.length > 0 &&
      paginatedStudents.every(student => selectedStudentIds.includes(student.id));

    const selectedCount = selectedStudentIds.length;
    const currentPageSelectedCount = paginatedStudents.filter(student =>
      selectedStudentIds.includes(student.id)
    ).length;

    return {
      isAllCurrentPageSelected,
      selectedCount,
      currentPageSelectedCount
    };
  }, [paginatedStudents, selectedStudentIds]);

  const { isAllCurrentPageSelected, selectedCount } = selectionState;

  return (
    <div className="flex flex-col h-full" role="main" aria-label="Student selection interface">
      {/* Search and Info */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Users size={20} className="text-primary-action" aria-hidden="true" />
            <h3 id="student-selection-title" className="text-lg font-medium text-text-primary">Select Students</h3>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              onClick={() => fetchStudents()}
              disabled={isLoading || isRetrying}
              className="h-8 w-8 p-0"
              title="Refresh student list"
              aria-label="Refresh student list"
            >
              <RefreshCw
                size={16}
                className={cn(
                  "text-gray-500",
                  (isLoading || isRetrying) && "animate-spin"
                )}
                aria-hidden="true"
              />
            </Button>
            <div className="text-sm text-text-secondary" aria-live="polite">
              {selectedCount} selected
            </div>
          </div>
        </div>

        {/* Search Input */}
        <div className="relative">
          <label htmlFor="student-search" className="sr-only">
            Search students by name or email
          </label>
          <Search
            size={18}
            className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
            aria-hidden="true"
          />
          <Input
            id="student-search"
            type="text"
            placeholder="Search students by name or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
            aria-describedby="search-help"
          />
          <div id="search-help" className="sr-only">
            Search will filter students as you type. Results update automatically.
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden" role="region" aria-labelledby="student-selection-title">
        {error && (
          <div className="p-4" role="alert" aria-live="assertive">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  {networkError ? (
                    <WifiOff className="h-5 w-5 text-red-400" aria-hidden="true" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-400" aria-hidden="true" />
                  )}
                </div>
                <div className="ml-3 flex-1">
                  <h3 className="text-sm font-medium text-red-800">
                    {networkError ? 'Connection Error' : 'Error Loading Students'}
                  </h3>
                  <p className="mt-1 text-sm text-red-700">{error}</p>

                  {networkError && retryCount < maxRetries && (
                    <div className="mt-3">
                      <Button
                        onClick={handleRetry}
                        disabled={isRetrying}
                        variant="outline"
                        className="text-red-700 border-red-300 hover:bg-red-50"
                      >
                        {isRetrying ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Retrying...
                          </>
                        ) : (
                          <>
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Retry ({retryCount}/{maxRetries})
                          </>
                        )}
                      </Button>
                    </div>
                  )}

                  {retryCount >= maxRetries && networkError && (
                    <div className="mt-3 text-sm text-red-600">
                      <p>Maximum retry attempts reached. Please check your connection and try again later.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {isLoading || isRetrying ? (
          <div className="p-4" role="status" aria-live="polite">
            <div className="flex items-center justify-center mb-4">
              <Loader2 className="w-5 h-5 animate-spin text-primary-action mr-2" aria-hidden="true" />
              <span className="text-sm text-text-secondary">
                {isRetrying ? `Retrying... (${retryCount}/${maxRetries})` : 'Loading unassigned students...'}
              </span>
            </div>
            <TableSkeleton rows={5} columns={3} showMobileCards={false} />
            <span className="sr-only">Loading student data, please wait</span>
          </div>
        ) : filteredStudents.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center p-8">
            <AlertCircle size={48} className="text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-text-primary mb-2">
              {debouncedSearchTerm ? 'No students found' : 'No unassigned students'}
            </h3>
            <p className="text-text-secondary">
              {debouncedSearchTerm
                ? 'Try adjusting your search terms'
                : 'All students have already been assigned to this exam'
              }
            </p>
          </div>
        ) : (
          <div className="flex flex-col h-full">
            {/* Table Header */}
            <div className="px-4 py-3 border-b border-gray-200 bg-gray-50" role="row">
              <div className="flex items-center">
                <div className="w-10">
                  <input
                    type="checkbox"
                    checked={isAllCurrentPageSelected}
                    onChange={handleSelectAll}
                    className="w-4 h-4 text-primary-action bg-gray-100 border-gray-300 rounded focus:ring-primary-action focus:ring-2"
                    aria-label={`Select all students on current page (${paginatedStudents.length} students)`}
                  />
                </div>
                <div className="flex-1 text-xs font-semibold text-gray-600 uppercase tracking-wider" role="columnheader">
                  Student
                </div>
                <div className="w-32 text-xs font-semibold text-gray-600 uppercase tracking-wider" role="columnheader">
                  Email
                </div>
              </div>
            </div>

            {/* Table Body */}
            <div className="flex-1 overflow-y-auto" role="grid" aria-label="Students available for assignment">
              {paginatedStudents.map((student, index) => (
                <div
                  key={student.id}
                  className={cn(
                    'flex items-center px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors focus-within:ring-2 focus-within:ring-primary-action focus-within:ring-offset-2',
                    selectedStudentIds.includes(student.id) && 'bg-blue-50'
                  )}
                  role="row"
                  aria-rowindex={index + 1}
                  onClick={() => handleStudentToggle(student.id)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleStudentToggle(student.id);
                    }
                  }}
                  tabIndex={0}
                  aria-label={`${student.name}, ${student.email}. ${selectedStudentIds.includes(student.id) ? 'Selected' : 'Not selected'}`}
                >
                  <div className="w-10" role="gridcell">
                    <input
                      type="checkbox"
                      checked={selectedStudentIds.includes(student.id)}
                      onChange={() => handleStudentToggle(student.id)}
                      className="w-4 h-4 text-primary-action bg-gray-100 border-gray-300 rounded focus:ring-primary-action focus:ring-2"
                      aria-label={`Select ${student.name}`}
                      tabIndex={-1}
                    />
                  </div>
                  <div className="flex-1" role="gridcell">
                    <div className="text-sm font-medium text-text-primary">
                      {student.name}
                    </div>
                  </div>
                  <div className="w-32 text-sm text-text-secondary truncate" role="gridcell">
                    {student.email}
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-text-secondary">
                    Showing {startIndex + 1} to {Math.min(startIndex + studentsPerPage, filteredStudents.length)} of {filteredStudents.length} students
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-1 text-sm"
                    >
                      Previous
                    </Button>
                    <span className="text-sm text-text-secondary">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 text-sm"
                    >
                      Next
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Footer Actions */}
      <div className="p-4 border-t border-gray-200 bg-gray-50" role="region" aria-label="Assignment actions">
        <div className="flex items-center justify-between">
          <div className="text-sm text-text-secondary" aria-live="polite" aria-atomic="true">
            {selectedStudentIds.length > 0 && (
              `${selectedStudentIds.length} student${selectedStudentIds.length === 1 ? '' : 's'} selected`
            )}
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={onCancel}
              disabled={isAssigning}
              aria-label="Cancel student assignment"
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleAssignExam}
              disabled={selectedStudentIds.length === 0 || isAssigning}
              aria-label={
                isAssigning
                  ? 'Assigning exam to students, please wait'
                  : selectedStudentIds.length === 0
                    ? 'Select students to assign exam'
                    : `Assign exam to ${selectedStudentIds.length} selected student${selectedStudentIds.length === 1 ? '' : 's'}`
              }
            >
              {isAssigning ? (
                <>
                  <Loader2 size={16} className="animate-spin mr-2" aria-hidden="true" />
                  Assigning...
                </>
              ) : (
                `Assign to ${selectedStudentIds.length} Student${selectedStudentIds.length === 1 ? '' : 's'}`
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Export memoized component with display name
export const StudentSelectionTable = React.memo(StudentSelectionTableComponent);
StudentSelectionTable.displayName = 'StudentSelectionTable';
