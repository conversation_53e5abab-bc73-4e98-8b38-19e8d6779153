'use client';

import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import { LoadingWorksheetScreen } from '@/components/molecules/LoadingWorksheetScreen/LoadingWorksheetScreen';
import QuestionListingView from '@/components/molecules/QuestionListingView/QuestionListingView';
import { Button } from '@/components/atoms/Button/Button';
import Icon from '@/components/atoms/Icon';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { WorksheetGeneratingStatus } from '@/apis/worksheet';
import { ProgressData } from '@/components/molecules/ProgressBar/ProgressBar';
import { useWorksheetProgress } from '@/hooks/useWorksheetProgress';
import { useState, useRef } from 'react';
import { CreateExamModal } from '@/components/molecules/CreateExamModal/CreateExamModal';
import { PrintModal } from '@/components/molecules/PrintModal';
import { QuestionReorderModal } from '@/components/molecules/QuestionReorderModal/QuestionReorderModal';
import { reorderWorksheetQuestionsAction, removeQuestionFromWorksheetAction, autoFillWorksheetQuestionsAction } from '@/actions/worksheet.action';
import { QuestionListingViewRef } from '@/components/molecules/QuestionListingView/QuestionListingView';

type WorksheetProgressViewProps = {
  worksheetId: string;
  initialStatus: WorksheetGeneratingStatus;
  initialQuestions?: Question[];
  initialQuestionIds?: string[]; // Array of question IDs from API
  initialProgress?: ProgressData;
  worksheetInfo?: {
    topic?: string;
    subject?: string; // Added subject
    grade?: string;
    language?: string;
    level?: string;
    totalQuestions?: number;
  };
  schoolInfo?: {
    name: string;
    address?: string;
    phoneNumber?: string;
    registeredNumber?: string;
    email?: string;
    logoUrl?: string;
  };
};

export const WorksheetProgressView: React.FC<WorksheetProgressViewProps> = ({
  worksheetId,
  initialStatus,
  initialQuestions = [],
  initialQuestionIds = [],
  initialProgress,
  worksheetInfo,
  schoolInfo,
}) => {
  const router = useRouter();
  const { status, questions, setQuestions } = useWorksheetProgress({
    worksheetId,
    initialStatus,
    initialQuestions,
    initialProgress,
  });

  // State for original questions to compare for changes
  const [originalQuestions, setOriginalQuestions] = useState<Question[]>(() =>
    JSON.parse(JSON.stringify(initialQuestions))
  );

  // Ref for QuestionListingView to trigger save
  const questionListingRef = useRef<QuestionListingViewRef>(null);
  
  // State for print modal and reorder modal
  const [isPrintModalOpen, setIsPrintModalOpen] = useState(false);
  const [isReorderModalOpen, setIsReorderModalOpen] = useState(false);
  const [isCreateExamModalOpen, setIsCreateExamModalOpen] = useState(false);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [isAutoFillLoading, setIsAutoFillLoading] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  
  // Validate question structure to prevent PDF generation errors
  const validateQuestion = (question: Question): Question => {
    return {
      id: question.id || undefined,
      type: question.type || 'multiple_choice',
      content: question.content || '',
      image: question.image || null,
      svgCode: question.svgCode || undefined,
      imagePrompt: question.imagePrompt || null,
      options: Array.isArray(question.options) ? question.options : [],
      answer: Array.isArray(question.answer) ? question.answer : [],
      explain: question.explain || '',
      prompt: question.prompt || undefined,
      subject: question.subject || undefined,
    };
  };

  // Validate questions array to ensure all questions have proper structure
  const validateQuestions = (questions: Question[]): Question[] => {
    if (!Array.isArray(questions)) {
      return [];
    }

    return questions
      .filter(question => question && typeof question === 'object')
      .map(validateQuestion);
  };

  const handleCancelReorder = () => {
    setQuestions([...originalQuestions]);
    setHasUnsavedChanges(false);
  };

  const handleSaveReorder = async (reorderedQuestions: Question[], questionIds?: string[]) => {
    if (!worksheetId) {
      throw new Error('Worksheet ID is required');
    }
    
    setIsSaving(true);
    
    try {
      const validatedQuestions = validateQuestions(reorderedQuestions);

      const reorderData = {
        reorders: validatedQuestions.map((question, index) => {
          let questionId: string;

          if (questionIds && questionIds[index]) {
            // Use questionIds from API if available
            questionId = questionIds[index];
          } else if (question.id) {
            // Fallback to question.id if available
            questionId = question.id;
          } else {
            // Last resort: use array index
            questionId = index.toString();
          }

          return {
            questionId,
            newPosition: index + 1 // API expects 1-based positions
          };
        })
      };

      const result = await reorderWorksheetQuestionsAction(worksheetId, reorderData);

      if (result.status === 'error') {
        throw new Error(result.message || 'Failed to reorder questions');
      }

      // Update the parent state with the validated questions
      setQuestions(validatedQuestions);
      setOriginalQuestions(validatedQuestions);
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Failed to save question order:', error);
      throw error;
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteQuestion = async (questionId: string, questionIndex: number, setLoading: (loading: boolean) => void) => {
    // Validate question ID exists
    if (!questionId) {
      return;
    }

    try {
      // Start loading state
      setLoading(true);
      
      // Call API and wait for response
      const result = await removeQuestionFromWorksheetAction(worksheetId, questionId);
      
      if (result.status === 'error') {
        throw new Error(result.message || 'Failed to remove question');
      }
      
      // Only update local state after successful API call
      setQuestions(prevQuestions =>
        prevQuestions.filter(q => q.id !== questionId)
      );
      
    } catch (error) {
      throw error; // Re-throw to allow parent component to handle if needed
    } finally {
      // Always stop loading
      setLoading(false);
    }
  };

  const handleAutoFillQuestions = async (questionCount: number) => {
    if (!worksheetId || questionCount <= 0) return;
    setIsAutoFillLoading(true);
    try {
      const result = await autoFillWorksheetQuestionsAction(worksheetId, questionCount);
      
      
      if (result.status === 'success' && result.data && result.data.addedQuestions) {
        const { addedQuestions, questionsAdded } = result.data;

        
        // Validate auto-filled questions to ensure consistent data structure
        const validatedQuestions = validateQuestions(addedQuestions);
        
        // Add validated questions to the existing list
        setQuestions(prevQuestions => [...prevQuestions, ...validatedQuestions]);
      } else {
        throw new Error(result.message || 'Failed to auto-fill questions');
      }
    } catch (error: any) {} finally {
      setIsAutoFillLoading(false);
    }
  };

  // If status is pending, show the loading screen with progress
  if (status === WorksheetGeneratingStatus.PENDING) {
    return (
      <LoadingWorksheetScreen
        worksheetId={worksheetId}
        initialQuestions={initialQuestions}
        initialProgress={initialProgress}
      />
    );
  }


  // If status is generated or error, show the completed worksheet
  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex-1 overflow-auto">
        <div className="w-full max-w-5xl mx-auto">
          <div className="w-full">
            {status === WorksheetGeneratingStatus.ERROR ? (
              <div className="w-full p-8 text-center">
                <div className="text-error text-xl mb-4">
                  An error occurred while generating the worksheet
                </div>
                <div className="text-gray-500">
                  Please try again or contact support if the problem persists.
                </div>
              </div>
            ) : (
              <>
                <QuestionListingView
                  ref={questionListingRef}
                  questions={questions}
                  questionIds={initialQuestionIds}
                  containerClass="pb-[50px] lg:pb-20"
                  isHtmlContent
                  worksheetInfo={worksheetInfo}
                  allowReordering={true}
                  worksheetId={worksheetId}
                  onSaveReorder={handleSaveReorder}
                  onHasUnsavedChangesChange={setHasUnsavedChanges}
                  allowDelete={true}
                  allowBulkDelete={false}
                  onDeleteQuestion={handleDeleteQuestion}
                  isSelectionModeControlled={isSelectionMode}
                  onSelectionModeChange={setIsSelectionMode}
                  onAutoFillQuestions={handleAutoFillQuestions}
                  isAutoFillLoading={isAutoFillLoading}
                />
                {/* Mobile-Responsive Bottom Action Bar - Minimal Design */}
                <div className="fixed bottom-[55px] left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-40 lg:bottom-0 lg:left-[310px] lg:w-[calc(100vw-310px)]">
                  <div className="flex justify-between items-center h-[48px] lg:h-[60px] px-3 lg:px-6">
                    <div className="w-7 h-7 lg:w-9 lg:h-9 rounded-full bg-gray-100 flex items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors">
                      <Link href="/manage-worksheet">
                        <Icon
                          variant="chevron-down"
                          className="rotate-90"
                          size={2.5}
                        />
                      </Link>
                    </div>
                    <div className="flex gap-2 lg:gap-3 items-center">
                      {/* Save Changes Button */}
                      {hasUnsavedChanges && (
                        <>
                          <Button
                            onClick={handleCancelReorder}
                            variant="outline"
                            className="px-3 py-1.5 h-8 lg:px-4 lg:py-2 lg:h-10 w-fit text-xs lg:text-sm font-medium"
                          >
                            Cancel
                          </Button>
                          <Button
                            onClick={() => questionListingRef.current?.save()}
                            isLoading={isSaving}
                            className="px-3 py-1.5 h-8 lg:px-4 lg:py-2 lg:h-10 w-fit text-xs lg:text-sm font-medium bg-green-600 hover:bg-green-700 text-white"
                            iconProps={{
                              variant: 'check',
                              size: 4,
                            }}
                          >
                            <span className="">Save Order</span>
                          </Button>
                        </>
                      )}
                      
                      {/* Reorder Questions Button */}
                      {!hasUnsavedChanges && questions && questions.length > 1 && !isSelectionMode && (
                        <Button
                          onClick={() => setIsReorderModalOpen(true)}
                          variant="outline"
                          className="px-3 py-1.5 h-8 lg:px-4 lg:py-2 lg:h-10 w-fit text-xs lg:text-sm font-medium border-blue-300 !text-blue-700 hover:bg-blue-50"
                          iconProps={{
                            variant: "list",
                            size: 3,
                            className: "text-blue-600"
                          }}
                        >
                          <span className="hidden lg:inline">Reorder Questions</span>
                          <span className="lg:hidden">Reorder</span>
                        </Button>
                      )}

                      {/* Print Button */}
                      {!hasUnsavedChanges && !isSelectionMode && (
                        <Button
                          onClick={() => setIsPrintModalOpen(true)}
                          className="px-3 py-1.5 h-8 lg:px-4 lg:py-2 lg:h-10 w-fit text-xs lg:text-sm font-medium"
                        >
                          <span className="hidden lg:inline">Print</span>
                          <span className="lg:hidden">Print</span>
                        </Button>
                      )}

                      {/* Create Exam Button */}
                      {!hasUnsavedChanges && !isSelectionMode && (
                        <Button
                          onClick={() => setIsCreateExamModalOpen(true)}
                          className="px-3 py-1.5 h-8 lg:px-4 lg:py-2 lg:h-10 w-fit text-xs lg:text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white"
                          iconProps={{
                            variant: "file-text",
                            size: 3,
                            className: "text-white"
                          }}
                        >
                          <span className="hidden lg:inline">Create Exam</span>
                          <span className="lg:hidden">Exam</span>
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Print Modal */}
                {isPrintModalOpen && (
                  <PrintModal
                    isOpen={isPrintModalOpen}
                    onClose={() => setIsPrintModalOpen(false)}
                    questions={validateQuestions(questions)}
                    worksheetInfo={worksheetInfo}
                    schoolInfo={schoolInfo}
                  />
                )}

                {/* Question Reorder Modal */}
                {isReorderModalOpen && (
                  <QuestionReorderModal
                    isOpen={isReorderModalOpen}
                    onClose={() => setIsReorderModalOpen(false)}
                    questions={questions}
                    questionIds={initialQuestionIds}
                    onSaveReorder={handleSaveReorder}
                    isLoading={isSaving}
                    isHtmlContent={true}
                  />
                )}

                {/* Create Exam Modal */}
                {isCreateExamModalOpen && worksheetInfo && (
                  <CreateExamModal
                    isOpen={isCreateExamModalOpen}
                    onClose={() => setIsCreateExamModalOpen(false)}
                    worksheet={{
                      id: worksheetId,
                      title: 'Untitled Worksheet'
                    }}
                    onSuccess={() => {
                      setIsCreateExamModalOpen(false);
                      // Optionally show a success message or redirect
                    }}
                  />
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
