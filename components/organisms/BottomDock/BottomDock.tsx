'use client';

import * as React from 'react';
import { cn } from '@/utils/cn';
import type { ComponentProps } from 'react';

export interface BottomDockProps extends ComponentProps<'div'> {}

const BottomDock: React.FC<BottomDockProps> = ({ className, children, ...props }) => {
  return (
    <div
      className={cn(
        // Base positioning: Fixed at bottom for mobile, responsive for desktop
        'fixed bottom-[55px] left-0 right-0',
        'lg:bottom-0 lg:left-[310px] lg:w-[calc(100vw-310px)]',
        // Background and styling
        'bg-white border-t border-gray-200 shadow-lg z-40',
        // Padding and safe area
        'p-4 safe-area-inset-bottom',
        // Flexbox layout - default to right alignment, can be overridden
        'flex items-center justify-end gap-4',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

export default BottomDock; 