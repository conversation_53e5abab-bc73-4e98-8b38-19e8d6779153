'use client';

import React, { useState } from 'react';
import { IStudentExamResultForTeacher } from '@/types/exam.types';
import { Users } from 'lucide-react';
import { cn } from '@/utils/cn';
import { 
  AssignmentTable, 
  QuestionAnalysis,
  IAssignmentDetail 
} from '@/components/molecules/DetailedStudentResult';

export interface DetailedStudentResultViewProps {
  studentResult: IStudentExamResultForTeacher;
  assignments?: IAssignmentDetail[];
}

export const DetailedStudentResultView: React.FC<DetailedStudentResultViewProps> = ({
  studentResult,
  assignments
}) => {
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);

  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  return (
    <div className="space-y-3 md:space-y-6">
      {/* Student Assignment List */}
      {assignments && (
        <div className="bg-background-default rounded-lg shadow-sm p-3 md:p-6">
          <div className="mb-4">
            <h2 className="text-base md:text-lg font-semibold text-text-primary flex items-center gap-2">
              <Users className="w-4 md:w-5 h-4 md:h-5 text-primary-action" />
              Student Assignments
            </h2>
          </div>
          <AssignmentTable
            assignments={assignments}
            sortConfig={sortConfig}
            onSort={handleSort}
          />
        </div>
      )}
      
      {/* Question-by-Question Analysis */}
      <div className="bg-background-default rounded-lg shadow-sm p-3 md:p-6">
        <QuestionAnalysis questions={studentResult.questions} />
      </div>
    </div>
  );
};
