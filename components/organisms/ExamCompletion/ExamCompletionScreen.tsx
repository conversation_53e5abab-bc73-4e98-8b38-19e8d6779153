'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { CheckCircle, Clock, FileText } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { ExamCardContainer } from '@/components/molecules/ExamLayout';
import { IExamCompletionData } from '@/types/exam.types';
import { cn } from '@/utils/cn';

interface ExamCompletionScreenProps {
  completionData: IExamCompletionData;
  className?: string;
}

export const ExamCompletionScreen: React.FC<ExamCompletionScreenProps> = ({
  completionData,
  className
}) => {
  const router = useRouter();

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'medium',
      timeStyle: 'short'
    }).format(new Date(date));
  };

  const handleViewResults = () => {
    router.push(`/exam/${completionData.examId}/result`);
  };

  const handleGoToDashboard = () => {
    router.push('/exam');
  };

  return (
    <div className={cn('min-h-screen bg-gray-50 py-8 px-4', className)}>
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Success Header */}
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Exam Completed Successfully!
          </h1>
          <p className="text-lg text-gray-600">
            {completionData.examTitle}
          </p>
          <p className="text-sm text-gray-500 mt-1">
            Submitted on {formatDate(completionData.submissionTimestamp)}
          </p>
        </div>

        {/* Submission Summary */}
        <ExamCardContainer variant="instruction">
          <div className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Submission Summary
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <FileText className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Questions Answered</p>
                  <p className="font-semibold">
                    {completionData.answeredQuestions} of {completionData.totalQuestions}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Clock className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Time Taken</p>
                  <p className="font-semibold">
                    {formatTime(completionData.timeSpent)}
                    {completionData.timeLimit && (
                      <span className="text-sm text-gray-500 ml-1">
                        of {formatTime(completionData.timeLimit)}
                      </span>
                    )}
                  </p>
                </div>
              </div>

              {completionData.score !== undefined && (
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">%</span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Score</p>
                    <p className="font-semibold">
                      {completionData.score}%
                      <span className={cn(
                        'ml-2 px-2 py-1 rounded-full text-xs font-medium',
                        completionData.passed 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      )}>
                        {completionData.passed ? 'Passed' : 'Failed'}
                      </span>
                    </p>
                  </div>
                </div>
              )}

              <div className="flex items-center gap-3">
                <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">#</span>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Submission ID</p>
                  <p className="font-mono text-sm">
                    {completionData.submissionId.split('-').pop()}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </ExamCardContainer>

        {/* Results Information */}
        <ExamCardContainer variant="instruction">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              What&apos;s Next?
            </h3>

            {completionData.resultsAvailable ? (
              <div className="flex items-center gap-3 p-4 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <p className="font-medium text-green-900">Results Available Now</p>
                  <p className="text-sm text-green-700">
                    Your exam has been automatically graded. View your detailed results below.
                  </p>
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <Clock className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-900">Results Coming Soon</p>
                  <p className="text-sm text-blue-700">
                    Your exam will be graded manually. Results will be available within 3-5 business days.
                  </p>
                </div>
              </div>
            )}
          </div>
        </ExamCardContainer>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          {completionData.resultsAvailable && (
            <Button
              onClick={handleViewResults}
              variant="primary"
              className="flex-1"
              iconProps={{ variant: 'arrow-right', className: 'w-4' }}
            >
              View Detailed Results
            </Button>
          )}

          <Button
            onClick={handleGoToDashboard}
            variant={completionData.resultsAvailable ? "outline" : "primary"}
            className="flex-1"
          >
            {completionData.resultsAvailable ? 'Back to Exams' : 'Go to Dashboard'}
          </Button>
        </div>
      </div>
    </div>
  );
};
