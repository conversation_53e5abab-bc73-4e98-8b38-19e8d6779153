'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/utils/cn';
import { useReducedMotion } from '@/hooks/useReducedMotion';

export interface ExamCardSkeletonProps {
  animationDelay?: number;
  showProgress?: boolean;
}

export const ExamCardSkeleton: React.FC<ExamCardSkeletonProps> = ({ 
  animationDelay = 0,
  showProgress = false 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const prefersReducedMotion = useReducedMotion();

  // Trigger entrance animation on mount with staggered delay
  useEffect(() => {
    const delay = prefersReducedMotion ? 0 : animationDelay;
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);
    return () => clearTimeout(timer);
  }, [animationDelay, prefersReducedMotion]);

  return (
    <div
      className={cn(
        "relative overflow-hidden rounded-2xl sm:rounded-3xl bg-background-default border border-gray-200/50",
        "min-h-[200px] sm:min-h-[220px] md:min-h-[240px]",
        // Performance optimizations
        "skeleton-optimized",
        // Entrance animation matching the real card
        "transform transition-all duration-500 ease-out",
        isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4",
        // Respect reduced motion preference
        prefersReducedMotion && "transition-none"
      )}
    >
      {/* Top accent bar skeleton with shimmer */}
      <div className="absolute top-0 left-0 right-0 h-1 sm:h-1.5 bg-gray-200 overflow-hidden">
        {!prefersReducedMotion && (
          <div 
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-[shimmer_2s_infinite]"
            style={{ animationDelay: `${animationDelay}ms` }}
          />
        )}
      </div>

      <div className="p-4 sm:p-5 md:p-6 h-full flex flex-col">
        {/* Header section skeleton */}
        <div className="mb-4 sm:mb-5 flex-grow">
          {/* Title skeleton with responsive sizing and shimmer */}
          <div className="relative overflow-hidden rounded mb-2 sm:mb-3">
            <div className="h-5 sm:h-6 bg-gray-200 rounded w-3/4" />
            {!prefersReducedMotion && (
              <div 
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-[shimmer_2s_infinite]"
                style={{ animationDelay: `${animationDelay + 200}ms` }}
              />
            )}
          </div>

          {/* Info lines skeleton with staggered shimmer */}
          <div className="space-y-1.5 sm:space-y-2">
            {/* Questions and duration line */}
            <div className="relative overflow-hidden rounded">
              <div className="h-3 sm:h-4 bg-gray-200 rounded w-2/3" />
              {!prefersReducedMotion && (
                <div 
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-[shimmer_2s_infinite]"
                  style={{ animationDelay: `${animationDelay + 400}ms` }}
                />
              )}
            </div>
            
            {/* Assignment date line */}
            <div className="relative overflow-hidden rounded">
              <div className="h-3 sm:h-4 bg-gray-200 rounded w-1/2" />
              {!prefersReducedMotion && (
                <div 
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-[shimmer_2s_infinite]"
                  style={{ animationDelay: `${animationDelay + 600}ms` }}
                />
              )}
            </div>
            
            {/* Status chip skeleton */}
            <div className="pt-1">
              <div className="relative overflow-hidden rounded-full">
                <div className="h-5 sm:h-6 bg-gray-200 rounded-full w-16 sm:w-20" />
                {!prefersReducedMotion && (
                  <div 
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-[shimmer_2s_infinite]"
                    style={{ animationDelay: `${animationDelay + 800}ms` }}
                  />
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Progress bar skeleton removed - will be implemented when real progress tracking is available */}

        {/* Footer section skeleton */}
        <div className="flex items-center justify-end pt-3 sm:pt-4 border-t border-gray-100 mt-auto">
          {/* Button skeleton with shimmer */}
          <div className="relative overflow-hidden rounded-xl">
            <div className="h-11 sm:h-10 bg-gray-200 rounded-xl w-20 sm:w-24" />
            {!prefersReducedMotion && (
              <div 
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-[shimmer_2s_infinite]"
                style={{ animationDelay: `${animationDelay + 1600}ms` }}
              />
            )}
          </div>
        </div>
      </div>

      {/* Main shimmer overlay effect */}
      {!prefersReducedMotion && (
        <div 
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-[shimmer_2s_infinite] pointer-events-none"
          style={{ animationDelay: `${animationDelay}ms` }}
        />
      )}
    </div>
  );
};

// Skeleton grid component for multiple cards
export interface ExamSkeletonGridProps {
  count?: number;
  showProgressVariants?: boolean;
}

export const ExamSkeletonGrid: React.FC<ExamSkeletonGridProps> = ({
  count = 8,
  showProgressVariants = true
}) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-5 md:gap-6 lg:gap-7 xl:gap-8 transition-all duration-300 ease-out">
      {Array.from({ length: count }).map((_, index) => (
        <ExamCardSkeleton
          key={index}
          animationDelay={index * 100} // Staggered entrance animation
          showProgress={false} // Progress removed until real progress tracking is available
        />
      ))}
    </div>
  );
};