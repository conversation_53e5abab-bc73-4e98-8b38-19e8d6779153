'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getAssignedExamsAction } from '@/actions/exam.action';
import {
  IAssignedExamsResponse,
  IExamListItem
} from '@/types/exam.types';
import { Button } from '@/components/atoms/Button/Button';
import { MobileOptimizedTablePagination } from '@/components/molecules/TablePagination/MobileOptimizedTablePagination';
import { EmptyState } from '@/components/molecules/EmptyState';
import { ErrorDisplay } from '@/components/molecules/ErrorDisplay/ErrorDisplay';
import { ErrorBoundary } from '@/components/molecules/ErrorBoundary';
import { cn } from '@/utils/cn';
import { ExamSkeletonGrid } from './ExamCardSkeleton';
import { useReducedMotion } from '@/hooks/useReducedMotion';

// Simplified status configuration using semantic color tokens
const getStatusConfig = (status: string) => {
  switch (status) {
    case 'assigned':
      return {
        label: 'Not Started',
        badgeColor: 'bg-primary-action text-background-default',
        buttonVariant: 'primary' as const,
        buttonText: 'Start Exam',
        accentColor: 'bg-primary-action',
      };
    case 'in_progress':
      return {
        label: 'In Progress',
        badgeColor: 'bg-link-default text-background-default',
        buttonVariant: 'primary' as const,
        buttonText: 'Continue',
        accentColor: 'bg-link-default',
      };
    case 'completed':
      return {
        label: 'Completed',
        badgeColor: 'bg-green-500 text-background-default',
        buttonVariant: 'secondary' as const,
        buttonText: 'View Completion',
        accentColor: 'bg-green-500',
      };
    default:
      return {
        label: status,
        badgeColor: 'bg-text-secondary text-background-default',
        buttonVariant: 'secondary' as const,
        buttonText: 'View',
        accentColor: 'bg-text-secondary',
      };
  }
};

// Enhanced Exam Card Component with performance optimization and accessibility
const ExamCard = React.memo<{
  exam: IExamListItem;
  onTakeExam: (examId: string, assignmentStatus?: string) => void;
  animationDelay?: number;
}>(({ exam, onTakeExam, animationDelay = 0 }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isClicked, setIsClicked] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const prefersReducedMotion = useReducedMotion();

  // Trigger entrance animation on mount with reduced motion support
  useEffect(() => {
    const delay = prefersReducedMotion ? 0 : animationDelay;
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);
    return () => clearTimeout(timer);
  }, [animationDelay, prefersReducedMotion]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const statusConfig = getStatusConfig(exam.assignmentStatus);

  // Progress data removed - will be implemented when real progress tracking is available
  // const progress = (exam as any).progress || (exam.assignmentStatus === 'in_progress' ? 65 : 0);

  const handleClick = () => {
    setIsClicked(true);
    // Reset click state after animation
    setTimeout(() => setIsClicked(false), 200);
    // Navigate after brief delay for visual feedback
    setTimeout(() => onTakeExam(exam.id, exam.assignmentStatus), 150);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleClick();
    }
  };

  // Generate accessible description for screen readers
  const getAccessibleDescription = () => {
    const statusText = statusConfig.label.toLowerCase();
    return `${exam.title}, ${statusText}, assigned ${formatDate(exam.createdAt)}. Press Enter or Space to ${statusConfig.buttonText.toLowerCase()}.`;
  };

  return (
    <div
      role="button"
      tabIndex={0}
      aria-label={getAccessibleDescription()}
      aria-describedby={`exam-${exam.id}-details`}
      className={cn(
        "relative overflow-hidden rounded-2xl sm:rounded-3xl bg-background-default border border-gray-200/50 cursor-pointer group",
        // Performance optimizations
        "exam-card-optimized",
        // Smooth hover animations with mobile-optimized scaling
        prefersReducedMotion ? "transition-none" : "exam-card-hover",
        "hover:shadow-lg",
        // Focus styles for keyboard navigation
        "focus:outline-none focus:ring-2 focus:ring-primary-action focus:ring-offset-2",
        isFocused && "ring-2 ring-primary-action ring-offset-2",
        // Mobile touch optimization - larger touch target
        prefersReducedMotion ? "" : "exam-card-click active:shadow-md",
        // Entrance animation with reduced motion support
        prefersReducedMotion ? "transition-none" : "staggered-entrance",
        isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4",
        // Click feedback
        isClicked && !prefersReducedMotion && "scale-[0.98] transition-transform duration-150 ease-in",
        // Mobile-first responsive design
        "min-h-[200px] sm:min-h-[220px] md:min-h-[240px]"
      )}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(false)}
    >
      {/* Top accent bar with responsive height */}
      <div
        className={cn(
          "absolute top-0 left-0 right-0 h-1 sm:h-1.5 transition-opacity duration-300",
          statusConfig.accentColor
        )}
      />

      <div className="p-4 sm:p-5 md:p-6 h-full flex flex-col">
        {/* Header section - clean and focused with responsive typography */}
        <div className="mb-4 sm:mb-5 flex-grow">
          {/* Full exam title with responsive typography */}
          <h3 className="text-lg sm:text-xl md:text-xl font-bold text-text-primary mb-2 sm:mb-3 leading-tight line-clamp-2">
            {exam.title}
          </h3>

          {/* Important exam info with responsive spacing - hidden from screen readers as it's in aria-label */}
          <div 
            id={`exam-${exam.id}-details`}
            className="space-y-1.5 sm:space-y-2 text-xs sm:text-sm text-text-secondary"
            aria-hidden="true"
          >
            <div className="flex flex-wrap items-center gap-1 sm:gap-2">
              <span className="font-medium">25 Questions</span>
              <span className="text-gray-400">•</span>
              <span className="font-medium">60 Minutes</span>
            </div>
            <div className="text-xs sm:text-sm">
              <span>Assigned {formatDate(exam.createdAt)}</span>
            </div>
            <div className="pt-1">
              {/* Status chip with responsive sizing */}
              <span className={cn(
                "inline-flex items-center px-2 sm:px-2.5 py-1 sm:py-1.5 rounded-full text-xs font-medium",
                statusConfig.badgeColor
              )}>
                {statusConfig.label}
              </span>
            </div>
          </div>
        </div>

        {/* Progress bar removed - will be implemented when real progress tracking is available */}

        {/* Footer section with mobile-optimized button sizing */}
        <div className="flex items-center justify-end pt-3 sm:pt-4 border-t border-gray-100 mt-auto">
          <Button
            variant={statusConfig.buttonVariant}
            aria-label={`${statusConfig.buttonText} for ${exam.title}`}
            onClick={(e) => {
              e.stopPropagation();
              setIsClicked(true);
              setTimeout(() => setIsClicked(false), 200);
              setTimeout(() => onTakeExam(exam.id, exam.assignmentStatus), 150);
            }}
            className={cn(
              // Mobile-first button sizing with larger touch targets
              "px-4 sm:px-5 py-2.5 sm:py-2 rounded-xl text-sm sm:text-sm font-medium",
              // Enhanced touch interactions with reduced motion support
              prefersReducedMotion ? "transition-none" : "transition-all duration-200",
              "active:scale-95 sm:hover:scale-105",
              // Minimum touch target size for mobile accessibility
              "min-h-[44px] sm:min-h-[40px] min-w-[88px]",
              // Click feedback animation
              isClicked && !prefersReducedMotion && "scale-95",
              // Focus styles for keyboard navigation
              "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-action"
            )}
          >
            {statusConfig.buttonText}
          </Button>
        </div>
      </div>
    </div>
  );
});

// Add display name for debugging and optimize memoization
ExamCard.displayName = 'ExamCard';

// Memoization comparison function for better performance
const areExamCardsEqual = (
  prevProps: { exam: IExamListItem; onTakeExam: (examId: string, assignmentStatus?: string) => void; animationDelay?: number },
  nextProps: { exam: IExamListItem; onTakeExam: (examId: string, assignmentStatus?: string) => void; animationDelay?: number }
) => {
  // Only re-render if exam data or animation delay changes
  return (
    prevProps.exam.id === nextProps.exam.id &&
    prevProps.exam.title === nextProps.exam.title &&
    prevProps.exam.assignmentStatus === nextProps.exam.assignmentStatus &&
    prevProps.exam.createdAt === nextProps.exam.createdAt &&
    prevProps.animationDelay === nextProps.animationDelay &&
    (prevProps.exam as any).progress === (nextProps.exam as any).progress
  );
};

export interface ExamListProps {
  initialData?: IAssignedExamsResponse;
  isLoading?: boolean;
  error?: string | null;
  tableTitle?: string;
  className?: string;
  // Pagination props for server-side pagination
  currentPageBackend?: number;
  totalPagesBackend?: number;
  totalItemsBackend?: number;
  onBackendPageChange?: (page: number) => void;
  onBackendRowsPerPageChange?: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  rowsPerPageBackend?: number;
}

const DEFAULT_PAGE_SIZE = 10;
const DEFAULT_PAGE_INDEX = 0;

export const ExamList: React.FC<ExamListProps> = ({
  initialData,
  isLoading: externalLoading = false,
  error: externalError = null,
  tableTitle = "",
  className,
  currentPageBackend,
  totalPagesBackend,
  totalItemsBackend,
  onBackendPageChange,
  onBackendRowsPerPageChange,
  rowsPerPageBackend,
}) => {
  const router = useRouter();

  // Local state for data management
  const [examData, setExamData] = useState<IAssignedExamsResponse | null>(initialData || null);
  const [isLoading, setIsLoading] = useState(externalLoading);
  const [isDataReady, setIsDataReady] = useState(false);
  const [error, setError] = useState<string | null>(externalError);
  const [retryCount, setRetryCount] = useState(0);

  // Pagination state
  const [pagination, setPagination] = useState({
    pageIndex: DEFAULT_PAGE_INDEX,
    pageSize: DEFAULT_PAGE_SIZE,
  });

  // Fetch data function - simplified without error recovery hook to prevent re-render loops
  const fetchExamData = useCallback(async (page: number = 1, limit: number = DEFAULT_PAGE_SIZE) => {
    try {
      setIsLoading(true);
      setError(null);
      setIsDataReady(false);

      const response = await getAssignedExamsAction({
        page,
        limit
      });

      if (response.status === 'success') {
        setExamData(response.data || null);
        setRetryCount(0); // Reset retry count on success
        // Add a small delay to ensure smooth transition from skeleton to content
        setTimeout(() => {
          setIsDataReady(true);
        }, 150);
      } else {
        const errorMessage = Array.isArray(response.message)
          ? response.message.join(', ')
          : response.message || 'Failed to load assigned exams';
        throw new Error(errorMessage);
      }
    } catch (err: any) {
      console.error('Error fetching assigned exams:', err);
      setError(err.message || 'Failed to load exams');
      setRetryCount(prev => prev + 1);
    } finally {
      setIsLoading(false);
    }
  }, []); // Empty dependency array to prevent re-creation

  // Initialize data ready state when initial data is provided
  useEffect(() => {
    if (initialData && initialData.items && initialData.items.length > 0) {
      setIsDataReady(true);
    }
  }, [initialData]);

  // Initial data fetch if no initial data provided - only run once
  useEffect(() => {
    if (!initialData && !externalLoading) {
      fetchExamData(1, DEFAULT_PAGE_SIZE);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array to run only once

  // Handler for taking an exam or viewing completion
  const handleTakeExam = (examId: string, assignmentStatus?: string) => {
    if (assignmentStatus === 'completed') {
      // If exam is completed, redirect to completion page
      router.push(`/exam/${examId}/completed`);
    } else {
      // Otherwise, redirect to exam taking page
      router.push(`/exam/${examId}`);
    }
  };

  // Handle pagination change for TablePagination component
  const handleTablePaginationChange = (page: number) => {
    const newPagination = { ...pagination, pageIndex: page - 1 };
    setPagination(newPagination);

    if (onBackendPageChange) {
      onBackendPageChange(page);
    } else {
      fetchExamData(page, newPagination.pageSize);
    }
  };

  // Handle page size change for TablePagination component
  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newPageSize = parseInt(e.target.value);
    const newPagination = { pageIndex: 0, pageSize: newPageSize };
    setPagination(newPagination);

    if (onBackendRowsPerPageChange) {
      onBackendRowsPerPageChange(e);
    } else {
      fetchExamData(1, newPageSize);
    }
  };

  // Loading state with enhanced skeleton components
  if (isLoading && !examData) {
    return (
      <div className={cn("space-y-4 sm:space-y-5 md:space-y-6 p-3 sm:p-4 md:p-6 transition-all duration-300 ease-out", className)}>
        {/* Header skeleton with enhanced responsive spacing */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-3 md:gap-4">
          {tableTitle && (
            <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 leading-tight">{tableTitle}</h2>
          )}
          {/* Count skeleton with shimmer */}
          <div className="relative overflow-hidden rounded">
            <div className="h-4 sm:h-5 bg-gray-200 rounded w-20 sm:w-24" />
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-[shimmer_2s_infinite]" />
          </div>
        </div>
        
        {/* Enhanced skeleton grid with variety */}
        <ExamSkeletonGrid count={8} showProgressVariants={true} />
      </div>
    );
  }

  // Error state with simplified error handling
  if (error || externalError) {
    const displayError = error || externalError || 'Unknown error occurred';
    const isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;
    
    return (
      <div className={cn("space-y-4 sm:space-y-5 md:space-y-6 p-3 sm:p-4 md:p-6", className)}>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-3 md:gap-4">
          {tableTitle && (
            <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 leading-tight">{tableTitle}</h2>
          )}
        </div>
        <ErrorDisplay
          error={displayError}
          title="Unable to Load Exams"
          description={
            isOnline 
              ? "We couldn't fetch your exam assignments. This might be due to a server issue or temporary problem."
              : "You appear to be offline. Please check your internet connection and try again."
          }
          onRetry={() => {
            fetchExamData(pagination.pageIndex + 1, pagination.pageSize);
          }}
          isRetrying={isLoading}
          retryText={retryCount < 3 ? `Retry (${3 - retryCount} attempts left)` : "Reload Exams"}
          variant={isOnline ? 'default' : 'network'}
          fallbackContent={
            <div className="mt-4 space-y-3">
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 className="text-sm font-semibold text-blue-800 mb-2">What you can try:</h3>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Check your internet connection</li>
                  <li>• Refresh the page</li>
                  <li>• Try again in a few moments</li>
                  <li>• Contact support if the problem persists</li>
                </ul>
              </div>
              {retryCount > 0 && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    We&apos;ve tried {retryCount} time{retryCount > 1 ? 's' : ''} to load your exams.
                    {retryCount < 3 ? ' We can try again.' : ' Please refresh the page or contact support.'}
                  </p>
                </div>
              )}
            </div>
          }
        />
      </div>
    );
  }

  // Check if we have no data (but not loading)
  const hasNoData = !isLoading && (!examData || !examData.items || examData.items.length === 0);

  return (
    <ErrorBoundary
      level="component"
      context="ExamList"
      showDetails={true}
      enableReporting={true}
      fallback={
        <div className={cn("space-y-4 sm:space-y-5 md:space-y-6 p-3 sm:p-4 md:p-6", className)}>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-3 md:gap-4">
            {tableTitle && (
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 leading-tight">{tableTitle}</h2>
            )}
          </div>
          <ErrorDisplay
            error="Component failed to render"
            title="Exam List Unavailable"
            description="The exam list component encountered an error. Please try refreshing the page."
            onRetry={() => window.location.reload()}
            retryText="Refresh Page"
            variant="critical"
            fallbackContent={
              <div className="mt-4 p-6 bg-gray-50 border border-gray-200 rounded-lg text-center">
                <p className="text-gray-600 mb-4">Unable to display exam list at this time.</p>
                <Button
                  variant="primary"
                  onClick={() => window.location.href = '/'}
                  className="mr-2"
                >
                  Go to Dashboard
                </Button>
                <Button
                  variant="secondary"
                  onClick={() => window.location.reload()}
                >
                  Refresh Page
                </Button>
              </div>
            }
          />
        </div>
      }
    >
      <div className={cn("space-y-4 sm:space-y-5 md:space-y-6 p-3 sm:p-4 md:p-6 transition-all duration-300 ease-out", className)}>
        {/* Header with enhanced responsive spacing and typography */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-3 md:gap-4">
          {tableTitle && (
            <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 leading-tight">{tableTitle}</h2>
          )}
          <div className="text-xs sm:text-sm text-gray-500 sm:text-right font-medium">
            {examData?.totalItems || 0} {(examData?.totalItems || 0) === 1 ? 'exam' : 'exams'} found
          </div>
        </div>

        {hasNoData ? (
          <ErrorBoundary
            level="component"
            context="EmptyState"
            fallback={
              <div className="p-8 text-center bg-gray-50 rounded-lg">
                <p className="text-gray-600">No exams available at this time.</p>
              </div>
            }
          >
            <EmptyState />
          </ErrorBoundary>
        ) : (
          // Card grid content with responsive grid and improved spacing
          <>
            <ErrorBoundary
              level="component"
              context="ExamCardGrid"
              fallback={
                <div className="p-8 text-center bg-gray-50 rounded-lg">
                  <p className="text-gray-600 mb-4">Unable to display exam cards.</p>
                  <Button
                    variant="secondary"
                    onClick={() => fetchExamData(pagination.pageIndex + 1, pagination.pageSize)}
                  >
                    Try Again
                  </Button>
                </div>
              }
            >
              <div 
                className={cn(
                  "grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-5 md:gap-6 lg:gap-7 xl:gap-8",
                  "transition-all duration-500 ease-out",
                  isDataReady ? "opacity-100 translate-y-0" : "opacity-0 translate-y-2"
                )}
              >
                {examData?.items?.map((exam, index) => (
                  <ErrorBoundary
                    key={exam.id}
                    level="component"
                    context={`ExamCard-${exam.id}`}
                    fallback={
                      <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg text-center">
                        <p className="text-sm text-gray-600">Unable to load exam</p>
                        <p className="text-xs text-gray-500 mt-1">{exam.title}</p>
                      </div>
                    }
                  >
                    <ExamCard
                      exam={exam}
                      onTakeExam={handleTakeExam}
                      animationDelay={isDataReady ? index * 100 : 0} // Staggered entrance animation only when data is ready
                    />
                  </ErrorBoundary>
                ))}
              </div>
            </ErrorBoundary>

            {examData && examData.totalItems > 0 && (
              <ErrorBoundary
                level="component"
                context="TablePagination"
                fallback={
                  <div className="flex justify-center pt-6">
                    <p className="text-sm text-gray-500">Pagination unavailable</p>
                  </div>
                }
              >
                <div className="pt-6 sm:pt-8 border-t border-gray-200/50 transition-all duration-300 ease-out">
                  <MobileOptimizedTablePagination
                    currentPage={examData.currentPage}
                    totalPages={examData.totalPages}
                    totalItems={examData.totalItems}
                    rowsPerPage={examData.pageSize}
                    onPageChange={handleTablePaginationChange}
                    onRowsPerPageChange={handlePageSizeChange}
                  />
                </div>
              </ErrorBoundary>
            )}
          </>
        )}
      </div>
    </ErrorBoundary>
  );
};
