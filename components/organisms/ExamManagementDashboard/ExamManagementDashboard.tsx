'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ITeacherExamsResponse, ITeacherExam } from '@/actions/exam.action';
import { Button } from '@/components/atoms/Button/Button';
import { CreateExamModal } from '@/components/molecules/CreateExamModal/CreateExamModal';
import { ExamListTable } from '@/components/molecules/ExamManagement/ExamListTable';
import {
  Eye,
  Users,
  TrendingUp,
  Clock,
  Calendar,
  MoreHorizontal,
  Plus,
  FileText,
  BarChart3
} from 'lucide-react';
import { cn } from '@/utils/cn';

export interface ExamManagementDashboardProps {
  examData: ITeacherExamsResponse;
}

export const ExamManagementDashboard: React.FC<ExamManagementDashboardProps> = ({
  examData,
}) => {
  const router = useRouter();
  const [selectedWorksheet, setSelectedWorksheet] = useState<{ id: string; title: string } | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const handleViewResults = (examId: string) => {
    router.push(`/admin/exam-results/${examId}`);
  };

  const handleCreateExam = () => {
    // For now, redirect to worksheet management to select a worksheet
    router.push('/manage-worksheet');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = 'px-2 py-1 text-xs font-medium rounded-full';
    switch (status) {
      case 'active':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'inactive':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="bg-base-100 rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-base-content">Quick Actions</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button
            onClick={handleCreateExam}
            className="flex items-center justify-center gap-2 h-12"
            variant="primary"
          >
            <Plus className="w-4 h-4" />
            Create New Exam
          </Button>
          <Button
            onClick={() => router.push('/manage-worksheet')}
            className="flex items-center justify-center gap-2 h-12"
            variant="outline"
          >
            <FileText className="w-4 h-4" />
            Manage Worksheets
          </Button>
          <Button
            onClick={() => router.push('/admin/exam-results/analytics')}
            className="flex items-center justify-center gap-2 h-12"
            variant="outline"
          >
            <BarChart3 className="w-4 h-4" />
            View Analytics
          </Button>
        </div>
      </div>

      {/* Exams Table - Using the new ExamListTable component */}
      <ExamListTable
        initialData={examData}
        tableTitle="Your Exams"
        showCreateButton={true}
        onCreateExam={handleCreateExam}
      />

      {/* Create Exam Modal */}
      {selectedWorksheet && (
        <CreateExamModal
          isOpen={isCreateModalOpen}
          onClose={() => {
            setIsCreateModalOpen(false);
            setSelectedWorksheet(null);
          }}
          worksheet={selectedWorksheet}
          onSuccess={() => {
            // Refresh the page or refetch data
            window.location.reload();
          }}
        />
      )}
    </div>
  );
};
