import { z } from 'zod';

/**
 * Schema for individual exam answer
 * Each answer corresponds to a question by index
 */
export const examAnswerSchema = z.object({
  questionIndex: z.number().min(0, 'Question index must be non-negative'),
  userAnswer: z.array(z.string()).min(1, 'At least one answer is required'),
});

/**
 * Schema for the complete exam form
 * Contains all answers for all questions
 */
export const examFormSchema = z.object({
  answers: z.array(examAnswerSchema).min(1, 'At least one answer is required'),
});

/**
 * Type definitions for form data
 */
export type ExamAnswerData = z.infer<typeof examAnswerSchema>;
export type ExamFormData = z.infer<typeof examFormSchema>;

/**
 * Helper function to create default form values
 * @param questionCount - Number of questions in the exam
 * @returns Default form data with empty answers
 */
export function createDefaultExamFormData(questionCount: number): ExamFormData {
  return {
    answers: Array.from({ length: questionCount }, (_, index) => ({
      questionIndex: index,
      userAnswer: [''],
    })),
  };
}
