'use client';

import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { useForm, FieldError } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { RefreshCw, X, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/atoms/Button/Button';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { ExamHeader } from '@/components/molecules/ExamHeader';
import { QuestionCard } from '@/components/molecules/QuestionCard';
import {
  ExamQuestionSidebar,
  ExamQuestionOverviewModal
} from '@/components/molecules/ExamQuestionNavigation';
import { clearExamTimer, ExamTimer } from '@/components/molecules/ExamTimer';
import BottomDock from '@/components/organisms/BottomDock/BottomDock';
import {
  ExamReviewSection,
  ExamSubmissionDialog,
  ExamSubmissionProgress
} from '@/components/molecules';
import {
  ExamResponsiveLayout,
  ExamQuestionGrid,
  ExamCardContainer
} from '@/components/molecules/ExamLayout';
import { submitExamAction } from '@/actions/exam.action';
import { IExamResponse } from '@/types/exam.types';
import { cn } from '@/utils/cn';
import { useViewport, responsiveClasses, touchUtils } from '@/utils/responsive';
import { useAccessibility, useKeyboardShortcuts } from '@/hooks/useAccessibility';
import { useExamProgress } from '@/hooks/useExamProgress';

import {
  examFormSchema,
  ExamFormData,
  createDefaultExamFormData
} from './ExamTaking.schema';

interface ExamTakingProps {
  exam: IExamResponse;
  className?: string;
}

export const ExamTaking: React.FC<ExamTakingProps> = ({
  exam,
  className,
}) => {
  const router = useRouter();
  const { isMobile, isTablet, isDesktop, currentBreakpoint } = useViewport();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0); // 0-based index
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [showOverviewModal, setShowOverviewModal] = useState(false);

  // Accessibility hooks
  const {
    announce,
    focusManagement,
    getAccessibilityClasses,
    getAnnouncementRegionProps
  } = useAccessibility({
    announceProgress: true,
    enableKeyboardNavigation: true
  });

  // Focus management hook (currently unused but may be needed for accessibility)
  // const { saveFocus, restoreFocus } = useFocusRestore();

  // Refs for focus management
  const mainContentRef = useRef<HTMLDivElement>(null);
  const questionCardRef = useRef<HTMLDivElement>(null);
  const skipLinkRef = useRef<HTMLAnchorElement>(null);

  // Enhanced submission states
  const [showReviewSection, setShowReviewSection] = useState(false);
  const [showSubmissionDialog, setShowSubmissionDialog] = useState(false);
  const [showSubmissionProgress, setShowSubmissionProgress] = useState(false);
  const [submissionStage, setSubmissionStage] = useState<'preparing' | 'uploading' | 'processing' | 'complete' | 'error'>('preparing');
  const [timeSpent, setTimeSpent] = useState(0);

  // Time tracking effect
  useEffect(() => {
    const startTime = Date.now();
    const interval = setInterval(() => {
      setTimeSpent(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Initialize form with default values
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    control,
    formState: { errors },
  } = useForm<ExamFormData>({
    resolver: zodResolver(examFormSchema),
    defaultValues: createDefaultExamFormData(exam.questions.length),
  });

  // Watch all answers to calculate progress
  const watchedAnswers = watch('answers');
  




  // Use centralized exam progress hook to eliminate duplication
  // Use watchedAnswers directly as it contains the correct structure
  const answersArray = useMemo(() => {
    if (!watchedAnswers || !Array.isArray(watchedAnswers)) {
      // Return default structure if watchedAnswers is not available
      return exam.questions.map((_, index) => ({
        questionIndex: index,
        userAnswer: ['']
      }));
    }
    return watchedAnswers;
  }, [watchedAnswers, exam.questions]);

  const examProgress = useExamProgress({
    exam,
    answers: answersArray
  });

  // Destructure for easier access
  const {
    totalQuestions,
    answeredQuestions,
    unansweredQuestions,
    completionPercentage,
    questionProgress,
    isComplete
  } = examProgress;



  // Navigation handlers with accessibility

  const handleQuestionSelect = useCallback((questionIndex: number) => {
    if (questionIndex !== currentQuestion) {
      // Announce navigation to screen readers
      const question = exam.questions[questionIndex];
      const isAnswered = questionProgress[questionIndex]?.isAnswered;
      announce(
        `Navigated to question ${questionIndex + 1} of ${totalQuestions}. ${question.type.replace('_', ' ')} question. ${isAnswered ? 'Already answered' : 'Not yet answered'}.`,
        'polite'
      );

      setCurrentQuestion(questionIndex);

      // Focus the question card
      setTimeout(() => {
        if (questionCardRef.current) {
          focusManagement.focusFirst(questionCardRef.current);
        }
      }, 50);
    }
  }, [currentQuestion, exam.questions, questionProgress, totalQuestions, announce, focusManagement]);

  const handlePreviousQuestion = useCallback(() => {
    if (currentQuestion > 0) {
      const newQuestionIndex = currentQuestion - 1;
      const isAnswered = questionProgress[newQuestionIndex]?.isAnswered;

      // Announce navigation
      announce(
        `Moving to previous question. Question ${newQuestionIndex + 1} of ${exam.questions.length}. ${isAnswered ? 'Already answered' : 'Not yet answered'}.`,
        'polite'
      );

      setCurrentQuestion(newQuestionIndex);

      // Focus management
      setTimeout(() => {
        if (questionCardRef.current) {
          focusManagement.focusFirst(questionCardRef.current);
        }
      }, 50);
    }
  }, [currentQuestion, questionProgress, exam.questions.length, announce, focusManagement]);

  const handleNextQuestion = useCallback(() => {
    if (currentQuestion < totalQuestions - 1) {
      const newQuestionIndex = currentQuestion + 1;
      const isAnswered = questionProgress[newQuestionIndex]?.isAnswered;

      // Announce navigation
      announce(
        `Moving to next question. Question ${newQuestionIndex + 1} of ${totalQuestions}. ${isAnswered ? 'Already answered' : 'Not yet answered'}.`,
        'polite'
      );

      setCurrentQuestion(newQuestionIndex);

      // Focus management
      setTimeout(() => {
        if (questionCardRef.current) {
          focusManagement.focusFirst(questionCardRef.current);
        }
      }, 50);
    }
  }, [currentQuestion, totalQuestions, questionProgress, announce, focusManagement]);

  const handleToggleSidebar = useCallback(() => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  }, [isSidebarCollapsed]);

  const handleShowOverview = useCallback(() => {
    setShowOverviewModal(true);
  }, []);

  const handleCloseOverview = useCallback(() => {
    setShowOverviewModal(false);
  }, []);

  // Handle navigation back to exam list
  const handleBackToExamList = () => {
    router.push('/exam');
  };

  // Enhanced submission handlers
  const handleShowReview = useCallback(() => {
    setShowReviewSection(true);
  }, []);

  const handleCloseReview = useCallback(() => {
    setShowReviewSection(false);
  }, []);

  const handleShowSubmissionDialog = useCallback(() => {
    setShowSubmissionDialog(true);
  }, []);

  const handleCloseSubmissionDialog = useCallback(() => {
    setShowSubmissionDialog(false);
    setSubmitError(null);
    setSubmitSuccess(false);
  }, []);

  const handleRetrySubmission = useCallback(() => {
    setSubmitError(null);
    setSubmitSuccess(false);
    setShowSubmissionDialog(false);
    // Trigger form submission again
    handleSubmit(onSubmit)();
  }, [handleSubmit]);

  const handleReviewQuestionSelect = useCallback((questionIndex: number) => {
    setShowReviewSection(false);
    handleQuestionSelect(questionIndex);
  }, [handleQuestionSelect]);

  // Keyboard shortcuts for exam navigation
  useKeyboardShortcuts({
    'arrowleft': handlePreviousQuestion,
    'arrowright': handleNextQuestion,
    'alt+r': handleShowReview,
    'alt+o': handleShowOverview,
    'escape': () => {
      if (showReviewSection) {
        handleCloseReview();
      } else if (showOverviewModal) {
        handleCloseOverview();
      } else if (showSubmissionDialog) {
        handleCloseSubmissionDialog();
      }
    }
  });

  // Announce progress changes
  useEffect(() => {
    if (answeredQuestions > 0) {
      announce(
        `Progress update: ${answeredQuestions} of ${totalQuestions} questions answered. ${completionPercentage}% complete.`,
        'polite'
      );
    }
  }, [answeredQuestions, totalQuestions, completionPercentage, announce]);

  // Skip to main content handler
  const handleSkipToMain = useCallback(() => {
    if (mainContentRef.current) {
      mainContentRef.current.focus();
      mainContentRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  // Enhanced form submission with progress tracking
  const onSubmit = async (data: ExamFormData) => {
    // Show submission dialog first for confirmation
    setShowSubmissionDialog(true);
  };

  // Actual submission handler called from confirmation dialog
  const handleConfirmSubmission = async () => {
    setShowSubmissionDialog(false);
    setShowSubmissionProgress(true);
    setIsSubmitting(true);
    setSubmitError(null);
    setSubmitSuccess(false);

    try {
      // Stage 1: Preparing submission
      setSubmissionStage('preparing');
      await new Promise(resolve => setTimeout(resolve, 800)); // Simulate preparation time

      // Transform form data to match API format
      const formData = watch();
      const submissionAnswers = formData.answers.map(answer => ({
        questionIndex: answer.questionIndex,
        userAnswer: answer.userAnswer.filter(ans => ans.trim() !== ''), // Remove empty answers
      }));

      // Stage 2: Uploading answers
      setSubmissionStage('uploading');
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate upload time

      // Stage 3: Processing submission
      setSubmissionStage('processing');

      // Submit to server action
      const response = await submitExamAction(exam.id, submissionAnswers, timeSpent);

      if (response.status === 'success') {
        // Stage 4: Complete
        setSubmissionStage('complete');
        setSubmitSuccess(true);
        
        // Clear timer data since exam is completed
        clearExamTimer(exam.id);
        
        await new Promise(resolve => setTimeout(resolve, 1500)); // Show success state

        // Hide progress and show success dialog
        setShowSubmissionProgress(false);
        setShowSubmissionDialog(true);

        // Redirect to completion page after a delay
        setTimeout(() => {
          console.log('Exam submission successful, redirecting to completion page');
          console.log('Response data:', response.data);

          // Redirect directly to completion page - no completion key needed
          const redirectUrl = `/exam/${exam.id}/completed`;
          console.log('Redirecting to:', redirectUrl);
          router.push(redirectUrl);
        }, 1500); // Reduced delay since we're going to a proper completion page
      } else {
        // Handle error response
        setSubmissionStage('error');
        let errorMessage = 'Failed to submit exam';

        if (typeof response.message === 'string') {
          errorMessage = response.message;

          // Provide more specific error messages based on content
          if (errorMessage.toLowerCase().includes('network')) {
            errorMessage = 'Network error. Please check your connection and try again.';
          } else if (errorMessage.toLowerCase().includes('timeout')) {
            errorMessage = 'Request timed out. Please try submitting again.';
          } else if (errorMessage.toLowerCase().includes('authentication') || errorMessage.toLowerCase().includes('session')) {
            errorMessage = 'Session expired. Please log in again.';
          }
        } else if (Array.isArray(response.message)) {
          // Handle validation errors
          errorMessage = 'Please check your answers and try again.';
        }

        setSubmitError(errorMessage);

        // Show error in progress component for 2 seconds, then show dialog
        setTimeout(() => {
          setShowSubmissionProgress(false);
          setShowSubmissionDialog(true);
        }, 2000);
      }
    } catch (error: any) {
      console.error('Error submitting exam:', error);
      setSubmissionStage('error');

      // Provide more specific error messages based on error type
      let errorMessage = 'An unexpected error occurred';

      if (error.name === 'NetworkError' || error.message?.includes('fetch')) {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      } else if (error.name === 'TimeoutError') {
        errorMessage = 'Request timed out. Please try again.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setSubmitError(errorMessage);

      // Show error in progress component for 2 seconds, then show dialog
      setTimeout(() => {
        setShowSubmissionProgress(false);
        setShowSubmissionDialog(true);
      }, 2000);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render question input using enhanced QuestionCard component
  const renderQuestionInput = (question: IExamResponse['questions'][0], index: number) => {
    const questionError = errors.answers?.[index]?.userAnswer as FieldError | undefined;
    const currentAnswer = watchedAnswers?.[index]?.userAnswer || [];

    return (
      <QuestionCard
        key={index}
        question={question}
        questionIndex={index}
        totalQuestions={totalQuestions}
        isActive={true}
        currentAnswer={currentAnswer}
        register={register}
        control={control}
        watch={watch}
        setValue={setValue}
        error={questionError}
        showNavigation={false} // We'll handle navigation separately for now
        className="mb-6"
      />
    );
  };

  return (
    <div className={cn('relative', getAccessibilityClasses())}>
      {/* Screen Reader Announcements */}
      <div {...getAnnouncementRegionProps()} />

      {/* Main Exam Interface */}
      <ExamResponsiveLayout
        className={className}
        header={
          <ExamHeader
            examTitle={exam.title}
            currentQuestion={currentQuestion + 1} // Convert to 1-based for display
            totalQuestions={totalQuestions}
            answeredQuestions={answeredQuestions}
            timeRemaining={exam.timeLimit}
            timeLimit={exam.timeLimit}
            examId={exam.id}
            onTimeExpired={() => {
              // Auto-submit when time expires
              console.log('Timer expired (from header), auto-submitting exam');
              handleSubmit(onSubmit)();
            }}
          />
        }
        sidebar={
          <ExamQuestionSidebar
            currentQuestion={currentQuestion}
            totalQuestions={totalQuestions}
            questionProgress={questionProgress}
            onQuestionSelect={handleQuestionSelect}
            isCollapsed={isSidebarCollapsed}
            onToggleCollapse={handleToggleSidebar}
            onPrevious={handlePreviousQuestion}
            onNext={handleNextQuestion}
            onSubmit={handleSubmit(onSubmit)}
            timeLimit={exam.timeLimit}
            examId={exam.id}
            onTimeExpired={() => {
              // Auto-submit when time expires
              console.log('Timer expired, auto-submitting exam');
              handleSubmit(onSubmit)();
            }}
          />
        }
        bottomNav={null}
      >
        <div
          ref={mainContentRef}
          id="main-content"
          tabIndex={-1}
          className="focus:outline-none"
          role="main"
          aria-label={`Exam: ${exam.title}. Question ${currentQuestion + 1} of ${totalQuestions}`}
        >
          <ExamQuestionGrid>
            {/* Error Message */}
            {submitError && (
              <ExamCardContainer variant="question">
                <AlertMessage
                  type="error"
                  title="Submission Failed"
                  message={submitError}
                  className="mb-4"
                />
                <div className="flex justify-center">
                  <Button
                    variant="outline"
                    onClick={() => setSubmitError(null)}
                    className={cn(
                      'min-w-32',
                      // Touch-optimized sizing
                      responsiveClasses.touch.button
                    )}
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Try Again
                  </Button>
                </div>
              </ExamCardContainer>
            )}

            {/* Exam Form */}
            <form onSubmit={handleSubmit(onSubmit)}>
              {/* Current Question */}
              {exam.questions[currentQuestion] && (
                <div key={`question-${currentQuestion}`}>
                  {renderQuestionInput(exam.questions[currentQuestion], currentQuestion)}
                </div>
              )}
            </form>
          </ExamQuestionGrid>

          {/* Mobile Question Overview Modal */}
          <ExamQuestionOverviewModal
            isOpen={showOverviewModal}
            onClose={handleCloseOverview}
            currentQuestion={currentQuestion}
            totalQuestions={totalQuestions}
            questionProgress={questionProgress}
            onQuestionSelect={handleQuestionSelect}
          />

          {/* Enhanced Submission Components */}

          {/* Review Section Modal */}
          {showReviewSection && (
            <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
                <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                  <h2 className="text-xl font-semibold">Review Your Answers</h2>
                  <button
                    onClick={handleCloseReview}
                    className="p-2 hover:bg-gray-100 rounded-lg"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
                <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
                  <ExamReviewSection
                    questions={exam.questions}
                    answers={watchedAnswers || []}
                    examTitle={exam.title}
                    timeSpent={timeSpent}
                    onQuestionSelect={handleReviewQuestionSelect}
                    className="border-0 shadow-none"
                    progressData={examProgress}
                  />
                </div>
                <div className="p-4 border-t border-gray-200 flex gap-3 justify-end">
                  <Button
                    onClick={handleCloseReview}
                    variant="outline"
                  >
                    Continue Exam
                  </Button>
                  <Button
                    onClick={handleShowSubmissionDialog}
                    variant="primary"
                    iconProps={{ variant: 'send' , className: 'w-4'}}
                  >
                    Submit Exam
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Submission Confirmation Dialog */}
          <ExamSubmissionDialog
            isOpen={showSubmissionDialog}
            onClose={handleCloseSubmissionDialog}
            onConfirm={handleConfirmSubmission}
            onRetry={handleRetrySubmission}
            examTitle={exam.title}
            totalQuestions={totalQuestions}
            answeredQuestions={answeredQuestions}
            timeSpent={timeSpent}
            isSubmitting={isSubmitting}
            submitError={submitError || undefined}
            submitSuccess={submitSuccess}
          />

          {/* Submission Progress Overlay */}
          <ExamSubmissionProgress
            isVisible={showSubmissionProgress}
            stage={submissionStage}
            error={submitError || undefined}
          />
        </div>
      </ExamResponsiveLayout>

      {/* Mobile Bottom Navigation */}
      {(isMobile || isTablet) && (
        <BottomDock className="bg-white/95 backdrop-blur-xl border-t border-gray-100 shadow-lg py-3 px-4">
          <div className="flex items-center justify-between w-full max-w-lg mx-auto">
            {/* Left: Back Button */}
            <Button
              variant="ghost"
              onClick={handleBackToExamList}
              className={cn(
                "rounded-full p-0 text-gray-500 hover:text-gray-900 hover:bg-gray-100 transition-all duration-200",
                touchUtils.getTouchTargetSize(currentBreakpoint),
                touchUtils.touchClasses.button
              )}
              aria-label="Back to exam list"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>

            {/* Center: Navigation Controls */}
            <div className="flex items-center gap-3">
              {/* Previous Button */}
              <Button
                type="button"
                variant="outline"
                onClick={handlePreviousQuestion}
                disabled={currentQuestion === 0}
                className={cn(
                  "disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium",
                  touchUtils.getTouchTargetSize(currentBreakpoint),
                  touchUtils.touchClasses.button
                )}
                aria-label="Previous question"
              >
                Prev
              </Button>

             {/* Right: Simple Timer */}
            {exam.timeLimit && (
              <ExamTimer
                timeLimit={exam.timeLimit}
                examId={exam.id}
                onTimeExpired={() => {
                  console.log('Timer expired (from dock), auto-submitting exam');
                  handleSubmit(onSubmit)();
                }}
                isCompact={true}
                className="text-sm bg-gray-100 rounded-lg px-3 py-2 font-medium text-gray-700"
              />
            )}

              {/* Next/Submit Button Logic - Fixed */}
              {currentQuestion < totalQuestions - 1 ? (
                // Show Next button when not on last question
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleNextQuestion}
                  className={cn(
                    "text-sm font-medium",
                    touchUtils.getTouchTargetSize(currentBreakpoint),
                    touchUtils.touchClasses.button
                  )}
                  aria-label="Next question"
                >
                  Next
                </Button>
              ) : (
                // Show Submit button on last question
                <Button
                  type="submit"
                  onClick={handleSubmit(onSubmit)}
                  disabled={isSubmitting}
                  variant="primary"
                  className={cn(
                    "text-sm font-medium",
                    touchUtils.getTouchTargetSize(currentBreakpoint),
                    touchUtils.touchClasses.button
                  )}
                  aria-label="Submit exam"
                  isLoading={isSubmitting}
                  iconProps={{
                    variant: 'send',
                    className: 'w-4'
                  }}
                >
                  {isSubmitting ? 'Submitting' : 'Submit'}
                </Button>
              )}
            </div>

            
          </div>
        </BottomDock>
      )}

      {/* Desktop Bottom Dock - Minimal */}
      {isDesktop && (
        <BottomDock className="bg-white/95 lg:left-[321px] backdrop-blur-xl border-t border-gray-100 shadow-lg justify-start py-4 px-6">
          <Button
            variant="ghost"
            onClick={handleBackToExamList}
            className="w-10 h-10 rounded-full p-0 text-gray-500 hover:text-gray-900 hover:bg-gray-100 transition-all duration-200"
            aria-label="Back to exam list"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
        </BottomDock>
      )}
    </div>
  );
};
