import { z } from 'zod';
import { requiredString } from '@/utils/zod';

/**
 * Validation schema for accept invitation form
 * Validates name, password, and password confirmation for completing registration
 * Ensures passwords match for security
 */
export const acceptInvitationFormSchema = z.object({
  name: requiredString.min(1, 'Name is required').max(100, 'Name is too long'),
  password: requiredString.min(6, 'Password must be at least 6 characters'),
  confirmPassword: requiredString.min(1, 'Please confirm your password'),
}).refine(
  (data) => data.password === data.confirmPassword,
  {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  }
);

export type AcceptInvitationFormData = z.infer<typeof acceptInvitationFormSchema>;
