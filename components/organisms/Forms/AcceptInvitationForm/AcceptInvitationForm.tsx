'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { User, GraduationCap, Mail, Building } from 'lucide-react';
import { signIn } from 'next-auth/react';

import { Button } from '@/components/atoms/Button/Button';
import { FormField } from '@/components/molecules/FormField/FormField';
import { InputWithIcon } from '@/components/molecules/InputWithIcon/InputWithIcon';
import { PasswordInput } from '@/components/molecules/PasswordInput/PasswordInput';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { InfoField } from '@/components/molecules/InfoField/InfoField';
import { acceptInvitationAction } from '@/actions/invitation.action';
import { IValidateInvitationResponse } from '@/apis/invitation';
import { cn } from '@/utils/cn';

import { acceptInvitationFormSchema, AcceptInvitationFormData } from './AcceptInvitationForm.schema';

export interface AcceptInvitationFormProps {
  invitationData: IValidateInvitationResponse;
  token: string;
}

export default function AcceptInvitationForm({ invitationData, token }: AcceptInvitationFormProps) {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<AcceptInvitationFormData>({
    resolver: zodResolver(acceptInvitationFormSchema),
    defaultValues: {
      name: '',
      password: '',
      confirmPassword: '',
    },
  });

  const onSubmit = async (data: AcceptInvitationFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      // Accept the invitation
      const response = await acceptInvitationAction(
        token,
        data.name,
        data.password,
        data.confirmPassword
      );

      if (response.status === 'success' && response.data) {
        // The invitation acceptance returns access token and user data
        // Sign in the user with NextAuth using their credentials
        const signInResponse = await signIn('credentials', {
          email: invitationData.email,
          password: data.password,
          redirect: false,
        });

        if (signInResponse?.error) {
          setError('Account created successfully, but there was an issue signing you in. Please try signing in manually.');
          return;
        }

        // Redirect to dashboard on success
        router.push('/manage-worksheet');
      } else {
        // Handle error response
        const errorMessage = response.status === 'error' && typeof response.message === 'string'
          ? response.message
          : 'Failed to accept invitation. Please try again.';
        setError(errorMessage);
      }
    } catch (err: any) {
      console.error('Error accepting invitation:', err);
      setError('An unexpected error occurred while creating your account. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
        {/* Header */}
        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-6 sm:mb-8"
        >
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-accent-bg-light rounded-full">
              <GraduationCap size={32} className="text-primary-action" />
            </div>
          </div>
          <h1 className="text-2xl sm:text-3xl font-bold text-text-primary mb-2">
            Complete Your Registration
          </h1>
          <p className="text-text-secondary text-sm sm:text-base">
            Your email and school are confirmed. Just set your name and password to get started.
          </p>
        </motion.div>

        {/* Error Message */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-4"
          >
            <AlertMessage
              type="error"
              message={error}
              className="text-sm"
            />
          </motion.div>
        )}

        {/* Form */}
        <motion.form
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          onSubmit={handleSubmit(onSubmit)}
          className="space-y-4"
        >
          {/* Read-Only Information */}
          <div className="bg-background-subtle border border-gray-200 rounded-lg p-4 space-y-3">
            <InfoField
              label="Email"
              value={invitationData.email}
              icon={<Mail size={16} className="text-link-default" />}
            />
            <InfoField
              label="School"
              value={invitationData.school.name}
              icon={<Building size={16} className="text-link-default" />}
            />
          </div>

          {/* Name Field */}
          <FormField
            label="Full Name"
            error={errors.name?.message}
            required
          >
            <InputWithIcon
              type="text"
              placeholder="Enter your full name"
              leftIcon={<User size={16} className="text-link-default" />}
              className={cn(
                'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
                'bg-background-default/50 backdrop-blur-sm',
                errors.name && 'border-red-500 focus:border-red-500'
              )}
              disabled={isSubmitting}
              {...register('name')}
            />
          </FormField>

          {/* Password Field */}
          <FormField
            label="Password"
            error={errors.password?.message}
            required
          >
            <PasswordInput
              placeholder="Create a strong password"
              className={cn(
                'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
                'bg-background-default/50 backdrop-blur-sm',
                errors.password && 'border-red-500 focus:border-red-500'
              )}
              disabled={isSubmitting}
              {...register('password')}
            />
          </FormField>

          {/* Confirm Password Field */}
          <FormField
            label="Confirm Password"
            error={errors.confirmPassword?.message}
            required
          >
            <PasswordInput
              placeholder="Confirm your password"
              className={cn(
                'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
                'bg-background-default/50 backdrop-blur-sm',
                errors.confirmPassword && 'border-red-500 focus:border-red-500'
              )}
              disabled={isSubmitting}
              {...register('confirmPassword')}
            />
          </FormField>

          {/* Submit Button */}
          <Button
            type="submit"
            variant="primary"
            size="lg"
            className="w-full h-12 text-base font-semibold rounded-xl"
            loading={isSubmitting}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Creating Account...' : 'Create My Account'}
          </Button>
        </motion.form>
      </div>
  );
}
