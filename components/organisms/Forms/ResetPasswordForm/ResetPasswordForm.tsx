'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useFormState } from 'react-dom';
import { useSearchPara<PERSON>, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Lock, Eye, EyeOff, ArrowLeft, CheckCircle2, AlertTriangle } from 'lucide-react';

import { Button } from '@/components/atoms/Button/Button';
import { FormField } from '@/components/molecules/FormField/FormField';
import { InputWithIcon } from '@/components/molecules/InputWithIcon/InputWithIcon';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';

import { ResetPasswordValues, resetPasswordSchema } from './ResetPasswordForm.schema';
import { resetPasswordAction } from '@/actions/auth.action';
import type { AuthActionResult } from '@/actions/auth.schemas';
import { APP_ROUTE } from '@/constants/route.constant';
import { cn } from '@/utils/cn';

export default function ResetPasswordForm() {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [tokenError, setTokenError] = useState<string | null>(null);
  
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get('token');

  const [state, formAction] = useFormState(resetPasswordAction, {
    success: false,
    message: '',
    errors: {},
  });

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
  } = useForm<ResetPasswordValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      token: token || '',
      password: '',
      confirmPassword: '',
    },
  });

  const watchedPassword = watch('password');
  const watchedConfirmPassword = watch('confirmPassword');

  // Validate token on component mount
  useEffect(() => {
    if (!token) {
      setTokenError('No reset token provided. Please check your email link or request a new password reset.');
    } else if (token.length < 10) {
      setTokenError('Invalid reset token format. Please request a new password reset link.');
    } else {
      setTokenError(null);
    }
  }, [token]);

  // Handle successful password reset
  useEffect(() => {
    if (state.success && !isSubmitted) {
      setIsSubmitted(true);
      // Redirect to sign-in after a short delay to show success message
      const timeoutId = setTimeout(() => {
        router.push(APP_ROUTE.AUTH.SIGN_IN);
      }, 3000);

      // Cleanup function to clear timeout if component unmounts
      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, [state.success, isSubmitted, router]);

  const onSubmit = async (data: ResetPasswordValues) => {
    // Create FormData for server action
    const formData = new FormData();
    formData.append('token', data.token);
    formData.append('password', data.password);
    formData.append('confirmPassword', data.confirmPassword);
    
    // Call server action
    formAction(formData);
  };

  // Token error state
  if (tokenError) {
    return (
      <div className="w-full max-w-md mx-auto px-4 sm:px-0">
        {React.createElement(motion.div, {
          initial: { opacity: 0, scale: 0.95 },
          animate: { opacity: 1, scale: 1 },
          transition: { duration: 0.6, ease: "easeOut" }
        }, 
          <div className="relative">
            {/* Gradient Background */}
            <div className="absolute inset-0 bg-gradient-to-br from-section-bg-accent via-background-default to-background-subtle rounded-xl sm:rounded-2xl"></div>

            {/* Glass Effect Overlay */}
            <div className="relative bg-background-default/80 backdrop-blur-sm border border-white/20 rounded-xl sm:rounded-2xl shadow-xl shadow-text-primary/10 p-6 sm:p-8">
              {/* Error Icon */}
              {React.createElement(motion.div, {
                initial: { scale: 0 },
                animate: { scale: 1 },
                transition: { duration: 0.5, delay: 0.2 }
              },
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center">
                    <AlertTriangle size={32} className="text-white" />
                  </div>
                </div>
              )}

              {/* Error Content */}
              <div className="text-center space-y-4">
                {React.createElement(motion.div, {
                  initial: { y: 20, opacity: 0 },
                  animate: { y: 0, opacity: 1 },
                  transition: { duration: 0.5, delay: 0.4 }
                },
                  <h2 className="text-xl sm:text-2xl font-semibold text-text-primary">
                    Invalid Reset Link
                  </h2>
                )}
                
                {React.createElement(motion.div, {
                  initial: { y: 20, opacity: 0 },
                  animate: { y: 0, opacity: 1 },
                  transition: { duration: 0.5, delay: 0.5 }
                },
                  <p className="text-text-secondary text-sm sm:text-base leading-relaxed">
                    {tokenError}
                  </p>
                )}

                {React.createElement(motion.div, {
                  initial: { y: 20, opacity: 0 },
                  animate: { y: 0, opacity: 1 },
                  transition: { duration: 0.5, delay: 0.6 }
                },
                  <div className="pt-4 space-y-3">
                    <Link href={APP_ROUTE.AUTH.FORGOT_PASSWORD} className="block">
                      <Button variant="primary" className="w-full">
                        Request New Reset Link
                      </Button>
                    </Link>
                    
                    <Link href={APP_ROUTE.AUTH.SIGN_IN} className="block">
                      <Button variant="ghost" className="w-full" iconProps={{
                        variant: 'arrow-left',
                        className: 'w-4'
                      }}>
                        Back to Sign In
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Success state
  if (isSubmitted && state.success) {
    return (
      <div className="w-full max-w-md mx-auto px-4 sm:px-0">
        {React.createElement(motion.div, {
          initial: { opacity: 0, scale: 0.95 },
          animate: { opacity: 1, scale: 1 },
          transition: { duration: 0.6, ease: "easeOut" }
        },
          <div className="relative">
            {/* Gradient Background */}
            <div className="absolute inset-0 bg-gradient-to-br from-section-bg-accent via-background-default to-background-subtle rounded-xl sm:rounded-2xl"></div>

            {/* Glass Effect Overlay */}
            <div className="relative bg-background-default/80 backdrop-blur-sm border border-white/20 rounded-xl sm:rounded-2xl shadow-xl shadow-text-primary/10 p-6 sm:p-8">
              {/* Success Animation */}
              {React.createElement(motion.div, {
                initial: { scale: 0 },
                animate: { scale: 1 },
                transition: { duration: 0.5, delay: 0.2 }
              },
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center">
                    <CheckCircle2 size={32} className="text-white" />
                  </div>
                </div>
              )}

              {/* Success Content */}
              <div className="text-center space-y-4">
                {React.createElement(motion.div, {
                  initial: { y: 20, opacity: 0 },
                  animate: { y: 0, opacity: 1 },
                  transition: { duration: 0.5, delay: 0.4 }
                },
                  <h2 className="text-xl sm:text-2xl font-semibold text-text-primary">
                    Password Reset Successful
                  </h2>
                )}
                
                {React.createElement(motion.div, {
                  initial: { y: 20, opacity: 0 },
                  animate: { y: 0, opacity: 1 },
                  transition: { duration: 0.5, delay: 0.5 }
                },
                  <p className="text-text-secondary text-sm sm:text-base leading-relaxed">
                    {state.message}
                  </p>
                )}

                {React.createElement(motion.div, {
                  initial: { y: 20, opacity: 0 },
                  animate: { y: 0, opacity: 1 },
                  transition: { duration: 0.5, delay: 0.6 }
                },
                  <div className="pt-4">
                    <p className="text-xs text-text-secondary mb-4">
                      You will be redirected to the sign-in page automatically...
                    </p>
                    
                    <Link href={APP_ROUTE.AUTH.SIGN_IN} className="block">
                      <Button className="w-full">
                        Continue to Sign In
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Main form state
  return (
    <div className="w-full max-w-md mx-auto px-4 sm:px-0">
      {React.createElement(motion.div, {
        initial: { opacity: 0, y: 20 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.6, ease: "easeOut" }
      },
        <div className="space-y-4 sm:space-y-6">
          {/* Error Alert */}
          {!state.success && state.message && (
            React.createElement(motion.div, {
              initial: { opacity: 0, scale: 0.95 },
              animate: { opacity: 1, scale: 1 },
              transition: { duration: 0.3 }
            },
              <AlertMessage
                type="error"
                message={state.message}
              />
            )
          )}

          {/* Main Form Card */}
          {React.createElement(motion.div, {
            initial: { opacity: 0, y: 20 },
            animate: { opacity: 1, y: 0 },
            transition: { duration: 0.6, delay: 0.1 }
          },
            <div className="relative">
              {/* Gradient Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-section-bg-accent via-background-default to-background-subtle rounded-xl sm:rounded-2xl"></div>

              {/* Glass Effect Overlay */}
              <div className="relative bg-background-default/80 backdrop-blur-sm border border-white/20 rounded-xl sm:rounded-2xl shadow-xl shadow-text-primary/10 p-4 sm:p-6">
                {/* Decorative Elements */}
                <div className="absolute top-0 right-0 w-24 sm:w-32 h-24 sm:h-32 bg-gradient-to-br from-link-default/10 to-purple-400/10 rounded-full -mr-12 sm:-mr-16 -mt-12 sm:-mt-16"></div>
                <div className="absolute bottom-0 left-0 w-20 sm:w-24 h-20 sm:h-24 bg-gradient-to-tr from-green-400/10 to-link-default/10 rounded-full -ml-10 sm:-ml-12 -mb-10 sm:-mb-12"></div>

                {/* Header */}
                <div className="text-center mb-6 relative z-10">
                  {React.createElement(motion.div, {
                    initial: { y: 10, opacity: 0 },
                    animate: { y: 0, opacity: 1 },
                    transition: { duration: 0.5, delay: 0.3 }
                  },
                    <h1 className="text-lg sm:text-xl font-semibold flex max-sm:flex-col max-sm:gap-0.5 justify-center items-center gap-2 text-text-primary mb-2">
                      Reset your password
                    </h1>
                  )}
                  {React.createElement(motion.div, {
                    initial: { y: 10, opacity: 0 },
                    animate: { y: 0, opacity: 1 },
                    transition: { duration: 0.5, delay: 0.4 }
                  },
                    <p className="text-xs sm:text-sm text-text-secondary">
                      Enter your new password below to complete the reset process.
                    </p>
                  )}
                </div>

                {/* Form */}
                <form 
                  onSubmit={handleSubmit(onSubmit)} 
                  className="space-y-4 relative z-10" 
                  role="form" 
                  aria-label="Reset password form"
                >
                  {/* Hidden token field */}
                  <input type="hidden" {...register('token')} />

                  {/* Password Field */}
                  {React.createElement(motion.div, {
                    initial: { y: 20, opacity: 0 },
                    animate: { y: 0, opacity: 1 },
                    transition: { duration: 0.5, delay: 0.5 }
                  },
                    <FormField
                      label="New Password"
                      error={errors.password?.message || (state.errors?.password ? state.errors.password[0] : undefined)}
                      required
                    >
                      <InputWithIcon
                        type={showPassword ? 'text' : 'password'}
                        placeholder="Enter your new password"
                        leftIcon={<Lock size={16} className="sm:size-[18px] text-link-default" />}
                        rightIcon={
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="text-text-secondary hover:text-link-default transition-colors duration-200"
                          >
                            {showPassword ? (
                              <EyeOff size={16} className="sm:size-[18px]" />
                            ) : (
                              <Eye size={16} className="sm:size-[18px]" />
                            )}
                          </button>
                        }
                        className={cn(
                          'h-10 sm:h-12 text-sm sm:text-base border-2 rounded-lg sm:rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
                          'bg-background-default/50 backdrop-blur-sm',
                          (errors.password || state.errors?.password)
                            ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                            : 'border-gray-200 focus:border-link-default'
                        )}
                        aria-describedby={errors.password ? 'password-error' : undefined}
                        {...register('password')}
                      />
                    </FormField>
                  )}

                  {/* Confirm Password Field */}
                  {React.createElement(motion.div, {
                    initial: { y: 20, opacity: 0 },
                    animate: { y: 0, opacity: 1 },
                    transition: { duration: 0.5, delay: 0.6 }
                  },
                    <FormField
                      label="Confirm New Password"
                      error={errors.confirmPassword?.message || (state.errors?.confirmPassword ? state.errors.confirmPassword[0] : undefined)}
                      required
                    >
                      <InputWithIcon
                        type={showConfirmPassword ? 'text' : 'password'}
                        placeholder="Confirm your new password"
                        leftIcon={<Lock size={16} className="sm:size-[18px] text-link-default" />}
                        rightIcon={
                          <button
                            type="button"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="text-text-secondary hover:text-link-default transition-colors duration-200"
                          >
                            {showConfirmPassword ? (
                              <EyeOff size={16} className="sm:size-[18px]" />
                            ) : (
                              <Eye size={16} className="sm:size-[18px]" />
                            )}
                          </button>
                        }
                        className={cn(
                          'h-10 sm:h-12 text-sm sm:text-base border-2 rounded-lg sm:rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
                          'bg-background-default/50 backdrop-blur-sm',
                          (errors.confirmPassword || state.errors?.confirmPassword)
                            ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                            : 'border-gray-200 focus:border-link-default'
                        )}
                        aria-describedby={errors.confirmPassword ? 'confirm-password-error' : undefined}
                        {...register('confirmPassword')}
                      />
                    </FormField>
                  )}

                  {/* Submit Button */}
                  {React.createElement(motion.div, {
                    initial: { y: 20, opacity: 0 },
                    animate: { y: 0, opacity: 1 },
                    transition: { duration: 0.5, delay: 0.7 }
                  },
                    <Button
                      type="submit"
                      className="w-full h-10 sm:h-12 text-sm sm:text-base font-medium rounded-lg sm:rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl"
                      disabled={isSubmitting || !watchedPassword?.trim() || !watchedConfirmPassword?.trim()}
                      isLoading={isSubmitting}
                    >
                      {isSubmitting ? 'Resetting Password...' : 'Reset Password'}
                    </Button>
                  )}

                  {/* Back to Sign In */}
                  {React.createElement(motion.div, {
                    initial: { y: 20, opacity: 0 },
                    animate: { y: 0, opacity: 1 },
                    transition: { duration: 0.5, delay: 0.8 }
                  },
                    <div className="text-center pt-2">
                      <Link 
                        href={APP_ROUTE.AUTH.SIGN_IN}
                        className="inline-flex items-center text-sm text-link-default hover:text-link-hover transition-colors duration-200"
                      >
                        Back to Sign In
                      </Link>
                    </div>
                  )}
                </form>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
} 