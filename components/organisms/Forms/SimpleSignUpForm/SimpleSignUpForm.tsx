'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion } from 'framer-motion';

import { signIn } from 'next-auth/react';
import Link from 'next/link';
import { Mail, Lock, User, GraduationCap } from 'lucide-react';

import { Button } from '@/components/atoms/Button/Button';
import { FormField } from '@/components/molecules/FormField/FormField';
import { InputWithIcon } from '@/components/molecules/InputWithIcon/InputWithIcon';
import { PasswordInput } from '@/components/molecules/PasswordInput/PasswordInput';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { signUpAction } from '@/actions/user.action';
import { cn } from '@/utils/cn';

import { simpleSignUpSchema, SimpleSignUpFormData } from './SimpleSignUpForm.schema';
import { useRouter } from 'next/navigation';
import { Icon } from '@/components/atoms';
import { APP_ROUTE } from '@/constants/route.constant';

export default function SimpleSignUpForm() {
  const router = useRouter();
  const [isRegistering, setIsRegistering] = useState(false);
  const [isSigningIn, setIsSigningIn] = useState(false);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);
  const [registrationError, setRegistrationError] = useState<string | null>(null);
  const [signInError, setSignInError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SimpleSignUpFormData>({
    resolver: zodResolver(simpleSignUpSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  const isLoading = isRegistering || isSigningIn;



  const getButtonText = () => {
    if (isRegistering) {
      return 'Creating Account...';
    }
    if (registrationSuccess && !isSigningIn) {
      return 'Account Created! Signing In...';
    }
    if (isSigningIn) {
      return 'Signing You In...';
    }
    return 'Create Account';
  };



  const onSubmit = async (data: SimpleSignUpFormData) => {
    try {
      // Clear previous states
      setRegistrationError(null);
      setSignInError(null);
      setRegistrationSuccess(false);

      // Step 1: Call server action for registration
      setIsRegistering(true);

      // Create FormData for server action
      const formData = new FormData();
      formData.append('name', data.name);
      formData.append('email', data.email);
      formData.append('password', data.password);

      let registrationResponse;
      try {
        registrationResponse = await signUpAction(formData);
      } catch (actionError: any) {
        setIsRegistering(false);
        setRegistrationError('Registration service unavailable. Please try again later.');
        return;
      }
      
      setIsRegistering(false);

      if (registrationResponse.status === 'error') {
        setRegistrationError(typeof registrationResponse.message === 'string'
          ? registrationResponse.message
          : 'Registration failed. Please try again.');
        return;
      }

      // Registration successful - show success state briefly
      setRegistrationSuccess(true);

      // Small delay to show success message
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Step 2: Sign in with NextAuth after successful registration
      setIsSigningIn(true);

      let signInResult;
      try {
        signInResult = await signIn('credentials', {
        email: data.email,
        password: data.password,
        redirect: false,
      });

      setIsSigningIn(false);

      if (signInResult && !signInResult.error) {
        // Clear any previous errors - user is now signed in successfully
        setSignInError(null);

        // Add a small delay to ensure session is fully synchronized
        await new Promise(resolve => setTimeout(resolve, 500));

        // Refresh the router to ensure layout re-renders with updated session
        // router.refresh();
        window.location.href = '/manage-worksheet';
        // redirect to home page and let the role-based redirection in app/page.tsx handle it
        // router.push('/');
      } else {
        // Handle sign-in errors - account was created but sign-in failed
        setSignInError(
          `Account created successfully, but automatic sign-in failed. ${signInResult?.error || 'Please try signing in manually using the sign-in page.'}`
        );
      }
      } catch (signInError: any) {
        console.error('Error during sign-in process:', signInError);
        setIsSigningIn(false);
        setSignInError('Unable to sign in automatically. Please try signing in manually using the sign-in page.');
      }

    } catch (error: any) {
      // Determine which phase the error occurred in
      if (isRegistering) {
        setRegistrationError('An unexpected error occurred during registration. Please try again.');
        setIsRegistering(false);
      } else if (isSigningIn) {
        setSignInError('An unexpected error occurred during sign-in. Please try again.');
        setIsSigningIn(false);
      } else {
        setRegistrationError('An unexpected error occurred during the sign-up process.');
      }
    }
  };

  return (
    <div className="w-full max-w-md mx-auto px-4 sm:px-0">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="space-y-4 sm:space-y-6">
          {/* Registration Success Alert */}
          {registrationSuccess && !signInError && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <AlertMessage
                type="success"
                message="Account created successfully! Signing you in..."
              />
            </motion.div>
          )}

          {/* Registration Error Alert */}
          {registrationError && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <AlertMessage
                type="error"
                message={registrationError}
              />
            </motion.div>
          )}

          {/* Sign-in Error Alert */}
          {signInError && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <AlertMessage
                type="error"
                message={signInError}
              />
            </motion.div>
          )}

          {/* Main Sign Up Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <div className="relative">
              {/* Gradient Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-section-bg-accent via-background-default to-background-subtle rounded-xl sm:rounded-2xl"></div>

              {/* Glass Effect Overlay */}
              <div className="relative bg-background-default/80 backdrop-blur-sm border border-white/20 rounded-xl sm:rounded-2xl shadow-xl shadow-text-primary/10 p-4 sm:p-6">
                {/* Decorative Elements */}
                <div className="absolute top-0 right-0 w-24 sm:w-32 h-24 sm:h-32 bg-gradient-to-br from-link-default/10 to-purple-400/10 rounded-full -mr-12 sm:-mr-16 -mt-12 sm:-mt-16"></div>
                <div className="absolute bottom-0 left-0 w-20 sm:w-24 h-20 sm:h-24 bg-gradient-to-tr from-green-400/10 to-link-default/10 rounded-full -ml-10 sm:-ml-12 -mb-10 sm:-mb-12"></div>

                {/* Logo and Header */}
                <div className="text-center mb-4 sm:mb-6 relative z-10">
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    <h1 className="text-lg sm:text-xl font-semibold flex max-sm:flex-col max-sm:gap-0.5 justify-center items-center gap-2 text-text-primary mb-1">
                      Join <Icon variant="logo" size={40} className=" max-sm:[w-30]" />
                    </h1>
                  </motion.div>
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    <p className="text-xs sm:text-sm text-text-secondary">
                      Transform your teaching with AI-powered worksheets
                    </p>
                  </motion.div>
                </div>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-3 sm:space-y-4 relative z-10" role="form" aria-label="Sign up form">
                  {/* Name Field */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    <FormField
                      label="Full Name"
                      error={errors.name?.message}
                      required
                    >
                      <InputWithIcon
                        type="text"
                        placeholder="Enter your full name"
                        leftIcon={<User size={16} className="sm:size-[18px] text-link-default" />}
                        className={cn(
                          'h-10 sm:h-12 text-sm sm:text-base border-2 rounded-lg sm:rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
                          'bg-background-default/50 backdrop-blur-sm',
                          errors.name
                            ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                            : 'border-gray-200 focus:border-link-default'
                        )}
                        {...register('name')}
                      />
                    </FormField>
                  </motion.div>

                  {/* Email Field */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                  >
                    <FormField
                      label="Email Address"
                      error={errors.email?.message}
                      required
                    >
                      <InputWithIcon
                        type="email"
                        placeholder="Enter your email address"
                        leftIcon={<Mail size={16} className="sm:size-[18px] text-link-default" />}
                        className={cn(
                          'h-10 sm:h-12 text-sm sm:text-base border-2 rounded-lg sm:rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
                          'bg-background-default/50 backdrop-blur-sm',
                          errors.email
                            ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                            : 'border-gray-200 focus:border-link-default'
                        )}
                        {...register('email')}
                      />
                    </FormField>
                  </motion.div>

                  {/* Password Field */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                  >
                    <FormField
                      label="Password"
                      error={errors.password?.message}
                      required
                    >
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                          <Lock size={16} className="sm:size-[18px] text-link-default" />
                        </div>
                        <PasswordInput
                          placeholder="Enter your password (min. 8 characters)"
                          hasLeftIcon={true}
                          className={cn(
                            'h-10 sm:h-12 text-sm sm:text-base border-2 rounded-lg sm:rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
                            'bg-background-default/50 backdrop-blur-sm',
                            errors.password
                              ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                              : 'border-gray-200 focus:border-link-default'
                          )}
                          {...register('password')}
                        />
                      </div>
                    </FormField>
                  </motion.div>

                  {/* Confirm Password Field */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.65 }}
                  >
                    <FormField
                      label="Confirm Password"
                      error={errors.confirmPassword?.message}
                      required
                    >
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                          <Lock size={16} className="sm:size-[18px] text-link-default" />
                        </div>
                        <PasswordInput
                          placeholder="Confirm your password"
                          hasLeftIcon={true}
                          className={cn(
                            'h-10 sm:h-12 text-sm sm:text-base border-2 rounded-lg sm:rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
                            'bg-background-default/50 backdrop-blur-sm',
                            errors.confirmPassword
                              ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                              : 'border-gray-200 focus:border-link-default'
                          )}
                          {...register('confirmPassword')}
                        />
                      </div>
                    </FormField>
                  </motion.div>

                  {/* Submit Button */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.75 }}
                  >
                    <Button
                      type="submit"
                      variant="primary"
                      className="w-full h-10 sm:h-12 text-sm sm:text-base font-semibold rounded-lg sm:rounded-xl bg-primary-action hover:bg-text-primary transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl focus:ring-4 focus:ring-accent-bg-light focus:ring-offset-2 border-0 text-background-default"
                      isLoading={isLoading}
                      disabled={isLoading}
                      aria-label={getButtonText()}
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center space-x-2">
                          <span className="loading loading-spinner loading-xs sm:loading-sm"></span>
                          <span>{getButtonText()}</span>
                        </div>
                      ) : (
                        'Create Account'
                      )}
                    </Button>
                  </motion.div>
                </form>
              </div>
            </div>
          </motion.div>

          {/* Navigation to Sign In */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.85 }}
          >
            <div className="text-center pt-4 sm:pt-6 border-t border-gray-200">
              <p className="text-sm text-text-secondary">
                Already have an account?{' '}
                <Link
                  href="/auth/sign-in"
                  className="text-link-default hover:text-link-hover font-medium transition-colors"
                >
                  Sign in here
                </Link>
              </p>
            </div>
          </motion.div>

          {/* Student Access Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.95 }}
          >
            <div className="text-center pt-4 sm:pt-6 border-t border-gray-200">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center justify-center mb-2">
                  <GraduationCap className="w-5 h-5 text-yellow-600 mr-2" />
                  <h3 className="text-sm font-semibold text-yellow-800">Student Access</h3>
                </div>
                <p className="text-xs text-yellow-700 mb-3">
                  Are you a student looking to take exams?
                </p>
                <div className="flex flex-col sm:flex-row gap-2 justify-center">
                  <Link
                    href={APP_ROUTE.AUTH.STUDENT.SIGN_IN}
                    className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-yellow-700 bg-white border border-yellow-300 rounded-md hover:bg-yellow-50 transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
                    aria-label="Sign in as a student"
                  >
                    <GraduationCap className="w-4 h-4 mr-1.5" />
                    Student Sign In
                  </Link>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}
