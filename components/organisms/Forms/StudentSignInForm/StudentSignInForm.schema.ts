import { z } from 'zod';
import { requiredString } from '@/utils/zod';

/**
 * Validation schema for student sign-in form
 * Reuses the validation logic from the server action
 */
export const studentSignInFormSchema = z.object({
  email: requiredString.email('Please enter a valid email address'),
  password: requiredString.min(1, 'Password is required'),
});

export type StudentSignInFormData = z.infer<typeof studentSignInFormSchema>;
