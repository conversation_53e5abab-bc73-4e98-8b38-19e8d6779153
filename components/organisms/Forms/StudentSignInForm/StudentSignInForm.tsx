'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { signIn } from 'next-auth/react';
import { motion } from 'framer-motion';
import { Mail, GraduationCap } from 'lucide-react';

import { Button } from '@/components/atoms/Button/Button';
import { FormField } from '@/components/molecules/FormField/FormField';
import { InputWithIcon } from '@/components/molecules/InputWithIcon/InputWithIcon';
import { PasswordInput } from '@/components/molecules/PasswordInput/PasswordInput';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { cn } from '@/utils/cn';

import { studentSignInFormSchema, StudentSignInFormData } from './StudentSignInForm.schema';

export default function StudentSignInForm() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<StudentSignInFormData>({
    resolver: zodResolver(studentSignInFormSchema),
  });

  const onSubmit = async (data: StudentSignInFormData) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await signIn('credentials', {
        email: data.email,
        password: data.password,
        redirect: false,
      });

      if (response?.error) {
        setError(response.error);
        return;
      }

      // Show success message
      setSuccess(true);
      reset();

      // Add a small delay to ensure session is fully synchronized
      await new Promise(resolve => setTimeout(resolve, 300));

      // Redirect to home page (role-based routing will handle student dashboard)
      window.location.href = '/';
    } catch (err: any) {
      setError(err.message || 'An error occurred during sign in');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {/* Header */}
      {React.createElement(motion.div, {
        initial: { y: -20, opacity: 0 },
        animate: { y: 0, opacity: 1 },
        transition: { duration: 0.5 }
      },
        <div className="text-center mb-6 sm:mb-8">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-accent-bg-light rounded-full">
              <GraduationCap size={32} className="text-primary-action" />
            </div>
          </div>
          <h1 className="text-2xl sm:text-3xl font-bold text-text-primary mb-2">
            Student Sign In
          </h1>
          <p className="text-text-secondary text-sm sm:text-base">
            Sign in to access your educational resources and worksheets
          </p>
        </div>
      )}

      {/* Success Message */}
      {success && (
        React.createElement(motion.div, {
          initial: { opacity: 0, y: -10 },
          animate: { opacity: 1, y: 0 }
        },
          <div className="mb-4">
            <AlertMessage
              type="success"
              message="Signed in successfully."
            />
            <p className="text-xs text-text-secondary mt-2 text-center">
              Redirecting you to your account...
            </p>
          </div>
        )
      )}

      {/* Error Message */}
      {!success && error && (
        React.createElement(motion.div, {
          initial: { opacity: 0, y: -10 },
          animate: { opacity: 1, y: 0 }
        },
          <div className="mb-4">
            <AlertMessage
              type="error"
              message={error}
            />
          </div>
        )
      )}

      {/* Form */}
      {React.createElement(motion.div, {
        initial: { y: 20, opacity: 0 },
        animate: { y: 0, opacity: 1 },
        transition: { duration: 0.5, delay: 0.2 }
      },
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="space-y-4"
        >
        {/* Email Field */}
        <FormField
          label="Email Address"
          error={errors.email?.message}
          required
        >
          <InputWithIcon
            type="email"
            placeholder="Enter your email address"
            leftIcon={<Mail size={16} className="text-link-default" />}
            className={cn(
              'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
              'bg-background-default/50 backdrop-blur-sm',
              errors.email && 'border-red-500 focus:border-red-500'
            )}
            {...register('email')}
          />
        </FormField>

        {/* Password Field */}
        <FormField
          label="Password"
          error={errors.password?.message}
          required
        >
          <PasswordInput
            placeholder="Enter your password"
            className={cn(
              'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
              'bg-background-default/50 backdrop-blur-sm',
              errors.password && 'border-red-500 focus:border-red-500'
            )}
            {...register('password')}
          />
        </FormField>

        {/* Submit Button */}
        <Button
          type="submit"
          variant="primary"
          className="w-full h-12 text-base font-semibold rounded-xl"
          isLoading={isLoading}
          disabled={isLoading || success}
        >
          {isLoading ? 'Signing In...' : 'Sign In'}
        </Button>
        </form>
      )}

      {/* Help Text */}
      {React.createElement(motion.div, {
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        transition: { duration: 0.5, delay: 0.4 }
      },
        <div className="text-center mt-6">
          <p className="text-text-secondary text-sm">
            Need help accessing your account?{' '}
            <button
              type="button"
              onClick={() => router.push('/auth/forgot-password')}
              className="text-link-default hover:text-link-hover font-medium transition-colors"
            >
              Reset password
            </button>
          </p>
        </div>
      )}
    </div>
  );
}
