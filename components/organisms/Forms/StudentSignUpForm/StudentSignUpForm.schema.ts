import { z } from 'zod';
import { requiredString } from '@/utils/zod';

/**
 * Validation schema for student sign-up form
 * Reuses the validation logic from the server action
 */
export const studentSignUpFormSchema = z.object({
  name: requiredString.min(1, 'Name is required').max(100, 'Name is too long'),
  email: requiredString.email('Please enter a valid email address'),
  password: requiredString.min(8, 'Password must be at least 8 characters'),
  confirmPassword: requiredString.min(1, 'Please confirm your password'),
}).refine(
  (data) => data.password === data.confirmPassword,
  {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  }
);

export type StudentSignUpFormData = z.infer<typeof studentSignUpFormSchema>;
