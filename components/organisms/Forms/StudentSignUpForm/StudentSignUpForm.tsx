'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useFormState } from 'react-dom';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Mail, User, GraduationCap } from 'lucide-react';

import { Button } from '@/components/atoms/Button/Button';
import { FormField } from '@/components/molecules/FormField/FormField';
import { InputWithIcon } from '@/components/molecules/InputWithIcon/InputWithIcon';
import { PasswordInput } from '@/components/molecules/PasswordInput/PasswordInput';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { studentSignUpAction } from '@/actions/auth.action';
import { cn } from '@/utils/cn';

import { studentSignUpFormSchema, StudentSignUpFormData } from './StudentSignUpForm.schema';

const initialState = {
  success: false,
  message: '',
  errors: {},
};

export default function StudentSignUpForm() {
  const router = useRouter();
  const [state, formAction] = useFormState(studentSignUpAction, initialState);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<StudentSignUpFormData>({
    resolver: zodResolver(studentSignUpFormSchema),
  });

  // Handle successful registration
  useEffect(() => {
    if (state.success) {
      // Reset form
      reset();

      // Redirect to student sign-in page after a short delay
      const timeoutId = setTimeout(() => {
        router.push('/auth/student/sign-in');
      }, 2000);

      // Cleanup function to clear timeout if component unmounts
      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, [state.success, reset, router]);

  const onSubmit = async (data: StudentSignUpFormData) => {
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('email', data.email);
    formData.append('password', data.password);
    formData.append('confirmPassword', data.confirmPassword);
    
    formAction(formData);
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {/* Header */}
      <motion.div
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-6 sm:mb-8"
      >
        <div className="flex justify-center mb-4">
          <div className="p-3 bg-accent-bg-light rounded-full">
            <GraduationCap size={32} className="text-primary-action" />
          </div>
        </div>
        <h1 className="text-2xl sm:text-3xl font-bold text-text-primary mb-2">
          Join as a Student
        </h1>
        <p className="text-text-secondary text-sm sm:text-base">
          Create your student account to access educational resources and worksheets
        </p>
      </motion.div>

      {/* Success Message */}
      {state.success && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-4"
        >
          <AlertMessage
            type="success"
            message={state.message}
            className="text-sm"
          />
          <p className="text-xs text-text-secondary mt-2 text-center">
            Redirecting you to sign in...
          </p>
        </motion.div>
      )}

      {/* Error Message */}
      {!state.success && state.message && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-4"
        >
          <AlertMessage
            type="error"
            message={state.message}
            className="text-sm"
          />
        </motion.div>
      )}

      {/* Form */}
      <motion.form
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        onSubmit={handleSubmit(onSubmit)}
        className="space-y-4"
      >
        {/* Name Field */}
        <FormField
          label="Full Name"
          error={errors.name?.message || state.errors?.name?.[0]}
          required
        >
          <InputWithIcon
            type="text"
            placeholder="Enter your full name"
            leftIcon={<User size={16} className="text-link-default" />}
            className={cn(
              'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
              'bg-background-default/50 backdrop-blur-sm',
              (errors.name || state.errors?.name) && 'border-red-500 focus:border-red-500'
            )}
            {...register('name')}
          />
        </FormField>

        {/* Email Field */}
        <FormField
          label="Email Address"
          error={errors.email?.message || state.errors?.email?.[0]}
          required
        >
          <InputWithIcon
            type="email"
            placeholder="Enter your email address"
            leftIcon={<Mail size={16} className="text-link-default" />}
            className={cn(
              'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
              'bg-background-default/50 backdrop-blur-sm',
              (errors.email || state.errors?.email) && 'border-red-500 focus:border-red-500'
            )}
            {...register('email')}
          />
        </FormField>

        {/* Password Field */}
        <FormField
          label="Password"
          error={errors.password?.message || state.errors?.password?.[0]}
          required
        >
          <PasswordInput
            placeholder="Create a strong password"
            className={cn(
              'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
              'bg-background-default/50 backdrop-blur-sm',
              (errors.password || state.errors?.password) && 'border-red-500 focus:border-red-500'
            )}
            {...register('password')}
          />
        </FormField>

        {/* Confirm Password Field */}
        <FormField
          label="Confirm Password"
          error={errors.confirmPassword?.message || state.errors?.confirmPassword?.[0]}
          required
        >
          <PasswordInput
            placeholder="Confirm your password"
            className={cn(
              'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
              'bg-background-default/50 backdrop-blur-sm',
              (errors.confirmPassword || state.errors?.confirmPassword) && 'border-red-500 focus:border-red-500'
            )}
            {...register('confirmPassword')}
          />
        </FormField>

        {/* Submit Button */}
        <Button
          type="submit"
          variant="primary"
          size="lg"
          className="w-full h-12 text-base font-semibold rounded-xl"
          loading={isSubmitting}
          disabled={isSubmitting || state.success}
        >
          {isSubmitting ? 'Creating Account...' : 'Create Student Account'}
        </Button>
      </motion.form>

      {/* Sign In Link */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="text-center mt-6"
      >
        <p className="text-text-secondary text-sm">
          Already have a student account?{' '}
          <button
            type="button"
            onClick={() => router.push('/auth/student/sign-in')}
            className="text-link-default hover:text-link-hover font-medium transition-colors"
          >
            Sign in here
          </button>
        </p>
      </motion.div>
    </div>
  );
}
