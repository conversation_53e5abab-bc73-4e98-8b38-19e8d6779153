'use client';

import React from 'react';
import { IStudentDetailResponse } from '@/apis/userApi';
import { ExamResultsTable } from '@/components/molecules/ExamResultsTable/ExamResultsTable';
import { BookOpen, Trophy, TrendingUp, User, Mail } from 'lucide-react';

export interface StudentDetailViewProps {
  student: IStudentDetailResponse;
}

export const StudentDetailView: React.FC<StudentDetailViewProps> = ({
  student,
}) => {
  const { examResults } = student;
  
  // Calculate stats
  const totalExams = examResults.length;
  const passedExams = examResults.filter(exam => exam.status === 'passed').length;
  const averageScore = totalExams > 0 
    ? examResults.reduce((sum, exam) => sum + exam.percentage, 0) / totalExams 
    : 0;

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Student Profile Information - Mobile Card Layout */}
      <div className="bg-white rounded-xl p-4 sm:p-6 border border-gray-100 shadow-sm">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-8 h-8 bg-primary-action/10 rounded-full flex items-center justify-center">
            <BookOpen className="w-4 h-4 text-primary-action" />
          </div>
          <h2 className="text-lg font-semibold text-text-primary">Student Profile</h2>
        </div>
        
        {/* Mobile: Stack cards vertically, Desktop: Grid */}
        <div className="space-y-4 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-4">
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
            <div className="flex items-start gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <User className="w-5 h-5 text-blue-600" />
              </div>
              <div className="min-w-0 flex-1">
                <label className="text-sm font-medium text-text-secondary block mb-1">Full Name</label>
                <p className="text-base text-text-primary font-medium break-words">{student.name}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
            <div className="flex items-start gap-3">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                <Mail className="w-5 h-5 text-green-600" />
              </div>
              <div className="min-w-0 flex-1">
                <label className="text-sm font-medium text-text-secondary block mb-1">Email Address</label>
                <p className="text-sm sm:text-base text-text-primary break-all leading-relaxed">{student.email}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Statistics - Enhanced Card Layout */}
      {totalExams > 0 && (
        <div className="bg-white rounded-xl p-4 sm:p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center gap-2 mb-4">
            <div className="w-8 h-8 bg-primary-action/10 rounded-full flex items-center justify-center">
              <TrendingUp className="w-4 h-4 text-primary-action" />
            </div>
            <h2 className="text-lg font-semibold text-text-primary">Performance Overview</h2>
          </div>
          
          {/* Mobile: Stack cards vertically, Tablet: 2 cols, Desktop: 3 cols */}
          <div className="space-y-3 sm:space-y-0 sm:grid sm:grid-cols-2 lg:grid-cols-3 sm:gap-4">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl p-4 border border-blue-200 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-blue-700 mb-1">{totalExams}</div>
                  <p className="text-sm text-blue-600 font-medium">Total Exams</p>
                </div>
                <div className="w-12 h-12 bg-blue-200/50 rounded-full flex items-center justify-center">
                  <BookOpen className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl p-4 border border-green-200 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-green-700 mb-1">{passedExams}</div>
                  <p className="text-sm text-green-600 font-medium">Passed Exams</p>
                </div>
                <div className="w-12 h-12 bg-green-200/50 rounded-full flex items-center justify-center">
                  <Trophy className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl p-4 border border-purple-200 shadow-sm hover:shadow-md transition-shadow sm:col-span-2 lg:col-span-1">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-purple-700 mb-1">{averageScore.toFixed(1)}%</div>
                  <p className="text-sm text-purple-600 font-medium">Average Score</p>
                </div>
                <div className="w-12 h-12 bg-purple-200/50 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Exam Results - Card Layout */}
      <div className="bg-white rounded-xl p-4 sm:p-6 border border-gray-100 shadow-sm">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-8 h-8 bg-primary-action/10 rounded-full flex items-center justify-center">
            <Trophy className="w-4 h-4 text-primary-action" />
          </div>
          <h2 className="text-lg font-semibold text-text-primary">Exam Results</h2>
        </div>
        
        {totalExams > 0 ? (
          <ExamResultsTable results={examResults} />
        ) : (
          <div className="text-center py-8 sm:py-12">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
              <Trophy className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-text-primary mb-2">No Exam Results</h3>
            <p className="text-text-secondary">This student hasn&apos;t completed any exams yet.</p>
          </div>
        )}
      </div>
    </div>
  );
}; 