'use client';

import React, { useMemo, useState } from 'react';
import {
  ColumnDef,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import CustomTable from '@/components/molecules/CustomTable/CustomTable';
import { TablePagination } from '@/components/molecules/TablePagination/TablePagination';
import { IStudentResponse } from '@/apis/userApi';
import { EUserRole } from '@/config/enums/user';
import { useSession } from 'next-auth/react';
import { TableSkeleton } from '@/components/molecules/CustomTable/TableSkeleton';
import { ErrorDisplay } from '@/components/molecules/ErrorDisplay/ErrorDisplay';
import { Button } from '@/components/atoms/Button/Button';
import { Trash2, Eye } from 'lucide-react';
import Link from 'next/link';
import { APP_ROUTE } from '@/constants/route.constant';
import { ConfirmationDialog } from '@/components/molecules/ConfirmationDialog/ConfirmationDialog';
import { useToast } from '@/stores/jotai/subscription/useToast';
import { handleDeleteStudentAction } from '@/actions/user.action';

interface StudentListTableProps {
  students: IStudentResponse[];
  isLoading: boolean;
  error: any;
}

export const StudentListTable: React.FC<StudentListTableProps> = ({
  students,
  isLoading,
  error,
}) => {
  const { data: session } = useSession();
  const userRole = session?.user?.role;
  const [sorting, setSorting] = useState<SortingState>([]);
  const [deletingStudent, setDeletingStudent] = useState<IStudentResponse | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const { showSuccess, showError } = useToast();

  const handleDelete = async () => {
    if (!deletingStudent) return;
    setIsDeleting(true);
    try {
      const response = await handleDeleteStudentAction(deletingStudent.id);
      if (response.status === 'success') {
        showSuccess('Student deleted successfully');
      } else {
        const errorMessage = Array.isArray(response.message)
          ? response.message.map(e => e.constraints).join(', ')
          : response.message || 'Failed to delete student';
        showError(errorMessage);
      }
    } catch (error: any) {
      showError(error.message || 'An unexpected error occurred');
    } finally {
      setIsDeleting(false);
      setDeletingStudent(null);
    }
  };

  const columns = useMemo<ColumnDef<IStudentResponse, any>[]>(() => {
    const baseColumns: ColumnDef<IStudentResponse, any>[] = [
      {
        accessorKey: 'name',
        header: 'Name',
        enableSorting: true,
      },
      {
        accessorKey: 'email',
        header: 'Email',
      },
      {
        accessorKey: 'createdAt',
        header: 'Created At',
        cell: ({ row }) => new Date(row.original.createdAt).toLocaleDateString(),
        enableSorting: true,
      },
      {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => (
          <div className="flex items-center gap-2">
            <Link href={`${APP_ROUTE.STUDENT_MANAGEMENT.DETAIL}/${row.original.id}`}>
              <Button variant="outline">
                <Eye size={16} />
              </Button>
            </Link>

            <Button variant="error" onClick={() => setDeletingStudent(row.original)}>
              <Trash2 size={16} />
            </Button>
          </div>
        ),
      },
    ];

    if (userRole === EUserRole.ADMIN) {
      baseColumns.splice(2, 0, {
        accessorKey: 'schoolId',
        header: 'School ID',
      });
    }

    return baseColumns;
  }, [userRole]);

  const table = useReactTable({
    data: students,
    columns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  if (isLoading) {
    return <TableSkeleton />;
  }

  if (error) {
    return <ErrorDisplay error="Failed to load students" onRetry={() => {}} />;
  }
  
  if (students.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        No students found.
      </div>
    );
  }

  return (
    <div>
      <CustomTable
        columns={columns}
        tableData={table.getRowModel().rows.map((row) => row.original)}
        clientSidePagination
        clientSideSorting
        sortingState={sorting}
        onSortingChange={setSorting}
      />
      <TablePagination
        currentPage={table.getState().pagination.pageIndex + 1}
        totalPages={table.getPageCount()}
        rowsPerPage={table.getState().pagination.pageSize}
        totalItems={students.length}
        onPageChange={(page) => table.setPageIndex(page - 1)}
        onRowsPerPageChange={(e) => {
          table.setPageSize(Number(e.target.value));
        }}
      />
      <ConfirmationDialog
        isOpen={!!deletingStudent}
        onClose={() => setDeletingStudent(null)}
        onConfirm={handleDelete}
        title="Delete Student"
        description="Are you sure you want to delete this student? This action cannot be undone."
        confirmLabel="Delete"
        confirmVariant="danger"
        isLoading={isDeleting}
      />
    </div>
  );
}; 