import { ColumnDef } from '@tanstack/react-table';
import { ITransaction } from '@/types/transaction';
import { CheckCircle, XCircle, AlertCircle, Eye, Download, CreditCard, MinusCircle } from 'lucide-react';
import React from 'react';

/**
 * Renders a colored badge with icon depending on transaction status.
 */
function StatusBadge({ status }: { status: ITransaction['status'] }) {
  const statusConfig: Record<ITransaction['status'], { icon: React.ElementType; className: string; label: string }> = {
    paid: {
      icon: CheckCircle,
      className: 'bg-green-50 text-green-700 border-green-200',
      label: 'Paid'
    },
    failed: {
      icon: XCircle,
      className: 'bg-red-50 text-red-700 border-red-200',
      label: 'Failed'
    },
    pending: {
      icon: AlertCircle,
      className: 'bg-yellow-50 text-yellow-700 border-yellow-200',
      label: 'Pending'
    },
    open: {
      icon: AlertCircle,
      className: 'bg-blue-50 text-blue-700 border-blue-200',
      label: 'Open'
    },
    void: {
      icon: MinusCircle,
      className: 'bg-gray-50 text-gray-700 border-gray-200',
      label: 'Void'
    },
    uncollectible: {
      icon: XCircle,
      className: 'bg-red-50 text-red-700 border-red-200',
      label: 'Uncollectible'
    },
  };

  const config = statusConfig[status] ?? statusConfig.pending;
  const Icon = config.icon;

  return (
    <span
      className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border ${config.className}`}
    >
      <Icon className="w-3 h-3" />
      {config.label}
    </span>
  );
}

/**
 * Renders the payment method with an icon.
 */
function PaymentMethod({ method }: { method?: string }) {
  if (!method || method === 'N/A') {
    return <span className="text-text-tertiary">N/A</span>;
  }

  const formattedMethod = method.charAt(0).toUpperCase() + method.slice(1);

  return (
    <div className="flex items-center gap-2 text-text-secondary">
      <CreditCard className="w-4 h-4" />
      <span>{formattedMethod}</span>
    </div>
  );
}

const formatAmount = (amount: number, currency: string) =>
  new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount / 100);

const formatDate = (dateString: string) =>
  new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(new Date(dateString));

/**
 * Column definitions for the transactions table.
 */
export const transactionColumns: ColumnDef<ITransaction, unknown>[] = [
  {
    accessorKey: 'date',
    header: 'Date',
    cell: ({ row }) => formatDate(row.original.date),
    enableSorting: true,
    theadColClass: 'w-40',
  } as ColumnDef<ITransaction, unknown> & { theadColClass?: string },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => row.original.description,
    theadColClass: 'min-w-[200px]',
  } as ColumnDef<ITransaction, unknown> & { theadColClass?: string },
  {
    accessorKey: 'paymentMethod',
    header: 'Payment Method',
    cell: ({ row }) => <PaymentMethod method={row.original.paymentMethod} />,
    theadColClass: 'w-48',
  } as ColumnDef<ITransaction, unknown> & { theadColClass?: string },
  {
    accessorKey: 'amount',
    header: 'Amount',
    cell: ({ row }) => formatAmount(row.original.amount, row.original.currency),
    enableSorting: true,
    theadColClass: 'w-32 text-right',
  } as ColumnDef<ITransaction, unknown> & { theadColClass?: string },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => <StatusBadge status={row.original.status} />,
    theadColClass: 'w-32',
  } as ColumnDef<ITransaction, unknown> & { theadColClass?: string },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => (
      <div className="flex items-center gap-1 sm:gap-2">
        <a
          href={row.original.invoiceUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="btn btn-ghost btn-sm btn-square min-h-[44px] min-w-[44px] sm:min-h-[32px] sm:min-w-[32px]"
          aria-label="View Invoice"
        >
          <Eye className="w-4 h-4" />
        </a>
        <a
          href={row.original.receiptUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="btn btn-ghost btn-sm btn-square min-h-[44px] min-w-[44px] sm:min-h-[32px] sm:min-w-[32px]"
          aria-label="Download Receipt"
        >
          <Download className="w-4 h-4" />
        </a>
      </div>
    ),
    theadColClass: 'w-28 text-center',
  } as ColumnDef<ITransaction, unknown> & { theadColClass?: string },
] as unknown as ColumnDef<ITransaction, unknown>[]; 