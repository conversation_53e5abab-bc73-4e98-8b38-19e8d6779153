import React from 'react';
import { cn } from '@/utils/cn';
import { SimpleProgressBar } from '@/components/molecules/ProgressBar';
import { Infinity } from 'lucide-react';


// As per task, usageData will have usage and limit.
// Let's define it here for now.
export interface UsageData {
  usage: number;
  limit: number;
}

export interface UsageLimitCardProps {
  title: string;
  usageData: UsageData;
  className?: string;
  isLoading?: boolean;
}

const UsageLimitCard: React.FC<UsageLimitCardProps> = ({
  title,
  usageData,
  className,
  isLoading = false,
}) => {
  if (isLoading) {
    return (
      <div className={cn('card bg-base-100 shadow-sm', className)}>
        <div className="card-body">
          <div className="skeleton h-6 w-1/2 mb-4"></div>
          <div className="skeleton h-4 w-1/4 mb-2"></div>
          <div className="skeleton h-2.5 w-full"></div>
        </div>
      </div>
    );
  }

  const { usage, limit } = usageData;
  const isUnlimited = limit === -1 || limit === 0;
  const percentage = isUnlimited ? 100 : Math.round((usage / limit) * 100);

  return (
    <div className={cn('card bg-base-100 shadow-sm', className)}>
      <div className="card-body p-4">
        <h3 className="card-title text-base">{title}</h3>
        <div className="text-sm text-gray-500 mb-2">
          {isUnlimited ? (
            <span className='flex items-center gap-1'>
              {usage} / <Infinity size={17} />
            </span>
          ) : (
            <span>
              {usage} / {limit}
            </span>
          )}
        </div>
        <SimpleProgressBar value={percentage} />
      </div>
    </div>
  );
};

export default UsageLimitCard; 