'use client';

import React, { useState, useEffect } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/atoms/Popover';
import { Button } from '@/components/atoms/Button/Button';
import UsageLimitCard, { UsageData } from './UsageLimitCard';
import { handleGetUsageLimitsAction } from '@/actions/usage.action';
import { IUsageLimit } from '@/types/usage';
import { PopoverArrow } from '@radix-ui/react-popover';

const UsageLimitPopover: React.FC = () => {
  const [usageLimits, setUsageLimits] = useState<IUsageLimit[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUsage = async () => {
      setIsLoading(true);
      setError(null);
      const response = await handleGetUsageLimitsAction();
      if (response.status === 'success') {
        setUsageLimits(response.data || []);
      } else {
        setError(response.message as string);
      }
      setIsLoading(false);
    };

    fetchUsage();
  }, []);

  const renderContent = () => {
    if (isLoading) {
      return (
        <>
          <UsageLimitCard title="Worksheets" usageData={{ usage: 0, limit: 0 }} isLoading />
          <UsageLimitCard title="Questions per Worksheet" usageData={{ usage: 0, limit: 0 }} isLoading className="mt-4" />
        </>
      );
    }

    if (error) {
      return <p className="text-red-500">{error}</p>;
    }

    if (usageLimits.length === 0) {
      return <p>No usage data available.</p>;
    }

    return usageLimits.map(limitData => (
      <UsageLimitCard
        key={limitData.feature}
        title={limitData.feature === 'maxWorksheets' ? 'Worksheets' : 'Questions per Worksheet'}
        usageData={{ usage: limitData.usage, limit: limitData.limit }}
        className="mb-4 last:mb-0"
      />
    ));
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button iconProps={{ size: 3.5, variant: 'infinity' }} className="w-fit text-white order-3">
          Your Usage Limits
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="grid gap-4">
          <div className="space-y-2">
            <h4 className="font-medium leading-none">Daily Usage Limits</h4>
            <p className="text-sm text-muted-foreground">
              Your current usage for today. This resets daily.
            </p>
          </div>
          <div className="grid gap-2">
            {renderContent()}
          </div>
        </div>
        <PopoverArrow className="PopoverArrow fill-gray-300" />
      </PopoverContent>
    </Popover>
  );
};

export default UsageLimitPopover; 