'use client';

import React from 'react';
import { Math<PERSON>Renderer } from '@/components/atoms/MathMLRenderer';
import { MathContentRenderer } from '@/components/molecules/MathContentRenderer';

/**
 * Test component to verify MathML rendering functionality
 * This component can be used for development and testing purposes
 */
export const MathMLTest: React.FC = () => {
  const testExpressions = [
    {
      title: 'Simple Equation',
      mathml: '<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>x</mi><mo>=</mo><mn>7</mn></math>',
      description: 'Basic variable assignment'
    },
    {
      title: 'Fraction',
      mathml: '<math xmlns="http://www.w3.org/1998/Math/MathML"><mfrac><mi>a</mi><mi>b</mi></mfrac></math>',
      description: 'Simple fraction a/b'
    },
    {
      title: 'Quadratic Formula',
      mathml: `<math xmlns="http://www.w3.org/1998/Math/MathML">
        <mi>x</mi><mo>=</mo>
        <mfrac>
          <mrow>
            <mo>-</mo><mi>b</mi><mo>±</mo>
            <msqrt>
              <msup><mi>b</mi><mn>2</mn></msup>
              <mo>-</mo><mn>4</mn><mi>a</mi><mi>c</mi>
            </msqrt>
          </mrow>
          <mrow><mn>2</mn><mi>a</mi></mrow>
        </mfrac>
      </math>`,
      description: 'Complex quadratic formula with square root and superscript'
    },
    {
      title: 'Superscript and Subscript',
      mathml: '<math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>x</mi><mn>1</mn></msub><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup></math>',
      description: 'Variables with subscript and superscript'
    }
  ];

  const mixedContentExamples = [
    {
      title: 'Question with Math',
      content: 'Solve for x in the equation: <math xmlns="http://www.w3.org/1998/Math/MathML"><mi>x</mi><mo>+</mo><mn>5</mn><mo>=</mo><mn>12</mn></math>',
      description: 'Text with embedded mathematical expression'
    },
    {
      title: 'Multiple Choice Option',
      content: 'A) <math xmlns="http://www.w3.org/1998/Math/MathML"><mi>x</mi><mo>=</mo><mn>7</mn></math>',
      description: 'Answer option with math'
    },
    {
      title: 'Complex Expression',
      content: 'The area of a circle is <math xmlns="http://www.w3.org/1998/Math/MathML"><mi>A</mi><mo>=</mo><mi>π</mi><msup><mi>r</mi><mn>2</mn></msup></math> where r is the radius.',
      description: 'Mathematical formula within explanatory text'
    }
  ];

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">MathML Rendering Test</h1>
        <p className="text-gray-600">Testing mathematical expression rendering with fallback support</p>
      </div>

      {/* Individual MathML Expressions */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-gray-800 border-b border-gray-200 pb-2">
          Individual MathML Expressions
        </h2>
        
        <div className="grid gap-6 md:grid-cols-2">
          {testExpressions.map((expr, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-medium text-gray-900 mb-2">{expr.title}</h3>
              <p className="text-sm text-gray-600 mb-4">{expr.description}</p>
              
              <div className="bg-gray-50 border border-gray-200 rounded p-4 mb-4">
                <div className="text-center text-xl">
                  <MathMLRenderer
                    content={expr.mathml}
                    fallbackToText={true}
                    onRenderError={(error) => {
                      console.warn(`Math rendering error for ${expr.title}:`, error);
                    }}
                  />
                </div>
              </div>
              
              <details className="text-sm">
                <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                  View MathML Source
                </summary>
                <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                  {expr.mathml}
                </pre>
              </details>
            </div>
          ))}
        </div>
      </section>

      {/* Mixed Content Examples */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-gray-800 border-b border-gray-200 pb-2">
          Mixed Content (HTML + MathML)
        </h2>
        
        <div className="space-y-6">
          {mixedContentExamples.map((example, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-medium text-gray-900 mb-2">{example.title}</h3>
              <p className="text-sm text-gray-600 mb-4">{example.description}</p>
              
              <div className="bg-gray-50 border border-gray-200 rounded p-4 mb-4">
                <MathContentRenderer
                  content={example.content}
                  className="text-base leading-relaxed"
                  fallbackToText={true}
                  onRenderError={(error) => {
                    console.warn(`Math rendering error for ${example.title}:`, error);
                  }}
                />
              </div>
              
              <details className="text-sm">
                <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                  View Source
                </summary>
                <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                  {example.content}
                </pre>
              </details>
            </div>
          ))}
        </div>
      </section>

      {/* Browser Support Information */}
      <section className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-blue-900 mb-4">Browser Support Information</h2>
        
        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <h3 className="font-medium text-blue-800 mb-2">Native MathML Support</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>✅ Firefox (all versions)</li>
              <li>✅ Safari 14.1+</li>
              <li>⚠️ Chrome 109+ (with flag)</li>
              <li>⚠️ Edge 109+ (with flag)</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-blue-800 mb-2">Fallback Support</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>✅ All browsers</li>
              <li>✅ Text-based representation</li>
              <li>✅ Screen reader compatible</li>
              <li>✅ Graceful degradation</li>
            </ul>
          </div>
        </div>
        
        <div className="mt-4 p-3 bg-blue-100 rounded">
          <p className="text-sm text-blue-800">
            <strong>Current Browser:</strong> {typeof window !== 'undefined' && 'MathMLElement' in window 
              ? '✅ Supports MathML natively' 
              : '⚠️ Using text fallback'}
          </p>
        </div>
      </section>

      {/* Usage Instructions */}
      <section className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-green-900 mb-4">Usage Instructions</h2>
        
        <div className="space-y-4 text-sm text-green-800">
          <div>
            <h3 className="font-medium mb-2">For Individual MathML Expressions:</h3>
            <pre className="bg-green-100 p-2 rounded text-xs overflow-x-auto">
{`<MathMLRenderer
  content="<math>...</math>"
  fallbackToText={true}
  onRenderError={(error) => console.warn(error)}
/>`}
            </pre>
          </div>
          
          <div>
            <h3 className="font-medium mb-2">For Mixed Content (HTML + MathML):</h3>
            <pre className="bg-green-100 p-2 rounded text-xs overflow-x-auto">
{`<MathContentRenderer
  content="Text with <math>...</math> expressions"
  fallbackToText={true}
  onRenderError={(error) => console.warn(error)}
/>`}
            </pre>
          </div>
        </div>
      </section>
    </div>
  );
};