# Document Upload Implementation

This document describes the implementation of the "Upload documents for processing" API and UI components.

## Overview

The implementation includes:
- API client function for document upload
- Next.js API route handler
- React component for document upload UI
- TypeScript interfaces for type safety
- **Role-based access control**: Only ADMIN users can access the feature
- Sidebar menu integration for admin users
- Middleware protection for unauthorized access

## API Specification

Based on the OpenAPI specification, the document upload endpoint:

- **URL**: `POST /documents/upload`
- **Content-Type**: `multipart/form-data`
- **Authentication**: Bearer token required

### Request Parameters

- `files`: Array of binary files (required)
- `title`: String (optional) - Title of the document(s)
- `description`: String (optional) - Description of the document(s)
- `category`: String (optional) - Category of the document(s)

### Response

```json
{
  "statusCode": 200,
  "message": "Documents uploaded successfully",
  "data": {
    "count": 2,
    "results": [
      {
        "documentId": "string",
        "title": "string",
        "description": "string",
        "fileName": "string",
        "mimeType": "string",
        "uploadedAt": "2024-01-01T00:00:00.000Z",
        "category": "string"
      }
    ]
  }
}
```

## Implementation Details

### 1. API Client (`apis/documentUploadApi.ts`)

- Handles multipart/form-data requests
- Includes proper error handling
- Supports authentication via bearer token
- Returns typed responses

### 2. API Route Handler (`app/api/documents/upload/route.ts`)

- Proxies requests to the backend API
- Handles authentication via NextAuth session
- Forwards multipart/form-data without modification
- Provides proper error responses

### 3. UI Component (`components/molecules/DocumentUpload/DocumentUpload.tsx`)

Features:
- Drag and drop file upload
- Multiple file selection
- File preview with size information
- Optional metadata fields (title, description, category)
- Progress indicator during upload
- Success/error message display
- Form validation

### 4. Demo Page (`app/documents/upload/page.tsx`)

- Standalone page demonstrating the upload functionality
- Includes success/error handling callbacks
- Responsive design

## Usage

### Basic Usage

```tsx
import { DocumentUpload } from '@/components/molecules/DocumentUpload';

function MyComponent() {
  const handleSuccess = (response) => {
    console.log('Upload successful:', response);
  };

  const handleError = (error) => {
    console.error('Upload failed:', error);
  };

  return (
    <DocumentUpload
      onSuccess={handleSuccess}
      onError={handleError}
    />
  );
}
```

### API Client Usage

```tsx
import { uploadDocuments } from '@/apis/documentUploadApi';

const handleUpload = async () => {
  const payload = {
    files: selectedFiles,
    title: 'My Documents',
    description: 'Important documents for processing',
    category: 'work'
  };

  const response = await uploadDocuments(payload, accessToken);
  
  if (response.status === 'success') {
    console.log('Upload successful:', response.data);
  } else {
    console.error('Upload failed:', response.message);
  }
};
```

## File Structure

```
├── apis/
│   └── documentUploadApi.ts          # API client function
├── app/
│   ├── api/
│   │   └── documents/
│   │       └── upload/
│   │           └── route.ts          # Next.js API route
│   └── documents/
│       └── upload/
│           └── page.tsx              # Demo page
├── components/
│   └── molecules/
│       └── DocumentUpload/
│           ├── DocumentUpload.tsx    # Main component
│           └── index.ts              # Export file
└── docs/
    └── document-upload-implementation.md  # This documentation
```

## Supported File Types

- PDF (.pdf)
- Microsoft Word (.doc, .docx)
- Text files (.txt)
- Rich Text Format (.rtf)

## Error Handling

The implementation includes comprehensive error handling:

1. **Network errors**: Displayed to user with retry option
2. **Validation errors**: Field-specific error messages
3. **Authentication errors**: Redirect to login if needed
4. **File size/type errors**: Clear feedback on unsupported files

## Security Considerations

- Authentication required for all uploads
- **Role-based access control**: Only ADMIN users can access document upload functionality
- File type validation on client and server
- Proper error handling to avoid information leakage
- Session-based authentication via NextAuth
- Middleware-level route protection for unauthorized access attempts

## Access Control

### Role-Based Access Control (RBAC)

The document upload feature implements strict role-based access control:

- **ADMIN Role**: Full access to document upload functionality
- **All Other Roles**: Access denied with appropriate error messages

### Access Control Layers

1. **Middleware Protection**: Routes are protected at the middleware level
2. **Page-Level Protection**: Client-side role checking with proper error display
3. **API Route Protection**: Server-side authentication validation
4. **UI Integration**: Menu item only appears for admin users

### Unauthorized Access Handling

- **Direct URL Access**: Redirected to home page with access denied message
- **API Access**: Returns 401 Unauthorized for non-admin users
- **Menu Access**: Document Upload menu item only visible to admin users

## Future Enhancements

Potential improvements:
- File compression before upload
- Chunked upload for large files
- Real-time upload progress
- File preview for supported formats
- Batch upload with individual file status
- Integration with document processing pipeline
- **Role expansion**: Consider allowing other roles (e.g., SCHOOL_MANAGER) to upload documents 