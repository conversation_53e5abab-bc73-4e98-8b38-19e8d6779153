'use client';

import { useEffect, useState, useCallback, useRef } from 'react';

export interface AccessibilityOptions {
  announceProgress?: boolean;
  enableKeyboardNavigation?: boolean;
  highContrast?: boolean;
  reducedMotion?: boolean;
}

export interface AccessibilityState {
  isKeyboardNavigating: boolean;
  prefersReducedMotion: boolean;
  prefersHighContrast: boolean;
  screenReaderActive: boolean;
}

/**
 * Custom hook for managing accessibility features in exam components
 */
export const useAccessibility = (options: AccessibilityOptions = {}) => {
  const {
    announceProgress = true,
    enableKeyboardNavigation = true,
    highContrast = false,
    reducedMotion = false
  } = options;

  const [accessibilityState, setAccessibilityState] = useState<AccessibilityState>({
    isKeyboardNavigating: false,
    prefersReducedMotion: false,
    prefersHighContrast: false,
    screenReaderActive: false
  });

  const announcementRef = useRef<HTMLDivElement>(null);
  const keyboardTimeoutRef = useRef<NodeJS.Timeout>();

  // Detect keyboard navigation
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Tab') {
      setAccessibilityState(prev => ({ ...prev, isKeyboardNavigating: true }));
      
      // Clear existing timeout
      if (keyboardTimeoutRef.current) {
        clearTimeout(keyboardTimeoutRef.current);
      }
      
      // Reset keyboard navigation state after 3 seconds of inactivity
      keyboardTimeoutRef.current = setTimeout(() => {
        setAccessibilityState(prev => ({ ...prev, isKeyboardNavigating: false }));
      }, 3000);
    }
  }, []);

  const handleMouseDown = useCallback(() => {
    setAccessibilityState(prev => ({ ...prev, isKeyboardNavigating: false }));
    if (keyboardTimeoutRef.current) {
      clearTimeout(keyboardTimeoutRef.current);
    }
  }, []);

  // Detect user preferences
  useEffect(() => {
    const checkPreferences = () => {
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
      
      // Detect screen reader (basic detection)
      const screenReaderActive = window.navigator.userAgent.includes('NVDA') ||
                                window.navigator.userAgent.includes('JAWS') ||
                                window.speechSynthesis?.speaking ||
                                false;

      setAccessibilityState(prev => ({
        ...prev,
        prefersReducedMotion: reducedMotion || prefersReducedMotion,
        prefersHighContrast: highContrast || prefersHighContrast,
        screenReaderActive
      }));
    };

    checkPreferences();

    // Listen for preference changes
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');

    const handleReducedMotionChange = (e: MediaQueryListEvent) => {
      setAccessibilityState(prev => ({ ...prev, prefersReducedMotion: e.matches }));
    };

    const handleHighContrastChange = (e: MediaQueryListEvent) => {
      setAccessibilityState(prev => ({ ...prev, prefersHighContrast: e.matches }));
    };

    reducedMotionQuery.addEventListener('change', handleReducedMotionChange);
    highContrastQuery.addEventListener('change', handleHighContrastChange);

    return () => {
      reducedMotionQuery.removeEventListener('change', handleReducedMotionChange);
      highContrastQuery.removeEventListener('change', handleHighContrastChange);
    };
  }, [reducedMotion, highContrast]);

  // Set up keyboard navigation detection
  useEffect(() => {
    if (!enableKeyboardNavigation) return;

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
      if (keyboardTimeoutRef.current) {
        clearTimeout(keyboardTimeoutRef.current);
      }
    };
  }, [enableKeyboardNavigation, handleKeyDown, handleMouseDown]);

  // Apply keyboard navigation class to body
  useEffect(() => {
    if (accessibilityState.isKeyboardNavigating) {
      document.body.classList.add('keyboard-navigation-active');
    } else {
      document.body.classList.remove('keyboard-navigation-active');
    }

    return () => {
      document.body.classList.remove('keyboard-navigation-active');
    };
  }, [accessibilityState.isKeyboardNavigating]);

  /**
   * Announce text to screen readers
   */
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!announceProgress || !announcementRef.current) return;

    const announcement = announcementRef.current;
    announcement.setAttribute('aria-live', priority);
    announcement.textContent = message;

    // Clear the announcement after a delay to allow for re-announcements
    setTimeout(() => {
      if (announcement) {
        announcement.textContent = '';
      }
    }, 1000);
  }, [announceProgress]);

  /**
   * Generate ARIA label for progress
   */
  const getProgressAriaLabel = useCallback((current: number, total: number, answered: number) => {
    return `Question ${current} of ${total}. ${answered} questions answered. ${total - answered} questions remaining.`;
  }, []);

  /**
   * Generate ARIA label for navigation buttons
   */
  const getNavigationAriaLabel = useCallback((direction: 'previous' | 'next', questionNumber: number, total: number) => {
    if (direction === 'previous') {
      return questionNumber > 1 
        ? `Go to previous question, question ${questionNumber - 1} of ${total}`
        : 'Previous question not available, currently on first question';
    } else {
      return questionNumber < total
        ? `Go to next question, question ${questionNumber + 1} of ${total}`
        : 'Next question not available, currently on last question';
    }
  }, []);

  /**
   * Generate ARIA label for question status
   */
  const getQuestionStatusAriaLabel = useCallback((questionNumber: number, isAnswered: boolean, questionType: string) => {
    const status = isAnswered ? 'answered' : 'unanswered';
    return `Question ${questionNumber}, ${questionType} question, ${status}`;
  }, []);

  /**
   * Get focus management utilities
   */
  const focusManagement = {
    /**
     * Focus the first focusable element in a container
     */
    focusFirst: (container: HTMLElement) => {
      const focusableElements = container.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      const firstElement = focusableElements[0] as HTMLElement;
      if (firstElement) {
        firstElement.focus();
      }
    },

    /**
     * Focus the last focusable element in a container
     */
    focusLast: (container: HTMLElement) => {
      const focusableElements = container.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
      if (lastElement) {
        lastElement.focus();
      }
    },

    /**
     * Trap focus within a container (for modals)
     */
    trapFocus: (container: HTMLElement, event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      const focusableElements = container.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      const firstElement = focusableElements[0] as HTMLElement;
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

      if (event.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement.focus();
        }
      }
    }
  };

  /**
   * Get CSS classes based on accessibility state
   */
  const getAccessibilityClasses = useCallback(() => {
    const classes = [];
    
    if (accessibilityState.isKeyboardNavigating) {
      classes.push('keyboard-navigation-active');
    }
    
    if (accessibilityState.prefersHighContrast) {
      classes.push('high-contrast-mode');
    }
    
    if (accessibilityState.prefersReducedMotion) {
      classes.push('reduced-motion-mode');
    }
    
    return classes.join(' ');
  }, [accessibilityState]);

  /**
   * Get props for announcement region element
   */
  const getAnnouncementRegionProps = () => ({
    ref: announcementRef,
    'aria-live': 'polite' as const,
    'aria-atomic': true,
    className: 'sr-only',
    role: 'status' as const
  });

  return {
    accessibilityState,
    announce,
    getProgressAriaLabel,
    getNavigationAriaLabel,
    getQuestionStatusAriaLabel,
    focusManagement,
    getAccessibilityClasses,
    getAnnouncementRegionProps
  };
};

/**
 * Hook for managing keyboard shortcuts in exam components
 */
export const useKeyboardShortcuts = (shortcuts: Record<string, () => void>) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs
      if (event.target instanceof HTMLInputElement || 
          event.target instanceof HTMLTextAreaElement) {
        return;
      }

      const key = event.key.toLowerCase();
      const modifiers = {
        ctrl: event.ctrlKey,
        alt: event.altKey,
        shift: event.shiftKey,
        meta: event.metaKey
      };

      // Create key combination string
      let keyCombo = '';
      if (modifiers.ctrl) keyCombo += 'ctrl+';
      if (modifiers.alt) keyCombo += 'alt+';
      if (modifiers.shift) keyCombo += 'shift+';
      if (modifiers.meta) keyCombo += 'meta+';
      keyCombo += key;

      // Check for matching shortcut
      if (shortcuts[keyCombo]) {
        event.preventDefault();
        shortcuts[keyCombo]();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts]);
};

/**
 * Hook for managing focus restoration
 */
export const useFocusRestore = () => {
  const previousFocusRef = useRef<HTMLElement | null>(null);

  const saveFocus = useCallback(() => {
    previousFocusRef.current = document.activeElement as HTMLElement;
  }, []);

  const restoreFocus = useCallback(() => {
    if (previousFocusRef.current && typeof previousFocusRef.current.focus === 'function') {
      previousFocusRef.current.focus();
    }
  }, []);

  return { saveFocus, restoreFocus };
};