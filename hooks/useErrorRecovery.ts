'use client';

import { useState, useCallback, useRef, useEffect } from 'react';

interface ErrorRecoveryOptions {
  maxRetries?: number;
  retryDelay?: number;
  exponentialBackoff?: boolean;
  onError?: (error: Error, retryCount: number) => void;
  onRecovery?: (retryCount: number) => void;
  onMaxRetriesReached?: (error: Error) => void;
}

interface ErrorRecoveryState {
  error: Error | null;
  isRetrying: boolean;
  retryCount: number;
  canRetry: boolean;
  lastRetryAt: number | null;
}

export const useErrorRecovery = (options: ErrorRecoveryOptions = {}) => {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    exponentialBackoff = true,
    onError,
    onRecovery,
    onMaxRetriesReached
  } = options;

  const [state, setState] = useState<ErrorRecoveryState>({
    error: null,
    isRetrying: false,
    retryCount: 0,
    canRetry: true,
    lastRetryAt: null
  });

  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const setError = useCallback((error: Error) => {
    setState(prev => ({
      ...prev,
      error,
      canRetry: prev.retryCount < maxRetries
    }));
    
    onError?.(error, state.retryCount);
    
    if (state.retryCount >= maxRetries) {
      onMaxRetriesReached?.(error);
    }
  }, [maxRetries, onError, onMaxRetriesReached, state.retryCount]);

  const clearError = useCallback(() => {
    setState({
      error: null,
      isRetrying: false,
      retryCount: 0,
      canRetry: true,
      lastRetryAt: null
    });
  }, []);

  const retry = useCallback(async (retryFn: () => Promise<void> | void) => {
    if (!state.canRetry || state.isRetrying) {
      return;
    }

    setState(prev => ({
      ...prev,
      isRetrying: true,
      lastRetryAt: Date.now()
    }));

    // Calculate delay with exponential backoff
    const delay = exponentialBackoff 
      ? retryDelay * Math.pow(2, state.retryCount)
      : retryDelay;

    try {
      // Create new abort controller for this retry attempt
      abortControllerRef.current = new AbortController();
      
      await new Promise(resolve => {
        retryTimeoutRef.current = setTimeout(resolve, delay);
      });

      // Check if component was unmounted or retry was cancelled
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      await retryFn();
      
      // Success - clear error state
      const finalRetryCount = state.retryCount + 1;
      setState({
        error: null,
        isRetrying: false,
        retryCount: 0,
        canRetry: true,
        lastRetryAt: null
      });
      
      onRecovery?.(finalRetryCount);
      
    } catch (error) {
      const newRetryCount = state.retryCount + 1;
      const canRetryAgain = newRetryCount < maxRetries;
      
      setState(prev => ({
        ...prev,
        error: error as Error,
        isRetrying: false,
        retryCount: newRetryCount,
        canRetry: canRetryAgain,
        lastRetryAt: Date.now()
      }));
      
      onError?.(error as Error, newRetryCount);
      
      if (!canRetryAgain) {
        onMaxRetriesReached?.(error as Error);
      }
    }
  }, [state, maxRetries, retryDelay, exponentialBackoff, onError, onRecovery, onMaxRetriesReached]);

  const executeWithRecovery = useCallback(async <T>(
    fn: () => Promise<T>,
    autoRetry: boolean = false
  ): Promise<T | null> => {
    try {
      clearError();
      const result = await fn();
      return result;
    } catch (error) {
      setError(error as Error);
      
      if (autoRetry && state.retryCount < maxRetries) {
        await retry(() => fn());
        return null;
      }
      
      throw error;
    }
  }, [clearError, setError, retry, state.retryCount, maxRetries]);

  return {
    // State
    error: state.error,
    isRetrying: state.isRetrying,
    retryCount: state.retryCount,
    canRetry: state.canRetry,
    lastRetryAt: state.lastRetryAt,
    
    // Actions
    setError,
    clearError,
    retry,
    executeWithRecovery,
    
    // Utilities
    getNextRetryDelay: () => exponentialBackoff 
      ? retryDelay * Math.pow(2, state.retryCount)
      : retryDelay,
    
    getRemainingRetries: () => Math.max(0, maxRetries - state.retryCount),
    
    getErrorSeverity: () => {
      if (!state.error) return 'none';
      if (state.retryCount >= maxRetries) return 'critical';
      if (state.retryCount >= maxRetries / 2) return 'high';
      return 'medium';
    }
  };
};

// Specialized hook for network-related errors
export const useNetworkErrorRecovery = (options: ErrorRecoveryOptions = {}) => {
  const [isOnline, setIsOnline] = useState(typeof navigator !== 'undefined' ? navigator.onLine : true);
  
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  const errorRecovery = useErrorRecovery({
    ...options,
    onError: (error, retryCount) => {
      // Log network-specific error details
      console.warn(`Network error (attempt ${retryCount}):`, {
        message: error.message,
        isOnline,
        timestamp: new Date().toISOString()
      });
      options.onError?.(error, retryCount);
    }
  });
  
  return {
    ...errorRecovery,
    isOnline,
    shouldRetry: errorRecovery.canRetry && isOnline
  };
};