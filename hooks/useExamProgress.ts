'use client';

import { useMemo } from 'react';
import { IExamResponse } from '@/types/exam.types';

export interface QuestionProgress {
  questionIndex: number;
  isAnswered: boolean;
  isComplete: boolean;
  timeSpent: number;
}

export interface ExamProgressData {
  totalQuestions: number;
  answeredQuestions: number;
  unansweredQuestions: number;
  completionPercentage: number;
  questionProgress: QuestionProgress[];
  isComplete: boolean;
}

export interface UseExamProgressProps {
  exam: IExamResponse;
  answers: Array<{
    questionIndex: number;
    userAnswer: string[];
  }>;
}

/**
 * Centralized hook for managing exam progress calculations
 * Eliminates duplication of progress logic across components
 */
export const useExamProgress = ({ exam, answers }: UseExamProgressProps): ExamProgressData => {

  // Calculate answered questions
  const answeredQuestions = useMemo(() => {
    if (!answers || !Array.isArray(answers)) return 0;

    return answers.filter(answer =>
      answer.userAnswer && answer.userAnswer.some(ans => ans && ans.trim() !== '')
    ).length;
  }, [JSON.stringify(answers)]);

  // Calculate total questions
  const totalQuestions = exam.questions.length;

  // Calculate unanswered questions
  const unansweredQuestions = totalQuestions - answeredQuestions;

  // Calculate completion percentage
  const completionPercentage = useMemo(() => {
    return totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;
  }, [answeredQuestions, totalQuestions]);

  // Calculate question progress for navigation
  const questionProgress = useMemo((): QuestionProgress[] => {
    return exam.questions.map((_, index) => {
      // Try to find answer by questionIndex first, then fall back to array index
      let answer = answers?.find(ans => ans.questionIndex === index);

      // If not found by questionIndex, try by array index (fallback)
      if (!answer && answers && answers[index]) {
        answer = answers[index];
      }

      const isAnswered = answer?.userAnswer && answer.userAnswer.some(ans => ans && ans.trim() !== '');

      return {
        questionIndex: index,
        isAnswered: Boolean(isAnswered),
        isComplete: Boolean(isAnswered),
        timeSpent: 0, // TODO: Implement time tracking if needed
      };
    });
  }, [exam.questions, JSON.stringify(answers)]);

  // Check if exam is complete (all questions answered)
  const isComplete = answeredQuestions === totalQuestions;

  return {
    totalQuestions,
    answeredQuestions,
    unansweredQuestions,
    completionPercentage,
    questionProgress,
    isComplete,
  };
};