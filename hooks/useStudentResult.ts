'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { getDetailedStudentResultAction } from '@/actions/exam.action';
import { IDetailedStudentResult } from '@/types/exam.types';
import { TApiError } from '@/apis/transformResponse';

const formatErrorMessage = (message: any): string => {
  if (typeof message === 'string') {
    return message;
  }
  if (Array.isArray(message)) {
    return message.map(err => err.constraints || 'Unknown error').join(', ');
  }
  return 'An unexpected error occurred.';
};

export function useStudentResult(examId: string, studentId: string) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [result, setResult] = useState<IDetailedStudentResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStudentResult = async () => {
      if (!examId || !studentId || status === 'loading') return;

      if (status === 'unauthenticated') {
        router.push('/auth/sign-in');
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await getDetailedStudentResultAction(examId, studentId);

        if (response.status === 'success') {
          if (response.data) {
            setResult(response.data);
          } else {
            setError('No detailed results found for this student.');
          }
        } else {
          setError(formatErrorMessage((response as TApiError).message) || 'Failed to load detailed student results');
        }
      } catch (err: any) {
        console.error('Error fetching detailed student result:', err);
        setError(err.message || 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchStudentResult();
  }, [examId, studentId, status, router]);

  return { result, loading, error };
}