'use client';

import { Session } from 'next-auth';
import { SessionProvider } from 'next-auth/react';
import { Provider as <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'jotai';
import { SubscriptionInitializer } from '@/stores/jotai/subscription/SubscriptionInitializer';
import { ToastContainer } from '@/stores/jotai/subscription/ToastContainer';

type Props = {
  children?: React.ReactNode;
  session: Session | null;
};

export const CustomProvider = ({ children, session }: Props) => (
  <SessionProvider
    session={session}
    basePath="/api/auth"
    baseUrl={process.env.BASE_URL}
  >
    <JotaiProvider>
      <SubscriptionInitializer>
        {children}
        <ToastContainer />
      </SubscriptionInitializer>
    </JotaiProvider>
  </SessionProvider>
);
