'use client';

import { useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useSubscription } from './useSubscription';

interface SubscriptionInitializerProps {
  children: React.ReactNode;
}

/**
 * SubscriptionInitializer component that automatically fetches subscription data
 * when a user is authenticated. This component should be placed high in the component
 * tree to ensure subscription data is available throughout the application.
 */
export const SubscriptionInitializer: React.FC<SubscriptionInitializerProps> = ({ children }) => {
  const { data: session, status } = useSession();
  const { refetchSubscription, clearAllData, isDataLoaded } = useSubscription();
  const hasInitialized = useRef(false);
  
  useEffect(() => {
    // Reset initialization flag when session status changes
    if (status === 'loading') {
      hasInitialized.current = false;
    }
  }, [status]);
  
  useEffect(() => {
    const initializeSubscriptionData = async () => {
      // Don't initialize if still loading or already initialized
      if (status === 'loading' || hasInitialized.current) {
        return;
      }
      
      if (session?.user?.accessToken) {
        // User is authenticated - fetch subscription data
        try {
          await refetchSubscription();
          hasInitialized.current = true;
        } catch (error) {
          console.error('Failed to initialize subscription data:', error);
          // Don't mark as initialized so it can retry
        }
      } else if (status === 'unauthenticated') {
        // User is not authenticated - clear any existing data
        clearAllData();
        hasInitialized.current = true;
      }
    };
    
    initializeSubscriptionData();
  }, [session?.user?.accessToken, status, refetchSubscription, clearAllData]);
  
  // Effect to handle session changes (login/logout)
  useEffect(() => {
    if (status === 'unauthenticated' && hasInitialized.current) {
      // User logged out - clear subscription data
      clearAllData();
    }
  }, [status, clearAllData]);
  
  return <>{children}</>;
}; 