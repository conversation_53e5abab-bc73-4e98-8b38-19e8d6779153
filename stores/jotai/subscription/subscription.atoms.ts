import { atom } from 'jotai';
import { ISubscriptionStatusResponse, IPackageResponse } from '@/apis/packageApi';
import { IUsageLimit } from '@/types/usage';

// Base atoms for storing subscription state
export const subscriptionStatusAtom = atom<ISubscriptionStatusResponse | null>(null);
export const packagesAtom = atom<IPackageResponse[] | null>(null);
export const subscriptionLoadingAtom = atom<boolean>(false);
export const subscriptionErrorAtom = atom<string | null>(null);

// Usage limits atoms
export const usageLimitsAtom = atom<IUsageLimit[] | null>(null);
export const usageLimitsLoadingAtom = atom<boolean>(false);
export const usageLimitsErrorAtom = atom<string | null>(null);

// Derived atom to check if user has an active subscription
export const isActiveSubscriptionAtom = atom((get) => {
  const subscriptionStatus = get(subscriptionStatusAtom);
  return subscriptionStatus?.status === 'active' || subscriptionStatus?.status === 'trialing';
});

// Derived atom to get current package details
export const currentPackageAtom = atom((get) => {
  const subscriptionStatus = get(subscriptionStatusAtom);
  return subscriptionStatus?.package || null;
});

// Derived atom to get maxQuestionsPerWorksheet usage limit
export const maxQuestionsPerWorksheetLimitAtom = atom((get) => {
  const usageLimits = get(usageLimitsAtom);
  return usageLimits?.find(limit => limit.feature === 'maxQuestionsPerWorksheet') || null;
});

// Derived atom to get maxWorksheets usage limit
export const maxWorksheetsLimitAtom = atom((get) => {
  const usageLimits = get(usageLimitsAtom);
  return usageLimits?.find(limit => limit.feature === 'maxWorksheets') || null;
});

// Derived atom to determine if user has question count limitations
// Free tier users are limited to 20 questions, paid users have unlimited
export const hasCustomQuestionLimitAtom = atom((get) => {
  const isActive = get(isActiveSubscriptionAtom);
  const currentPackage = get(currentPackageAtom);
  
  // If no active subscription, user is on free tier with limitations
  if (!isActive) {
    return true;
  }
  
  // If user has active subscription, check package tier
  // For now, assume all paid packages allow unlimited questions
  // This can be refined based on specific package features
  return false;
});

// Derived atom to get the maximum allowed question count for the current user
// Now uses the actual remaining count from API when available
export const maxAllowedQuestionsAtom = atom((get) => {
  const maxQuestionsLimit = get(maxQuestionsPerWorksheetLimitAtom);
  const hasLimit = get(hasCustomQuestionLimitAtom);
  
  // If we have usage limit data from API, use the remaining count
  if (maxQuestionsLimit) {
    return maxQuestionsLimit.remaining;
  }
  
  // Fallback to subscription tier-based logic when API data isn't available
  // Free tier users: limited to 20 questions
  // Premium users: unlimited (represented by a high number)
  return hasLimit ? 20 : 999;
});

// Derived atom for subscription loading state (useful for UI indicators)
export const subscriptionDataLoadedAtom = atom((get) => {
  const loading = get(subscriptionLoadingAtom);
  const subscriptionStatus = get(subscriptionStatusAtom);
  const packages = get(packagesAtom);
  
  return !loading && (subscriptionStatus !== null || packages !== null);
});

// Derived atom for usage limits loading state
export const usageLimitsDataLoadedAtom = atom((get) => {
  const loading = get(usageLimitsLoadingAtom);
  const usageLimits = get(usageLimitsAtom);
  
  return !loading && usageLimits !== null;
});

// Write-only atom for updating subscription status with error handling
export const updateSubscriptionStatusAtom = atom(
  null,
  (get, set, subscriptionStatus: ISubscriptionStatusResponse | null) => {
    set(subscriptionStatusAtom, subscriptionStatus);
    set(subscriptionErrorAtom, null); // Clear error when successfully updating
  }
);

// Write-only atom for updating packages with error handling
export const updatePackagesAtom = atom(
  null,
  (get, set, packages: IPackageResponse[] | null) => {
    set(packagesAtom, packages);
    set(subscriptionErrorAtom, null); // Clear error when successfully updating
  }
);

// Write-only atom for updating usage limits with error handling
export const updateUsageLimitsAtom = atom(
  null,
  (get, set, usageLimits: IUsageLimit[] | null) => {
    set(usageLimitsAtom, usageLimits);
    set(usageLimitsErrorAtom, null); // Clear error when successfully updating
  }
);

// Write-only atom for setting loading state
export const setSubscriptionLoadingAtom = atom(
  null,
  (get, set, loading: boolean) => {
    set(subscriptionLoadingAtom, loading);
  }
);

// Write-only atom for setting usage limits loading state
export const setUsageLimitsLoadingAtom = atom(
  null,
  (get, set, loading: boolean) => {
    set(usageLimitsLoadingAtom, loading);
  }
);

// Write-only atom for setting error state
export const setSubscriptionErrorAtom = atom(
  null,
  (get, set, error: string | null) => {
    set(subscriptionErrorAtom, error);
  }
);

// Write-only atom for setting usage limits error state
export const setUsageLimitsErrorAtom = atom(
  null,
  (get, set, error: string | null) => {
    set(usageLimitsErrorAtom, error);
  }
);

// Write-only atom for clearing all subscription data (useful for logout)
export const clearSubscriptionDataAtom = atom(
  null,
  (get, set) => {
    set(subscriptionStatusAtom, null);
    set(packagesAtom, null);
    set(subscriptionLoadingAtom, false);
    set(subscriptionErrorAtom, null);
  }
);

// Write-only atom for clearing all usage limits data
export const clearUsageLimitsDataAtom = atom(
  null,
  (get, set) => {
    set(usageLimitsAtom, null);
    set(usageLimitsLoadingAtom, false);
    set(usageLimitsErrorAtom, null);
  }
); 