import { atom } from 'jotai';

export interface ToastMessage {
  id: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number; // in milliseconds, default 4000
}

// Atom to store active toast messages
export const toastMessagesAtom = atom<ToastMessage[]>([]);

// Write-only atom to add a toast message
export const addToastAtom = atom(
  null,
  (get, set, toast: Omit<ToastMessage, 'id'>) => {
    const id = `toast-${Date.now()}-${Math.random()}`;
    const newToast: ToastMessage = {
      id,
      duration: 4000,
      ...toast,
    };
    
    set(toastMessagesAtom, (prev) => [...prev, newToast]);
    
    // Auto-remove toast after duration
    setTimeout(() => {
      set(removeToastAtom, id);
    }, newToast.duration);
  }
);

// Write-only atom to remove a toast message
export const removeToastAtom = atom(
  null,
  (get, set, toastId: string) => {
    set(toastMessagesAtom, (prev) => prev.filter(toast => toast.id !== toastId));
  }
);

// Write-only atom to clear all toasts
export const clearAllToastsAtom = atom(
  null,
  (get, set) => {
    set(toastMessagesAtom, []);
  }
); 