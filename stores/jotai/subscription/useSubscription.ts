import { use<PERSON>tom, useAtomValue, useSet<PERSON><PERSON> } from 'jotai';
import { useCallback } from 'react';
import { useSession } from 'next-auth/react';
import {
  subscriptionStatusAtom,
  packagesAtom,
  subscriptionLoadingAtom,
  subscriptionErrorAtom,
  isActiveSubscriptionAtom,
  currentPackageAtom,
  hasCustomQuestionLimitAtom,
  maxAllowedQuestionsAtom,
  subscriptionDataLoadedAtom,
  updateSubscriptionStatusAtom,
  updatePackagesAtom,
  setSubscriptionLoadingAtom,
  setSubscriptionErrorAtom,
  clearSubscriptionDataAtom,
  // New usage limits atoms
  usageLimitsAtom,
  usageLimitsLoadingAtom,
  usageLimitsErrorAtom,
  usageLimitsDataLoadedAtom,
  maxQuestionsPerWorksheetLimitAtom,
  maxWorksheetsLimitAtom,
  updateUsageLimitsAtom,
  setUsageLimitsLoadingAtom,
  setUsageLimitsErrorAtom,
  clearUsageLimitsDataAtom
} from './subscription.atoms';
import { 
  handleGetSubscriptionStatusAction, 
  handleGetPackagesPublicAction 
} from '@/actions/package.action';
import { handleGetUsageLimitsAction } from '@/actions/usage.action';

// Helper function to format error messages (copied from subscription page)
function formatErrorMessage(message: string | Array<{ field: string; constraints: string }>): string {
  if (typeof message === 'string') {
    return message;
  }
  if (Array.isArray(message)) {
    return message.map(error => `${error.field}: ${error.constraints}`).join(', ');
  }
  return 'An unexpected error occurred.';
}

export interface UseSubscriptionReturn {
  // Data
  subscriptionStatus: ReturnType<typeof useAtomValue<typeof subscriptionStatusAtom>>;
  packages: ReturnType<typeof useAtomValue<typeof packagesAtom>>;
  currentPackage: ReturnType<typeof useAtomValue<typeof currentPackageAtom>>;
  
  // Usage limits data
  usageLimits: ReturnType<typeof useAtomValue<typeof usageLimitsAtom>>;
  maxQuestionsPerWorksheetLimit: ReturnType<typeof useAtomValue<typeof maxQuestionsPerWorksheetLimitAtom>>;
  maxWorksheetsLimit: ReturnType<typeof useAtomValue<typeof maxWorksheetsLimitAtom>>;
  
  // Computed states
  isActiveSubscription: boolean;
  hasCustomQuestionLimit: boolean;
  maxAllowedQuestions: number;
  isDataLoaded: boolean;
  isUsageLimitsLoaded: boolean;
  
  // Loading and error states
  loading: boolean;
  error: string | null;
  usageLimitsLoading: boolean;
  usageLimitsError: string | null;
  
  // Actions
  refetchSubscription: () => Promise<void>;
  refetchUsageLimits: () => Promise<void>;
  refetchAll: () => Promise<void>;
  clearError: () => void;
  clearUsageLimitsError: () => void;
  clearAllData: () => void;
}

/**
 * Custom hook for managing subscription state using Jotai
 * Provides a clean interface for components to interact with subscription data
 */
export const useSubscription = (): UseSubscriptionReturn => {
  const { data: session } = useSession();
  
  // Read atoms
  const subscriptionStatus = useAtomValue(subscriptionStatusAtom);
  const packages = useAtomValue(packagesAtom);
  const currentPackage = useAtomValue(currentPackageAtom);
  const isActiveSubscription = useAtomValue(isActiveSubscriptionAtom);
  const hasCustomQuestionLimit = useAtomValue(hasCustomQuestionLimitAtom);
  const maxAllowedQuestions = useAtomValue(maxAllowedQuestionsAtom);
  const isDataLoaded = useAtomValue(subscriptionDataLoadedAtom);
  const loading = useAtomValue(subscriptionLoadingAtom);
  const error = useAtomValue(subscriptionErrorAtom);
  
  // Usage limits atoms
  const usageLimits = useAtomValue(usageLimitsAtom);
  const maxQuestionsPerWorksheetLimit = useAtomValue(maxQuestionsPerWorksheetLimitAtom);
  const maxWorksheetsLimit = useAtomValue(maxWorksheetsLimitAtom);
  const isUsageLimitsLoaded = useAtomValue(usageLimitsDataLoadedAtom);
  const usageLimitsLoading = useAtomValue(usageLimitsLoadingAtom);
  const usageLimitsError = useAtomValue(usageLimitsErrorAtom);
  
  // Write atoms
  const updateSubscriptionStatus = useSetAtom(updateSubscriptionStatusAtom);
  const updatePackages = useSetAtom(updatePackagesAtom);
  const setLoading = useSetAtom(setSubscriptionLoadingAtom);
  const setError = useSetAtom(setSubscriptionErrorAtom);
  const clearAllData = useSetAtom(clearSubscriptionDataAtom);
  
  // Usage limits write atoms
  const updateUsageLimits = useSetAtom(updateUsageLimitsAtom);
  const setUsageLimitsLoading = useSetAtom(setUsageLimitsLoadingAtom);
  const setUsageLimitsError = useSetAtom(setUsageLimitsErrorAtom);
  const clearUsageLimitsData = useSetAtom(clearUsageLimitsDataAtom);
  
  /**
   * Fetches subscription data from the API and updates atoms
   */
  const refetchSubscription = useCallback(async () => {
    if (!session?.user.accessToken) {
      setError('Authentication token is missing. Please log in again.');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      const [packagesResponse, subscriptionResponse] = await Promise.all([
        handleGetPackagesPublicAction(),
        handleGetSubscriptionStatusAction(session.user.accessToken)
      ]);
      
      // Handle packages response
      if (packagesResponse.status === 'error') {
        setError(formatErrorMessage(packagesResponse.message));
        return;
      }
      updatePackages(packagesResponse.data || []);
      
      // Handle subscription response
      if (subscriptionResponse.status === 'success' && subscriptionResponse.data) {
        updateSubscriptionStatus(subscriptionResponse.data);
      } else if (subscriptionResponse.status === 'error') {
        console.warn("Could not fetch subscription status:", subscriptionResponse.message);
        // Don't set error for subscription status as user might not have one
        updateSubscriptionStatus(null);
      } else {
        updateSubscriptionStatus(null);
      }
      
    } catch (err) {
      console.error('Error fetching subscription data:', err);
      setError('Failed to fetch subscription details. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [
    session?.user.accessToken,
    setLoading,
    setError,
    updatePackages,
    updateSubscriptionStatus
  ]);

  /**
   * Fetches usage limits data from the API and updates atoms
   */
  const refetchUsageLimits = useCallback(async () => {
    if (!session?.user.accessToken) {
      setUsageLimitsError('Authentication token is missing. Please log in again.');
      return;
    }
    
    try {
      setUsageLimitsLoading(true);
      setUsageLimitsError(null);
      
      const usageResponse = await handleGetUsageLimitsAction();
      
      if (usageResponse.status === 'success') {
        updateUsageLimits(usageResponse.data || []);
      } else {
        setUsageLimitsError(formatErrorMessage(usageResponse.message));
      }
      
    } catch (err) {
      console.error('Error fetching usage limits:', err);
      setUsageLimitsError('Failed to fetch usage limits. Please try again later.');
    } finally {
      setUsageLimitsLoading(false);
    }
  }, [
    session?.user.accessToken,
    setUsageLimitsLoading,
    setUsageLimitsError,
    updateUsageLimits
  ]);

  /**
   * Fetches both subscription and usage limits data
   */
  const refetchAll = useCallback(async () => {
    await Promise.all([
      refetchSubscription(),
      refetchUsageLimits()
    ]);
  }, [refetchSubscription, refetchUsageLimits]);
  
  /**
   * Clears the current error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, [setError]);

  /**
   * Clears the current usage limits error state
   */
  const clearUsageLimitsError = useCallback(() => {
    setUsageLimitsError(null);
  }, [setUsageLimitsError]);
  
  return {
    // Data
    subscriptionStatus,
    packages,
    currentPackage,
    
    // Usage limits data
    usageLimits,
    maxQuestionsPerWorksheetLimit,
    maxWorksheetsLimit,
    
    // Computed states
    isActiveSubscription,
    hasCustomQuestionLimit,
    maxAllowedQuestions,
    isDataLoaded,
    isUsageLimitsLoaded,
    
    // Loading and error states
    loading,
    error,
    usageLimitsLoading,
    usageLimitsError,
    
    // Actions
    refetchSubscription,
    refetchUsageLimits,
    refetchAll,
    clearError,
    clearUsageLimitsError,
    clearAllData
  };
}; 