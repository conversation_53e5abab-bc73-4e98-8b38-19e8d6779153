import { useSet<PERSON>tom } from 'jotai';
import { addToastAtom, ToastMessage } from './toast.atoms';

export interface UseToastReturn {
  showToast: (toast: Omit<ToastMessage, 'id'>) => void;
  showSuccess: (message: string, duration?: number) => void;
  showError: (message: string, duration?: number) => void;
  showWarning: (message: string, duration?: number) => void;
  showInfo: (message: string, duration?: number) => void;
}

/**
 * Custom hook for displaying toast notifications
 * Provides convenient methods for different toast types
 */
export const useToast = (): UseToastReturn => {
  const addToast = useSetAtom(addToastAtom);

  const showToast = (toast: Omit<ToastMessage, 'id'>) => {
    addToast(toast);
  };

  const showSuccess = (message: string, duration?: number) => {
    addToast({ message, type: 'success', duration });
  };

  const showError = (message: string, duration?: number) => {
    addToast({ message, type: 'error', duration });
  };

  const showWarning = (message: string, duration?: number) => {
    addToast({ message, type: 'warning', duration });
  };

  const showInfo = (message: string, duration?: number) => {
    addToast({ message, type: 'info', duration });
  };

  return {
    showToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  };
}; 