import { z } from 'zod';
import { requiredString } from '@/utils/zod';
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

/**
 * Schema for selectedOptions structure
 */
const selectedOptionSchema = z.object({
  key: z.string(),
  value: z.string(),
});

/**
 * Validation schema for individual question structure
 */
const questionSchema = z.object({
  type: z.string(),
  content: z.string(),
  options: z.array(z.string()),
  answer: z.array(z.string()),
  image: z.string().optional(),
  explain: z.string().optional(),
});

/**
 * Validation schema for creating an exam from a worksheet
 * Validates worksheetId, title, selectedOptions, and questions array
 */
export const createExamFromWorksheetSchema = z.object({
  worksheetId: requiredString.min(1, 'Worksheet ID is required'),
  title: requiredString.min(1, 'Exam title is required').max(200, 'Title is too long'),
  description: z.string().optional(),
  selectedOptions: z.array(selectedOptionSchema).min(2, 'Time limit and passing score are required'),
  questions: z.array(questionSchema).min(1, 'At least one question is required'),
});

// ============================================================================
// INTERFACES
// ============================================================================

/**
 * Interface for selected options structure  
 */
export interface ISelectedOption {
  key: string;
  value: string;
}

/**
 * Interface for exam question structure
 */
export interface IExamQuestion {
  type: string;
  content: string;
  options: string[];
  answer: string[];
  image?: string;
  explain?: string;
}

/**
 * Input interface for creating an exam from a worksheet
 */
export interface CreateExamFromWorksheetInput {
  worksheetId: string;
  title: string;
  description?: string;
  selectedOptions: ISelectedOption[];
  questions: IExamQuestion[];
}

/**
 * Response interface when creating an exam - matches backend API structure
 */
export interface ICreateExamResponse {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  userId: string;
  worksheetId: string;
  title: string;
  description: string | null;
  selectedOptions: any; // jsonb type from backend
  status: 'in_progress' | 'completed' | 'cancelled';
  questions: any; // jsonb type from backend
  maxAttempts: number;
  schoolId: string | null;
}

// ============================================================================
// LEGACY INTERFACES (for compatibility with existing components)
// ============================================================================

/**
 * Legacy exam response interface
 */
export interface IExamResponse {
  id: string;
  title: string;
  questions: IExamQuestion[];
  timeLimit?: number;
  passingScore?: number;
}

/**
 * Legacy exam submission payload
 */
export interface IExamSubmissionPayload {
  answers: IExamSubmissionAnswer[];
  timeSpent: number; // Time spent on exam in seconds
}

/**
 * Legacy exam submission answer
 */
export interface IExamSubmissionAnswer {
  questionIndex: number;
  userAnswer: string[];
}

/**
 * Legacy exam submission response
 */
export interface IExamSubmissionResponse {
  score: number;
  passed: boolean;
  totalQuestions: number;
  correctAnswers: number;
  detail: IExamQuestionResult[];
}

/**
 * Legacy exam question result
 */
export interface IExamQuestionResult {
  questionIndex: number;
  isCorrect: boolean;
  selectedAnswers: string[];
  correctAnswers: string[];
}

/**
 * Interface for a single exam in the student's assigned exam list
 */
export interface IExamListItem {
  id: string;
  title: string;
  description: string | null;
  status: 'in_progress' | 'completed' | 'cancelled';
  assignmentStatus: 'assigned' | 'in_progress' | 'completed';
  createdAt: string;
}

/**
 * Enhanced interface for exam list items with additional UI-specific properties
 */
export interface IEnhancedExamListItem extends IExamListItem {
  progress?: number; // 0-100 for in-progress exams
  duration?: number; // in minutes
  questionCount?: number;
  dueDate?: string;
}

/**
 * Interface for exam status configuration used in UI components
 */
export interface IExamStatusConfig {
  label: string;
  bgColor: string;
  cardBg: string;
  textColor: string;
  badgeColor: string;
  accentColor: string;
  buttonVariant: 'primary' | 'secondary';
  buttonText: string;
  buttonIcon: any; // LucideIcon type
  iconBg: string;
  iconColor: string;
  gradientFrom: string;
  gradientTo: string;
  shadowColor: string;
}

/**
 * Interface for ExamCard component props
 */
export interface IExamCardProps {
  exam: IEnhancedExamListItem;
  onTakeExam: (examId: string, assignmentStatus?: string) => void;
  animationDelay?: number;
}

/**
 * Interface for the response containing a paginated list of student assigned exams
 */
export interface IAssignedExamsResponse {
  items: IExamListItem[];
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

/**
 * Interface for a single exam in the teacher's list
 */
export interface ITeacherExam {
  id: string;
  title: string;
  worksheetTitle: string;
  createdAt: string;
  submissionCount: number;
  averageScore: number | null;
}

/**
 * Interface for the response containing a list of teacher exams
 */
export interface ITeacherExamsResponse {
  exams: ITeacherExam[];
  total: number;
  page: number;
  limit: number;
}

/**
 * Interface for teacher exams query params
 */
export interface ITeacherExamsQueryParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
  status?: 'active' | 'inactive' | 'all';
}

/**
 * Interface for a single student's result in an exam
 */
export interface IExamResultDetail {
  studentId: string;
  studentName: string;
  score: number;
  status: 'passed' | 'failed';
  submittedAt: string;
}

/**
 * Interface for the response containing exam results
 */
export interface IExamResultsResponse {
  examId: string;
  examTitle: string;
  results: IExamResultDetail[];
  assignments?: IExamAssignmentDetail[]; // Added assignment tracking
  total: number;
  page: number;
  limit: number;
  stats: {
    totalSubmissions: number;
    totalAssignments?: number; // Added total assignments count
    passRate: number;
    averageScore: number;
    highestScore: number;
    lowestScore: number;
    averageTimeSpent: number; // in seconds
  };
}

/**
 * Interface for exam results query params
 */
export interface IExamResultsQueryParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  minScore?: number;
  maxScore?: number;
  passed?: boolean;
  search?: string;
}


/**
 * Legacy detailed student result
 */
export interface IDetailedStudentResult {
  studentId: string;
  studentName: string;
  examTitle: string;
  score: number;
  passed: boolean;
  submittedAt: string;
  timeSpent: number; // in seconds
  totalQuestions: number;
  correctAnswers: number;
  questions: IExamQuestion[];
  answers: IExamAnswerDetail[];
}

// ============================================================================
// EXAM DETAIL API INTERFACES (Role-based responses)
// ============================================================================

/**
 * Interface for individual exam result (student submission)
 */
export interface IExamResult {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  examId: string;
  studentId: string;
  answers: any; // jsonb type from backend
  detail: any; // jsonb type from backend
  score: number;
  total: number;
  submittedAt: string;
  timeSpent?: number | null; // Time spent on exam in seconds (optional as it might be null)
}

/**
 * Base interface for exam detail response (common fields)
 */
export interface IExamDetailBase {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  userId: string;
  worksheetId: string;
  title: string;
  description: string | null;
  selectedOptions: any; // jsonb type from backend
  status: 'in_progress' | 'completed' | 'cancelled';
  questions: any; // jsonb type from backend
  maxAttempts: number;
  schoolId: string | null;
}

/**
 * Interface for exam detail response for teachers (includes all student results)
 */
export interface IExamDetailTeacherResponse extends IExamDetailBase {
  results: IExamResult[];
}

/**
 * Interface for exam detail response for students (includes user's specific result)
 */
export interface IExamDetailStudentResponse extends IExamDetailBase {
  userResult: IExamResult | null;
}

/**
 * Union type for exam detail response (role-based)
 */
export type IExamDetailResponse = IExamDetailTeacherResponse | IExamDetailStudentResponse;

/**
 * Legacy exam answer detail
 */
export interface IExamAnswerDetail {
  questionIndex: number;
  selectedAnswers: string[];
  correctAnswers: string[];
  isCorrect: boolean;
  points: number;
  maxPoints: number;
}

// ============================================================================
// STUDENT EXAM RESULT FOR TEACHER INTERFACES
// ============================================================================

/**
 * Interface for question result in student exam result for teacher
 * Matches the API specification for GET /exams/{examId}/results/{studentId}
 */
export interface IQuestionResult {
  questionIndex: number;
  content: string;
  type: 'single_choice' | 'multiple_choice' | 'fill_blank' | 'creative_writing';
  studentAnswer: string[];
  correctAnswer: string[];
  explanation?: string;
  isCorrect: boolean;
  score: number;
}

/**
 * Interface for student exam result for teacher view
 * Matches the API specification for GET /exams/{examId}/results/{studentId}
 */
export interface IStudentExamResultForTeacher {
  studentId: string;
  studentName: string;
  examId: string;
  examTitle: string;
  score: number;
  total: number;
  percentage: number;
  status: 'passed' | 'failed';
  timeSpent?: number; // seconds, nullable
  submittedAt: string;
  questions: IQuestionResult[];
}

// ============================================================================
// EXAM COMPLETION INTERFACES
// ============================================================================

/**
 * Interface for exam completion data displayed on the completion page
 */
export interface IExamCompletionData {
  examId: string;
  examTitle: string;
  submissionId: string;
  submissionTimestamp: Date;
  timeSpent: number; // Time spent in seconds
  timeLimit?: number; // Time limit in seconds
  totalQuestions: number;
  answeredQuestions: number;
  gradingType: 'auto' | 'manual';
  resultsAvailable: boolean;
  estimatedResultsDate?: Date;
  studentName?: string;
  score?: number; // Available for auto-graded exams
  passed?: boolean; // Available for auto-graded exams
  completionPercentage: number; // Percentage of questions answered
}

/**
 * Extended submission response that includes completion key for redirect
 */
export interface IExamSubmissionWithCompletionResponse extends IExamSubmissionResponse {
  completionKey?: string;
}

/**
 * Interface for exam assignment details
 * Matches the API specification for exam assignments
 */
export interface IExamAssignmentDetail {
  studentId: string;
  studentName: string;
  studentEmail: string;
  status: 'assigned' | 'in_progress' | 'completed';
  assignedAt: string;
  startedAt?: string;
  completedAt?: string;
  score?: number;
  feedback?: string;
}
