/**
 * TypeScript definitions for invitation functionality
 * Includes types for inviting students with automatic exam assignment
 */

/**
 * Valid user roles for invitations (matching API specification)
 */
export type InvitationUserRole = 'teacher' | 'admin' | 'student' | 'school_manager' | 'independent_teacher';

/**
 * Request payload for sending an invitation with exam assignment
 */
export interface ISendInvitationWithExamPayload {
  email: string;
  schoolId: string;
  role: InvitationUserRole; // Now required and uses proper enum values
  examIds: string[];
}

/**
 * Assignment response data from the invite with exam API
 */
export interface IExamAssignmentResponse {
  id: string;
  examId: string;
  studentId: string;
  status: string;
  score: number;
  assignedAt: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Complete response interface for invitation with exam assignment
 */
export interface IInvitationWithExamResponse {
  invitation: {
    id: string;
    email: string;
    schoolId: string;
    role: string;
    token: string;
    status: string;
    expiresAt: string;
    createdAt: string;
    updatedAt: string;
  };
  assignments: IExamAssignmentResponse[];
} 