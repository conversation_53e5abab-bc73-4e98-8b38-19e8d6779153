export type TUsageLimitFeature = 'maxWorksheets' | 'maxQuestionsPerWorksheet';
export type TUsageLimitPeriod = 'daily' | 'monthly' | 'yearly';

export interface IUsageLimit {
  usage: number;
  limit: number;
  remaining: number;
  withinLimit: boolean;
  period: TUsageLimitPeriod;
  feature: TUsageLimitFeature;
}

export interface IDailySummary {
  maxWorksheets: IUsageLimit;
  maxQuestionsPerWorksheet: IUsageLimit;
}

export interface GetDailySummaryResponse {
  success: boolean;
  data: {
    userId: string;
    dailySummary: IDailySummary;
  };
} 