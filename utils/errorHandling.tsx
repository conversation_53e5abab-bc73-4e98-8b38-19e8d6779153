'use client';

// Error classification and handling utilities

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  timestamp?: string;
  url?: string;
  userAgent?: string;
  additionalData?: Record<string, any>;
}

export interface ErrorReport {
  id: string;
  message: string;
  stack?: string;
  type: ErrorType;
  severity: ErrorSeverity;
  context: ErrorContext;
  recoverable: boolean;
  retryable: boolean;
}

export type ErrorType = 
  | 'network'
  | 'authentication'
  | 'authorization'
  | 'validation'
  | 'runtime'
  | 'chunk_load'
  | 'timeout'
  | 'unknown';

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

// Error classification based on error message and context
export const classifyError = (error: Error, context?: ErrorContext): { type: ErrorType; severity: ErrorSeverity; recoverable: boolean; retryable: boolean } => {
  const message = error.message.toLowerCase();
  const stack = error.stack?.toLowerCase() || '';
  
  // Network errors
  if (
    message.includes('fetch') ||
    message.includes('network') ||
    message.includes('connection') ||
    message.includes('timeout') ||
    message.includes('cors') ||
    error.name === 'NetworkError'
  ) {
    return {
      type: 'network',
      severity: 'medium',
      recoverable: true,
      retryable: true
    };
  }
  
  // Authentication errors
  if (
    message.includes('unauthorized') ||
    message.includes('401') ||
    message.includes('authentication') ||
    message.includes('token')
  ) {
    return {
      type: 'authentication',
      severity: 'high',
      recoverable: true,
      retryable: false
    };
  }
  
  // Authorization errors
  if (
    message.includes('forbidden') ||
    message.includes('403') ||
    message.includes('permission') ||
    message.includes('access denied')
  ) {
    return {
      type: 'authorization',
      severity: 'medium',
      recoverable: false,
      retryable: false
    };
  }
  
  // Validation errors
  if (
    message.includes('validation') ||
    message.includes('invalid') ||
    message.includes('required') ||
    message.includes('400') ||
    message.includes('bad request')
  ) {
    return {
      type: 'validation',
      severity: 'low',
      recoverable: true,
      retryable: false
    };
  }
  
  // Chunk loading errors (common in React apps)
  if (
    message.includes('chunk') ||
    message.includes('loading') ||
    stack.includes('chunk') ||
    error.name === 'ChunkLoadError'
  ) {
    return {
      type: 'chunk_load',
      severity: 'low',
      recoverable: true,
      retryable: true
    };
  }
  
  // Timeout errors
  if (
    message.includes('timeout') ||
    message.includes('aborted') ||
    error.name === 'TimeoutError'
  ) {
    return {
      type: 'timeout',
      severity: 'medium',
      recoverable: true,
      retryable: true
    };
  }
  
  // Runtime errors (potentially critical)
  if (
    message.includes('cannot read') ||
    message.includes('undefined') ||
    message.includes('null') ||
    error.name === 'TypeError' ||
    error.name === 'ReferenceError'
  ) {
    return {
      type: 'runtime',
      severity: 'high',
      recoverable: false,
      retryable: false
    };
  }
  
  // Default classification
  return {
    type: 'unknown',
    severity: 'medium',
    recoverable: true,
    retryable: true
  };
};

// Generate user-friendly error messages
export const getErrorMessage = (error: Error, type: ErrorType): { title: string; description: string; actionText: string } => {
  switch (type) {
    case 'network':
      return {
        title: 'Connection Problem',
        description: 'Unable to connect to the server. Please check your internet connection and try again.',
        actionText: 'Retry Connection'
      };
    
    case 'authentication':
      return {
        title: 'Authentication Required',
        description: 'Your session has expired. Please sign in again to continue.',
        actionText: 'Sign In'
      };
    
    case 'authorization':
      return {
        title: 'Access Denied',
        description: 'You don\'t have permission to access this resource. Please contact your administrator.',
        actionText: 'Contact Support'
      };
    
    case 'validation':
      return {
        title: 'Invalid Input',
        description: 'Please check your input and try again.',
        actionText: 'Fix Input'
      };
    
    case 'chunk_load':
      return {
        title: 'Loading Issue',
        description: 'There was a problem loading part of the application. Please refresh the page.',
        actionText: 'Refresh Page'
      };
    
    case 'timeout':
      return {
        title: 'Request Timeout',
        description: 'The request took too long to complete. Please try again.',
        actionText: 'Try Again'
      };
    
    case 'runtime':
      return {
        title: 'Application Error',
        description: 'An unexpected error occurred in the application. Please refresh the page or contact support.',
        actionText: 'Refresh Page'
      };
    
    default:
      return {
        title: 'Something Went Wrong',
        description: 'An unexpected error occurred. Please try again or contact support if the problem persists.',
        actionText: 'Try Again'
      };
  }
};

// Create error report for logging/monitoring
export const createErrorReport = (error: Error, context?: ErrorContext): ErrorReport => {
  const classification = classifyError(error, context);
  const errorId = `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  return {
    id: errorId,
    message: error.message,
    stack: error.stack,
    type: classification.type,
    severity: classification.severity,
    recoverable: classification.recoverable,
    retryable: classification.retryable,
    context: {
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : '',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
      ...context
    }
  };
};

// Error reporting service (placeholder for actual implementation)
export const reportError = async (errorReport: ErrorReport): Promise<void> => {
  try {
    // In a real application, this would send to your error monitoring service
    // Examples: Sentry, LogRocket, Bugsnag, etc.
    
    console.group(`🚨 Error Report: ${errorReport.id}`);
    console.error('Message:', errorReport.message);
    console.error('Type:', errorReport.type);
    console.error('Severity:', errorReport.severity);
    console.error('Context:', errorReport.context);
    if (errorReport.stack) {
      console.error('Stack:', errorReport.stack);
    }
    console.groupEnd();
    
    // Example API call (commented out)
    /*
    await fetch('/api/errors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(errorReport),
    });
    */
    
  } catch (reportingError) {
    console.error('Failed to report error:', reportingError);
  }
};

// Graceful error handler for async operations
export const handleAsyncError = async <T>(
  operation: () => Promise<T>,
  context?: ErrorContext,
  options?: {
    retries?: number;
    retryDelay?: number;
    onError?: (error: Error, attempt: number) => void;
    onSuccess?: (result: T, attempts: number) => void;
  }
): Promise<T | null> => {
  const { retries = 0, retryDelay = 1000, onError, onSuccess } = options || {};
  let lastError: Error | null = null;
  
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const result = await operation();
      onSuccess?.(result, attempt + 1);
      return result;
    } catch (error) {
      lastError = error as Error;
      onError?.(lastError, attempt + 1);
      
      // Don't retry on the last attempt
      if (attempt < retries) {
        const classification = classifyError(lastError, context);
        
        // Only retry if the error is retryable
        if (classification.retryable) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)));
          continue;
        }
      }
      
      // Report the error
      const errorReport = createErrorReport(lastError, context);
      await reportError(errorReport);
      
      break;
    }
  }
  
  return null;
};

// React error boundary helper
export const createErrorBoundaryFallback = (
  level: 'page' | 'component' | 'critical' = 'component',
  context?: string
) => {
  return ({ error, retry }: { error: Error; retry?: () => void }) => {
    const classification = classifyError(error);
    const errorMessage = getErrorMessage(error, classification.type);
    
    if (level === 'page') {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
          <div className="max-w-md w-full text-center">
            <div className="mb-6">
              <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{errorMessage.title}</h1>
              <p className="text-gray-600">{errorMessage.description}</p>
            </div>
            {retry && classification.retryable && (
              <button
                onClick={retry}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                {errorMessage.actionText}
              </button>
            )}
          </div>
        </div>
      );
    }
    
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-start gap-3">
          <svg className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-red-800">{errorMessage.title}</h3>
            <p className="text-sm text-red-700 mt-1">{errorMessage.description}</p>
            {context && (
              <p className="text-xs text-red-600 mt-1">Component: {context}</p>
            )}
          </div>
          {retry && classification.retryable && (
            <button
              onClick={retry}
              className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded hover:bg-red-200 transition-colors"
            >
              {errorMessage.actionText}
            </button>
          )}
        </div>
      </div>
    );
  };
};

// Global error handler setup
export const setupGlobalErrorHandling = () => {
  if (typeof window === 'undefined') return;
  
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));
    const errorReport = createErrorReport(error, {
      component: 'Global',
      action: 'unhandledrejection'
    });
    reportError(errorReport);
  });
  
  // Handle global errors
  window.addEventListener('error', (event) => {
    const error = event.error instanceof Error ? event.error : new Error(event.message);
    const errorReport = createErrorReport(error, {
      component: 'Global',
      action: 'error',
      additionalData: {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      }
    });
    reportError(errorReport);
  });
};