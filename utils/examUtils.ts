/**
 * Utility functions for extracting and processing exam data
 */

import { IExamApiResponse } from '@/apis/exam';

/**
 * Interface for exam settings extracted from selectedOptions
 */
export interface IExamSettings {
  timeLimit?: number; // in minutes
  passingScore?: number; // percentage
  maxAttempts?: number;
  shuffleQuestions?: boolean;
  showResults?: boolean;
}

/**
 * Interface for selected option structure
 */
interface ISelectedOption {
  key: string;
  value: string;
}

/**
 * Extracts exam settings from the selectedOptions field
 * @param selectedOptions - The selectedOptions array from exam data
 * @returns Parsed exam settings object
 */
export const extractExamSettings = (selectedOptions: any): IExamSettings => {
  const settings: IExamSettings = {};

  if (!selectedOptions || !Array.isArray(selectedOptions)) {
    return settings;
  }

  const options = selectedOptions as ISelectedOption[];

  options.forEach((option) => {
    switch (option.key) {
      case 'timeLimit':
        const timeLimit = parseInt(option.value, 10);
        if (!isNaN(timeLimit) && timeLimit > 0) {
          settings.timeLimit = timeLimit;
        }
        break;
      
      case 'passingScore':
        const passingScore = parseInt(option.value, 10);
        if (!isNaN(passingScore) && passingScore >= 0 && passingScore <= 100) {
          settings.passingScore = passingScore;
        }
        break;
      
      case 'maxAttempts':
        const maxAttempts = parseInt(option.value, 10);
        if (!isNaN(maxAttempts) && maxAttempts > 0) {
          settings.maxAttempts = maxAttempts;
        }
        break;
      
      case 'shuffleQuestions':
        settings.shuffleQuestions = option.value === 'true' || option.value === '1';
        break;
      
      case 'showResults':
        settings.showResults = option.value === 'true' || option.value === '1';
        break;
    }
  });

  return settings;
};

/**
 * Formats time limit for display
 * @param timeLimit - Time limit in minutes
 * @returns Formatted time string
 */
export const formatTimeLimit = (timeLimit?: number): string => {
  if (!timeLimit || timeLimit <= 0) {
    return 'No limit';
  }

  if (timeLimit < 60) {
    return `${timeLimit} min`;
  }

  const hours = Math.floor(timeLimit / 60);
  const minutes = timeLimit % 60;

  if (minutes === 0) {
    return `${hours}h`;
  }

  return `${hours}h ${minutes}m`;
};

/**
 * Formats passing score for display
 * @param passingScore - Passing score percentage
 * @returns Formatted score string
 */
export const formatPassingScore = (passingScore?: number): string => {
  if (passingScore === undefined || passingScore < 0) {
    return 'Not set';
  }
  return `${passingScore}%`;
};

/**
 * Gets exam status badge styling
 * @param status - Exam status
 * @returns CSS classes for status badge
 */
export const getExamStatusBadge = (status: string): string => {
  const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';
  
  switch (status?.toLowerCase()) {
    case 'in_progress':
      return `${baseClasses} bg-blue-100 text-blue-800`;
    case 'completed':
      return `${baseClasses} bg-green-100 text-green-800`;
    case 'cancelled':
      return `${baseClasses} bg-red-100 text-red-800`;
    default:
      return `${baseClasses} bg-gray-100 text-gray-800`;
  }
};

/**
 * Formats exam status for display
 * @param status - Exam status
 * @returns Human-readable status string
 */
export const formatExamStatus = (status: string): string => {
  switch (status?.toLowerCase()) {
    case 'in_progress':
      return 'Active';
    case 'completed':
      return 'Completed';
    case 'cancelled':
      return 'Cancelled';
    default:
      return status || 'Unknown';
  }
};

/**
 * Formats assignment status for display with proper capitalization
 * @param status - Assignment status
 * @returns Human-readable status string with proper capitalization
 */
export const formatAssignmentStatus = (status: string): string => {
  switch (status?.toLowerCase()) {
    case 'assigned':
      return 'Assigned';
    case 'in_progress':
      return 'In Progress';
    case 'completed':
      return 'Completed';
    case 'cancelled':
      return 'Cancelled';
    case 'pending':
      return 'Pending';
    case 'active':
      return 'Active';
    case 'inactive':
      return 'Inactive';
    case 'draft':
      return 'Draft';
    case 'published':
      return 'Published';
    case 'failed':
      return 'Failed';
    case 'passed':
      return 'Passed';
    default:
      // Fallback: capitalize first letter and replace underscores with spaces
      return status ? status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Unknown';
  }
};

/**
 * Gets assignment status badge styling with semantic colors
 * @param status - Assignment status
 * @returns CSS classes for status badge
 */
export const getAssignmentStatusBadge = (status: string): string => {
  const baseClasses = 'inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-semibold border';
  
  switch (status?.toLowerCase()) {
    case 'completed':
      return `${baseClasses} bg-green-50 text-green-700 border-green-200`;
    case 'in_progress':
      return `${baseClasses} bg-blue-50 text-blue-700 border-blue-200`;
    case 'assigned':
      return `${baseClasses} bg-section-bg-neutral-alt text-text-secondary border-gray-200`;
    case 'pending':
      return `${baseClasses} bg-yellow-50 text-yellow-700 border-yellow-200`;
    case 'cancelled':
    case 'failed':
      return `${baseClasses} bg-red-50 text-red-700 border-red-200`;
    case 'passed':
    case 'active':
      return `${baseClasses} bg-green-50 text-green-700 border-green-200`;
    case 'inactive':
    case 'draft':
      return `${baseClasses} bg-section-bg-neutral-alt text-text-secondary border-gray-200`;
    default:
      return `${baseClasses} bg-section-bg-neutral-alt text-text-primary border-gray-200`;
  }
};

/**
 * Extracts question count from exam questions
 * @param questions - Questions array from exam data
 * @returns Number of questions
 */
export const getQuestionCount = (questions: any): number => {
  if (!questions) return 0;
  
  if (Array.isArray(questions)) {
    return questions.length;
  }
  
  // If questions is stored as JSON string
  if (typeof questions === 'string') {
    try {
      const parsed = JSON.parse(questions);
      return Array.isArray(parsed) ? parsed.length : 0;
    } catch {
      return 0;
    }
  }
  
  return 0;
};

/**
 * Formats date for display in exam table
 * @param dateString - ISO date string
 * @returns Formatted date string
 */
export const formatExamDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch {
    return 'Invalid date';
  }
};

/**
 * Gets worksheet title from worksheetId (placeholder for now)
 * In a real implementation, this might fetch worksheet details
 * @param worksheetId - Worksheet ID
 * @returns Worksheet display name
 */
export const getWorksheetDisplayName = (worksheetId: string): string => {
  if (!worksheetId) return 'N/A';
  
  // For now, just return the ID
  // In the future, this could be enhanced to fetch actual worksheet titles
  return worksheetId;
};

/**
 * Determines if exam data has sufficient information for display
 * @param exam - Exam data object
 * @returns Boolean indicating if exam has required fields
 */
export const isValidExamData = (exam: IExamApiResponse): boolean => {
  return !!(exam.id && exam.title && exam.createdAt);
};
