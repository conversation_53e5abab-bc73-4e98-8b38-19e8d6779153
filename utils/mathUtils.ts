/**
 * Utility functions for handling mathematical content and MathML
 */

/**
 * Checks if a string contains MathML content
 */
export const containsMathML = (content: string): boolean => {
  return /<math[\s>]/.test(content);
};

/**
 * Splits content into parts, separating MathML from regular HTML
 */
export interface ContentPart {
  type: 'text' | 'mathml';
  content: string;
  index: number;
}

export const parseContentWithMath = (content: string): ContentPart[] => {
  const parts: ContentPart[] = [];
  let currentIndex = 0;
  
  // Regular expression to match MathML elements
  const mathMLRegex = /<math[^>]*>[\s\S]*?<\/math>/gi;
  let match;
  let lastIndex = 0;
  
  while ((match = mathMLRegex.exec(content)) !== null) {
    // Add text content before the MathML
    if (match.index > lastIndex) {
      const textContent = content.substring(lastIndex, match.index);
      if (textContent.trim()) {
        parts.push({
          type: 'text',
          content: textContent,
          index: currentIndex++
        });
      }
    }
    
    // Add the MathML content
    parts.push({
      type: 'mathml',
      content: match[0],
      index: currentIndex++
    });
    
    lastIndex = match.index + match[0].length;
  }
  
  // Add any remaining text content
  if (lastIndex < content.length) {
    const textContent = content.substring(lastIndex);
    if (textContent.trim()) {
      parts.push({
        type: 'text',
        content: textContent,
        index: currentIndex++
      });
    }
  }
  
  // If no MathML was found, return the entire content as text
  if (parts.length === 0) {
    parts.push({
      type: 'text',
      content: content,
      index: 0
    });
  }
  
  return parts;
};

/**
 * Validates MathML syntax
 */
export const validateMathML = (mathmlContent: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  try {
    // Basic validation checks
    if (!mathmlContent.includes('<math')) {
      errors.push('No MathML root element found');
    }
    
    // Check for proper opening and closing tags
    const openTags = (mathmlContent.match(/<math[^>]*>/g) || []).length;
    const closeTags = (mathmlContent.match(/<\/math>/g) || []).length;
    
    if (openTags !== closeTags) {
      errors.push('Mismatched opening and closing math tags');
    }
    
    // Check for common MathML elements
    const commonElements = ['mi', 'mo', 'mn', 'mrow', 'mfrac', 'msup', 'msub', 'msqrt'];
    const hasValidElements = commonElements.some(element => 
      mathmlContent.includes(`<${element}`) || mathmlContent.includes(`</${element}>`)
    );
    
    if (!hasValidElements && mathmlContent.includes('<math')) {
      errors.push('No valid MathML elements found inside math tags');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  } catch (error) {
    errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return {
      isValid: false,
      errors
    };
  }
};

/**
 * Cleans and normalizes MathML content
 */
export const normalizeMathML = (content: string): string => {
  let normalized = content;
  
  // Ensure proper namespace
  normalized = normalized.replace(
    /<math(?!\s[^>]*xmlns)/gi,
    '<math xmlns="http://www.w3.org/1998/Math/MathML"'
  );
  
  // Clean up common encoding issues
  normalized = normalized.replace(/&amp;#x([0-9A-Fa-f]+);/g, '&#x$1;');
  normalized = normalized.replace(/&amp;([a-zA-Z]+);/g, '&$1;');
  
  // Remove extra whitespace between elements
  normalized = normalized.replace(/>\s+</g, '><');
  
  return normalized;
};

/**
 * Extracts plain text representation from MathML
 */
export const mathMLToText = (mathmlContent: string): string => {
  try {
    // Create a temporary DOM element to parse the MathML
    const parser = new DOMParser();
    const doc = parser.parseFromString(mathmlContent, 'text/xml');
    
    if (doc.documentElement.nodeName === 'parsererror') {
      // Fallback: strip tags and return text content
      return mathmlContent.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
    }
    
    const mathElement = doc.querySelector('math');
    if (!mathElement) {
      return mathmlContent.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
    }
    
    return extractMathText(mathElement);
  } catch (error) {
    console.warn('Error converting MathML to text:', error);
    return mathmlContent.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
  }
};

/**
 * Helper function to extract readable text from MathML elements
 */
const extractMathText = (element: Element): string => {
  const textParts: string[] = [];
  
  const processNode = (node: Node) => {
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent?.trim();
      if (text) textParts.push(text);
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const el = node as Element;
      const tagName = el.tagName.toLowerCase();
      
      switch (tagName) {
        case 'mo': // Operators
          const op = el.textContent?.trim();
          if (op) textParts.push(` ${op} `);
          break;
        case 'mfrac': // Fractions
          const children = Array.from(el.children);
          if (children.length >= 2) {
            const num = children[0].textContent?.trim();
            const den = children[1].textContent?.trim();
            if (num && den) {
              textParts.push(`(${num})/(${den})`);
            }
          }
          break;
        case 'msup': // Superscripts
          const supChildren = Array.from(el.children);
          if (supChildren.length >= 2) {
            const base = supChildren[0].textContent?.trim();
            const sup = supChildren[1].textContent?.trim();
            if (base && sup) {
              textParts.push(`${base}^${sup}`);
            }
          }
          break;
        case 'msub': // Subscripts
          const subChildren = Array.from(el.children);
          if (subChildren.length >= 2) {
            const base = subChildren[0].textContent?.trim();
            const sub = subChildren[1].textContent?.trim();
            if (base && sub) {
              textParts.push(`${base}_${sub}`);
            }
          }
          break;
        case 'msqrt': // Square roots
          const content = el.textContent?.trim();
          if (content) {
            textParts.push(`√(${content})`);
          }
          break;
        case 'mroot': // Nth roots
          const rootChildren = Array.from(el.children);
          if (rootChildren.length >= 2) {
            const radicand = rootChildren[0].textContent?.trim();
            const index = rootChildren[1].textContent?.trim();
            if (radicand && index) {
              textParts.push(`${index}√(${radicand})`);
            }
          }
          break;
        default:
          // For other elements, process children recursively
          Array.from(el.childNodes).forEach(processNode);
          break;
      }
    }
  };
  
  Array.from(element.childNodes).forEach(processNode);
  return textParts.join('').replace(/\s+/g, ' ').trim();
};