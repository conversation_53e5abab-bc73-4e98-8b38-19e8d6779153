/**
 * Responsive Layout Utilities for Exam Detail UI
 * Provides consistent breakpoint management and responsive behavior
 */

// Breakpoint definitions following Tailwind CSS defaults
export const BREAKPOINTS = {
  xs: 375,   // Extra small devices (small phones)
  sm: 640,   // Small devices (phones)
  md: 768,   // Medium devices (tablets)
  lg: 1024,  // Large devices (desktops)
  xl: 1280,  // Extra large devices (large desktops)
  '2xl': 1536, // 2X large devices (larger desktops)
} as const;

export type Breakpoint = keyof typeof BREAKPOINTS;

/**
 * Hook to get current viewport size and breakpoint
 */
export const useViewport = () => {
  const [viewport, setViewport] = React.useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  React.useEffect(() => {
    // Early return if running on server
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      setViewport({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const getCurrentBreakpoint = (): Breakpoint => {
    const { width } = viewport;
    if (width >= BREAKPOINTS['2xl']) return '2xl';
    if (width >= BREAKPOINTS.xl) return 'xl';
    if (width >= BREAKPOINTS.lg) return 'lg';
    if (width >= BREAKPOINTS.md) return 'md';
    if (width >= BREAKPOINTS.sm) return 'sm';
    return 'xs';
  };

  return {
    ...viewport,
    isMobile: viewport.width < BREAKPOINTS.md,
    isTablet: viewport.width >= BREAKPOINTS.md && viewport.width < BREAKPOINTS.lg,
    isDesktop: viewport.width >= BREAKPOINTS.lg,
    currentBreakpoint: getCurrentBreakpoint(),
  };
};

/**
 * Responsive class name utilities
 */
export const responsiveClasses = {
  // Container classes for different layouts
  container: {
    mobile: 'px-4 py-3',
    tablet: 'px-6 py-4',
    desktop: 'px-8 py-6',
    responsive: 'px-4 py-3 md:px-6 md:py-4 lg:px-8 lg:py-6',
  },
  
  // Grid layouts
  grid: {
    examLayout: {
      mobile: 'grid grid-cols-1 gap-4',
      tablet: 'grid grid-cols-1 gap-6',
      desktop: 'grid grid-cols-[280px_1fr] gap-8',
      responsive: 'grid grid-cols-1 gap-4 md:gap-6 lg:grid-cols-[280px_1fr] lg:gap-8',
    },
    questionGrid: {
      mobile: 'grid grid-cols-1 gap-3',
      tablet: 'grid grid-cols-1 gap-4',
      desktop: 'grid grid-cols-1 gap-6',
      responsive: 'grid grid-cols-1 gap-3 md:gap-4 lg:gap-6',
    },
  },
  
  // Typography scaling
  text: {
    examTitle: 'text-xl sm:text-2xl lg:text-3xl',
    questionTitle: 'text-lg sm:text-xl lg:text-2xl',
    questionContent: 'text-sm sm:text-base lg:text-lg',
    metadata: 'text-xs sm:text-sm',
    navigation: 'text-sm sm:text-base',
  },
  
  // Spacing utilities
  spacing: {
    cardPadding: 'p-4 sm:p-5 lg:p-6',
    sectionGap: 'space-y-4 sm:space-y-5 lg:space-y-6',
    buttonGap: 'gap-2 sm:gap-3 lg:gap-4',
  },
  
  // Touch-optimized sizes
  touch: {
    button: 'min-h-[44px] min-w-[44px] sm:min-h-[48px] sm:min-w-[48px]',
    navButton: 'h-10 w-10 sm:h-12 sm:w-12 lg:h-10 lg:w-10',
    input: 'min-h-[44px] sm:min-h-[48px]',
  },
  
  // Layout visibility
  visibility: {
    mobileOnly: 'block md:hidden',
    tabletOnly: 'hidden md:block lg:hidden',
    desktopOnly: 'hidden lg:block',
    mobileTablet: 'block lg:hidden',
    tabletDesktop: 'hidden md:block',
  },
};

/**
 * Touch interaction utilities
 */
export const touchUtils = {
  // Check if device supports touch
  isTouchDevice: () => {
    if (typeof window === 'undefined') return false;
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  },
  
  // Get optimal touch target size based on device
  getTouchTargetSize: (breakpoint: Breakpoint) => {
    switch (breakpoint) {
      case 'xs':
      case 'sm':
        return 'min-w-[48px]'; // Larger for small screens
      case 'md':
        return 'min-w-[44px]'; // Standard tablet size
      default:
        return 'min-w-[40px]'; // Desktop can be smaller
    }
  },
  
  // Touch-optimized interaction classes
  touchClasses: {
    button: 'active:scale-95 transition-transform duration-100 ease-out',
    card: 'active:scale-[0.98] transition-transform duration-150 ease-out',
    nav: 'active:scale-90 transition-transform duration-100 ease-out',
  },
};

/**
 * Layout configuration for different screen sizes
 */
export const layoutConfig = {
  exam: {
    mobile: {
      showSidebar: false,
      sidebarCollapsed: true,
      bottomNavHeight: '80px',
      contentPadding: 'pb-24',
      headerHeight: 'auto',
      questionSpacing: 'space-y-4',
    },
    tablet: {
      showSidebar: false,
      sidebarCollapsed: true,
      bottomNavHeight: '72px',
      contentPadding: 'pb-20',
      headerHeight: 'auto',
      questionSpacing: 'space-y-5',
    },
    desktop: {
      showSidebar: true,
      sidebarCollapsed: false,
      bottomNavHeight: '0px',
      contentPadding: 'pb-8',
      headerHeight: 'auto',
      questionSpacing: 'space-y-6',
    },
  },
  
  navigation: {
    mobile: {
      type: 'bottom',
      showOverview: true,
      showProgress: true,
      buttonSize: 'large',
    },
    tablet: {
      type: 'bottom',
      showOverview: true,
      showProgress: true,
      buttonSize: 'medium',
    },
    desktop: {
      type: 'sidebar',
      showOverview: false,
      showProgress: true,
      buttonSize: 'medium',
    },
  },
};

/**
 * Animation configurations for different devices
 */
export const animationConfig = {
  // Reduced animations for mobile to preserve battery
  mobile: {
    duration: 'duration-200',
    easing: 'ease-out',
    reduceMotion: true,
  },
  tablet: {
    duration: 'duration-250',
    easing: 'ease-out',
    reduceMotion: false,
  },
  desktop: {
    duration: 'duration-300',
    easing: 'ease-out',
    reduceMotion: false,
  },
};

/**
 * Get responsive configuration based on current viewport
 */
export const getResponsiveConfig = (breakpoint: Breakpoint) => {
  const isMobile = breakpoint === 'xs' || breakpoint === 'sm';
  const isTablet = breakpoint === 'md';
  
  return {
    layout: isMobile ? layoutConfig.exam.mobile : 
            isTablet ? layoutConfig.exam.tablet : 
            layoutConfig.exam.desktop,
    navigation: isMobile ? layoutConfig.navigation.mobile :
                isTablet ? layoutConfig.navigation.tablet :
                layoutConfig.navigation.desktop,
    animation: isMobile ? animationConfig.mobile :
               isTablet ? animationConfig.tablet :
               animationConfig.desktop,
    classes: {
      container: responsiveClasses.container.responsive,
      grid: responsiveClasses.grid.examLayout.responsive,
      text: responsiveClasses.text,
      spacing: responsiveClasses.spacing,
      touch: responsiveClasses.touch,
    },
  };
};

// React import for the hook
import React from 'react';